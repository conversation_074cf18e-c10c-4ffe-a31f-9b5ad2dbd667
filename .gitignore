venv/

# sphinx build directories
_build/

# dotfiles
.*
!.dockerignore
!.gitignore
!.github
!.mailmap
!.cursor
!.augment-guidelines
!odoo
# compiled python files
*.py[co]
__pycache__/
# setup.py egg_info
*.egg-info
# emacs backup files
*~
# hg stuff
*.orig
status
# odoo filestore
odoo/filestore
# maintenance migration scripts
odoo/addons/base/maintenance

# generated for windows installer?
install/win32/*.bat
install/win32/meta.py

# needed only when building for win32
setup/win32/static/less/
setup/win32/static/wkhtmltopdf/
setup/win32/static/postgresql*.exe

# js tooling
node_modules
jsconfig.json
tsconfig.json
package-lock.json
package.json
.husky

# various virtualenv
/bin/
/build/
/dist/
/include/
/lib/
/man/
/share/
/src/

# editor files
!.editorconfig
!.gitmodules
/*.sql

/odoo/
odoo.conf
*.Identifier