# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_attendance
# 
# Translators:
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "\"Check Out\" time cannot be earlier than \"Check In\" time."
msgstr "「チェックアウト」時間は「チェックイン」時間より早くすることはできません。"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "%(empl_name)s from %(check_in)s"
msgstr "%(empl_name)s %(check_in)s から"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "%(empl_name)s from %(check_in)s to %(check_out)s"
msgstr "%(empl_name)s %(check_in)s から %(check_out)s まで"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid ": Your user should be linked to an employee to use attendance."
msgstr ": 勤怠管理を利用するためには、ユーザが従業員にリンクされている必要があります。"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid ""
"<b>Warning! Last check in was over 12 hours ago.</b><br/>If this isn't "
"right, please contact Human Resource staff"
msgstr "<b>警告 最終チェックインは12時間以上前です</b><br/>もし誤りがある場合は、人事担当者にご連絡ください。"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_kanban
msgid "<i class=\"fa fa-calendar\" aria-label=\"Period\" role=\"img\" title=\"Period\"/>"
msgstr "<i class=\"fa fa-calendar\" aria-label=\"Period\" role=\"img\" title=\"Period\"/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Last Month\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            先月\n"
"                        </span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "<span class=\"o_stat_text\">Extra Hours</span>"
msgstr "<span class=\"o_stat_text\">時間外</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle text-success "
"me-1\" role=\"img\" aria-label=\"Available\" title=\"Available\"/>"
msgstr ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle text-success "
"me-1\" role=\"img\" aria-label=\"Available\" title=\"Available\"/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle text-warning me-1\" role=\"img\" aria-label=\"Not available\" title=\"Not available\">\n"
"                                    </span>"
msgstr ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle text-warning me-1\" role=\"img\" aria-label=\"Not available\" title=\"Not available\">\n"
"                                    </span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span> Minutes</span>"
msgstr "<span> 分</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"<span> Minutes</span>\n"
"                                    <br/>\n"
"                                    <br/>"
msgstr ""
"<span> 分</span>\n"
"<br/>\n"
"<br/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span>Time Period </span>"
msgstr "<span>時間 </span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Activate the count of employees' extra hours."
msgstr "従業員の時間労働カウントを有効化します。"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid ""
"Add a few employees to be able to select an employee here and perform his check in / check out.\n"
"                To create employees go to the Employees menu."
msgstr ""
"ここで従業員を選択し、チェックイン/チェックアウトを実行できるように、何名かの従業員を追加します。\n"
"従業員を作成するには、従業員メニューに移動します。"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__adjustment
msgid "Adjustment"
msgstr "消込"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_manager
msgid "Administrator"
msgstr "管理者"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Allow a period of time (around working hours) where extra time will not be "
"counted, in benefit of the company"
msgstr "会社の利益のために、時間外労働がカウントされない時間（就業時間前後）を設ける。"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Allow a period of time (around working hours) where extra time will not be "
"deducted, in benefit of the employee"
msgstr "従業員のために、時間外労働が控除されない時間（就業時間前後）を設ける。"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
msgid "Amount of extra hours"
msgstr "時間外労働時間数"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "An apple a day keeps the doctor away"
msgstr "リンゴ1日一個で医者いらず"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Another good day's work! See you soon!"
msgstr "またいつか！ また近いうちにお会いしましょう！"

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_my_attendances
#: model:ir.model,name:hr_attendance.model_hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_ids
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_pivot
msgid "Attendance"
msgstr "勤怠"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_report_action
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_report_action_filtered
msgid "Attendance Analysis"
msgstr "勤怠分析"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_delay
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_delay
msgid "Attendance Kiosk Delay"
msgstr "出勤キオスク遅延"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_mode
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_mode
msgid "Attendance Mode"
msgstr "出勤モード"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_attendance_overtime
msgid "Attendance Overtime"
msgstr "出勤残業"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_attendance_report
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_graph
msgid "Attendance Statistics"
msgstr "出勤分析"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_state
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__attendance_state
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__attendance_state
msgid "Attendance Status"
msgstr "出勤ステータス"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action_employee
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action_overview
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_kiosk_mode
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_attendances_overview
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_root
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_view_attendances
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Attendances"
msgstr "勤怠"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Available"
msgstr "処理可能"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_barcode_source__back
msgid "Back Camera"
msgstr "バックカメラ"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Barcode"
msgstr "バーコード"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_kiosk_mode__barcode
msgid "Barcode / RFID"
msgstr "バーコード / RFID"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_kiosk_mode__barcode_manual
msgid "Barcode / RFID and Manual Selection"
msgstr "バーコード / RFID、手動選択"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_barcode_source
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_barcode_source
msgid "Barcode Source"
msgstr "バーコード読取"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee hasn't "
"checked out since %(datetime)s"
msgstr "%(empl_name)sの新しい勤怠記録を作成できません。従業員は%(datetime)s以降にチェックアウトしていません"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee was "
"already checked in on %(datetime)s"
msgstr "%(empl_name)sの新しい勤怠記録を作成できません。従業員は%(datetime)s にすでに出勤しています。"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid ""
"Cannot perform check out on %(empl_name)s, could not find corresponding "
"check in. Your attendances have probably been modified manually by human "
"resources."
msgstr ""
"%(empl_name)sで退勤を実行できません。対応する出勤が見つかりませんでした。あなたの勤怠はおそらく人事によって手動で変更されています。"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Check IN"
msgstr "出勤"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_in
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_in
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Check In"
msgstr "チェックイン"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_my_attendances
msgid "Check In / Check Out"
msgstr "チェックイン / チェックアウト"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Check OUT"
msgstr "退勤"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_out
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_out
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_out
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Check Out"
msgstr "チェックアウト"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Check-In/Out in Kiosk Mode"
msgstr "キオスクモードでのチェックイン／アウト"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_in
msgid "Checked in"
msgstr "チェックイン"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Checked in at"
msgstr "チェックイン時刻:"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_out
msgid "Checked out"
msgstr "チェックアウト"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Checked out at"
msgstr "チェックアウト時刻:"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Choose how long the greeting message will be displayed."
msgstr "挨拶メッセージの表示時間を選択"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_company
msgid "Companies"
msgstr "会社"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__company_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__company_id
msgid "Company"
msgstr "会社"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Company Logo"
msgstr "会社ロゴ"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Compare attendance with working hours set on employee."
msgstr "従業員に設定された就業時間と出勤時間を比較"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_settings
msgid "Configuration"
msgstr "設定"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__hr_attendance_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__hr_attendance_overtime
msgid "Count Extra Hours"
msgstr "時間外労働のカウント"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Count of Extra Hours"
msgstr "時間外労働時間数"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Count of extra hours is considered from this date. Potential extra hours "
"prior to this date are not considered."
msgstr "時間外労働のカウントはこの日から考慮されます。それ以前の時間外労働は考慮されません。"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid "Create a new employee"
msgstr "新しい従業員を作成"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__create_uid
msgid "Created by"
msgstr "作成者"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__create_date
msgid "Created on"
msgstr "作成日"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__date
msgid "Day"
msgstr "日"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Define the camera used for the barcode scan."
msgstr "バーコードのスキャンに使用するカメラを定義"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Define the way the user will be identified by the application."
msgstr "アプリケーションでユーザを識別する方法を定義"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__department_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__department_id
msgid "Department"
msgstr "部門"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__display_name
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__display_name
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__display_name
msgid "Display Name"
msgstr "表示名"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Display Time"
msgstr "表示時間"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Do not have access, user cannot edit the attendances that are not his own."
msgstr "アクセス権がない場合、ユーザは自分以外の出席を編集することはできません。"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Early to bed and early to rise, makes a man healthy, wealthy and wise"
msgstr "早起きして早起きする、裕福で賢明な人を健康にする"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Eat breakfast as a king, lunch as a merchant and supper as a beggar"
msgstr "王としての朝食、商人としての昼食、乞食の夕食"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__employee_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__employee_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid "Employee"
msgstr "従業員"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__group_attendance_use_pin
msgid "Employee PIN"
msgstr "従業員PIN"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Employee attendances"
msgstr "従業員の勤怠"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_employee_attendance_action_kanban
msgid "Employees"
msgstr "従業員"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_use_pin
msgid "Enable PIN use"
msgstr "PIN番号使用有効化"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Error: could not find corresponding employee."
msgstr "エラー：対応する従業員が見つかりませんでした。"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_overtime_action
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__duration
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__overtime_hours
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Extra Hours"
msgstr "時間外労働"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__duration_real
msgid "Extra Hours (Real)"
msgstr "時間外労働(実際)"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_start_date
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_start_date
msgid "Extra Hours Starting Date"
msgstr "時間外労働開始日時"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Extra hours today:"
msgstr "今日の超過労働時間："

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance_overtime__duration_real
msgid "Extra-hours including the threshold duration"
msgstr "しきい値期間を含む時間外労働"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "First come, first served"
msgstr "先着順"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_barcode_source__front
msgid "Front Camera"
msgstr "フロントカメラ"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Glad to have you back, it's been a while!"
msgstr "お疲れ様でした"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Go back"
msgstr "戻る"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good afternoon"
msgstr "こんにちは！"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good evening"
msgstr "こんばんは"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good morning"
msgstr "おはようございます"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good night"
msgstr "おやすみなさい"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Goodbye"
msgstr "さようなら"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Group By"
msgstr "グループ化"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_search
msgid "HR Attendance Search"
msgstr "HR出勤検索"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a good afternoon"
msgstr "お疲れさまでした！"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a good day!"
msgstr "それでは"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a good evening"
msgstr "お疲れさまでした。"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a nice lunch!"
msgstr "良い昼食を！"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Hours"
msgstr "時間"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_last_month
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__hours_last_month
msgid "Hours Last Month"
msgstr "先月の勤務時間"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_last_month_display
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__hours_last_month_display
msgid "Hours Last Month Display"
msgstr "先月の勤務時間表示"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_today
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__hours_today
msgid "Hours Today"
msgstr "今日の勤務時間"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__worked_hours
msgid "Hours Worked"
msgstr "勤務時間"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Hr Attendance Search"
msgstr "勤怠の検索"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__id
msgid "ID"
msgstr "ID"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Identify Manually"
msgstr "手動で選択"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "If a job is worth doing, it is worth doing well!"
msgstr "仕事が価値があるなら、それはうまくいく価値があります！"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Invalid request"
msgstr "無効なリクエスト"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_kiosk_no_user_mode
msgid "Kiosk Mode"
msgstr "キオスクモード"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_attendance_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__last_attendance_id
msgid "Last Attendance"
msgstr "最終出勤"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance____last_update
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime____last_update
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_kiosk_mode__manual
msgid "Manual Selection"
msgstr "手動選択"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_ir_ui_menu
msgid "Menu"
msgstr "メニュー"

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_greeting_message
msgid "Message"
msgstr "メッセージ"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "My Attendances"
msgstr "自分の勤怠"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "No Check Out"
msgstr "チェックアウトなし"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action
msgid "No attendance records found"
msgstr "出勤記録は見つかりませんでした"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_employee
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_overview
msgid "No attendance records to display"
msgstr "表示する出勤レコードはありません"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid "No employee corresponding to Badge ID '%(barcode)s.'"
msgstr "バッジID '%(barcode)s'に一致する従業員はいません。"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "OK"
msgstr "OK"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_user
msgid "Officer : Manage all attendances"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__overtime_ids
msgid "Overtime"
msgstr "時間超過"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Please contact your administrator."
msgstr "管理者にご連絡ください。"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Please enter your PIN to"
msgstr "PINコードを入力して"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Please return to the main menu."
msgstr "メインメニューに戻ります。"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee_public
msgid "Public Employee"
msgstr "公務員"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_report
msgid "Reporting"
msgstr "レポーティング"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Scan your badge"
msgstr "バッジをスキャン"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_barcode_source__scanner
msgid "Scanner"
msgstr "スキャナー"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Set PIN codes in the employee detail form (in HR Settings tab)."
msgstr "従業員詳細フォーム(HR設定タブより)でPINコードを設定"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.action_hr_attendance_settings
msgid "Settings"
msgstr "管理設定"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Sign out"
msgstr "サインアウト"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Start from"
msgstr "開始日"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid "Such grouping is not allowed."
msgstr "このようなグループ化は許可されていません。"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_employee
msgid "The attendance records of your employees will be displayed here."
msgstr "従業員の勤怠記録がここに表示されます。"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "The early bird catches the worm"
msgstr "早起きは三文の得"

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_kiosk
msgid ""
"The user will be able to open the kiosk mode and validate the employee PIN."
msgstr "ユーザはキオスクモードを開き、従業員PINを検証することができます。"

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance
msgid ""
"The user will gain access to the human resources attendance menu, enabling "
"him to manage his own attendance."
msgstr "ユーザーは人事管理の出勤メニューにアクセスし、出勤者を管理することができます。"

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_use_pin
msgid ""
"The user will have to enter his PIN to check in and out manually at the "
"company screen."
msgstr "ユーザは会社の画面で手動でチェックインおよびチェックアウトするためにPINを入力する必要があります。"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid ""
"To activate Kiosk mode without pin code, you must have access right as an "
"Officer or above in the Attendance app. Please contact your administrator."
msgstr "ピンコードなしでキオスクモードを有効にするには、勤怠アプリで役員以上のアクセス権を持っている必要があります。管理者にお問い合わせ下さい。"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Today's work hours:"
msgstr "本日の就業時間"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_company_threshold
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_company_threshold
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Tolerance Time In Favor Of Company"
msgstr "会社に有利な許容時間"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_employee_threshold
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_employee_threshold
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Tolerance Time In Favor Of Employee"
msgstr "従業員に有利な許容時間"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__total_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__total_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__total_overtime
msgid "Total Overtime"
msgstr "合計残業時間"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Total extra hours:"
msgstr "超過労働時間合計："

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Use PIN codes (defined on the Employee's profile) to check-in."
msgstr "チェックインには、PINコード（従業員のプロファイルに定義）を使用"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_users
#: model:res.groups,name:hr_attendance.group_hr_attendance
msgid "User"
msgstr "ユーザ"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_kiosk
msgid "User : Only kiosk mode"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Want to check out?"
msgstr "チェックアウトしますか？"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Warning"
msgstr "警告"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Welcome"
msgstr "ようこそ！"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Welcome to"
msgstr "ようこそ"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Welcome!"
msgstr "ようこそ！"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Work Hours"
msgstr "就業時間"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__worked_hours
msgid "Worked Hours"
msgstr "就業時間"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Worked hours last month"
msgstr "先月勤務時間"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid "Wrong PIN"
msgstr "不正な暗証番号"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "You cannot duplicate an attendance."
msgstr "勤怠を複製することはできません。"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_overview
msgid "Your attendance records will be displayed here."
msgstr "あなたの勤怠レコードはここに表示されます。"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "check in"
msgstr "チェックイン"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "check out"
msgstr "チェックアウト"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "seconds"
msgstr "秒"
