# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays_attendance
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_type.py:0
#, python-format
msgid "%s hours available"
msgstr "%s horas disponibles"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_attendance
msgid "Attendance"
msgstr "Asistencia"

#. module: hr_holidays_attendance
#. odoo-javascript
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "Available"
msgstr "Disponible"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__hr_attendance_overtime
msgid "Count Extra Hours"
msgstr "Número de horas adicionales"

#. module: hr_holidays_attendance
#. odoo-javascript
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "Days"
msgstr "Días"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_type__overtime_deductible
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_employee_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.res_users_view_form
msgid "Deduct Extra Hours"
msgstr "Descontar horas adicionales"

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_allocation_overtime_view_form
msgid "Discard"
msgstr "Descartar"

#. module: hr_holidays_attendance
#: model:hr.leave.type,name:hr_holidays_attendance.holiday_status_extra_hours
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__overtime_id
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__overtime_id
msgid "Extra Hours"
msgstr "Horas adicionales"

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_attendance_holidays_hr_leave_allocation_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_view_form_overtime
msgid "Extra Hours Available"
msgstr "Horas adicionales disponibles"

#. module: hr_holidays_attendance
#. odoo-javascript
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "Hours"
msgstr "Horas"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_type__hr_attendance_overtime
msgid "Hr Attendance Overtime"
msgstr "Asistencia de tiempo adicional de RR. HH."

#. module: hr_holidays_attendance
#: model:ir.actions.act_window,name:hr_holidays_attendance.hr_leave_allocation_overtime_action
#: model:ir.actions.act_window,name:hr_holidays_attendance.hr_leave_allocation_overtime_manager_action
msgid "New Allocation Request"
msgstr "Nueva solicitud de asignación"

#. module: hr_holidays_attendance
#: model:ir.model.fields,help:hr_holidays_attendance.field_hr_leave_type__overtime_deductible
msgid ""
"Once a time off of this type is approved, extra hours in attendances will be"
" deducted."
msgstr ""
"Una vez que este tipo de tiempo personal se apruebe, se descontarán las "
"horas adicionales en asistencias."

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__overtime_deductible
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__overtime_deductible
msgid "Overtime Deductible"
msgstr "Tiempo adicional descontable"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_res_users__request_overtime
msgid "Request Overtime"
msgstr "Solicitar tiempo adicional"

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_allocation_overtime_view_form
msgid "Save"
msgstr "Guardar"

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"The employee does not have enough extra hours to extend this allocation."
msgstr ""
"El empleado no tiene suficientes horas adicionales para extender esta "
"asignación."

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
#, python-format
msgid "The employee does not have enough extra hours to extend this leave."
msgstr ""
"El empleado no tiene suficientes horas adicionales para extender este "
"permiso."

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"The employee does not have enough extra hours to request this allocation."
msgstr ""
"El empleado no tiene suficientes horas adicionales para solicitar esta "
"asignación."

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
#, python-format
msgid "The employee does not have enough extra hours to request this leave."
msgstr ""
"El empleado no tiene suficientes horas adicionales para solicitar este "
"permiso."

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"The employee does not have enough overtime hours to request this leave."
msgstr ""
"El empleado no tiene suficientes horas adicionales para solicitar este "
"permiso."

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave
msgid "Time Off"
msgstr "Tiempo personal"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave_allocation
msgid "Time Off Allocation"
msgstr "Asignación de tiempo personal"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave_type
msgid "Time Off Type"
msgstr "Tipo de tiempo personal"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__employee_overtime
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__employee_overtime
msgid "Total Overtime"
msgstr "Total de horas adicionales"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_res_users
msgid "User"
msgstr "Usuario"

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
#, python-format
msgid "You do not have enough extra hours to request this leave"
msgstr "No tiene suficientes horas adicionales para solicitar este permiso"
