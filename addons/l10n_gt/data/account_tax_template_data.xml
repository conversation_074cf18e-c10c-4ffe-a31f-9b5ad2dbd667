<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Compras e IVA por Cobrar -->
        <record id="impuestos_plantilla_iva_por_cobrar" model="account.tax.template">
            <field name="chart_template_id" ref="cuentas_plantilla"/>
            <field name="name">IVA por Cobrar</field>
            <field name="description">IVA por Cobrar</field>
            <field name="amount" eval="12"/>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include" eval="True"/>
            <field name="tax_group_id" ref="tax_group_iva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {'repartition_type': 'base'}),

                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('cta110301'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),

                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('cta110301'),
                }),
            ]"/>
        </record>
        <!-- Ventas e IVA por Pagar -->
        <record id="impuestos_plantilla_iva_por_pagar" model="account.tax.template">
            <field name="chart_template_id" ref="cuentas_plantilla"/>
            <field name="name">IVA por Pagar</field>
            <field name="description">IVA por Pagar</field>
            <field name="amount" eval="12"/>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include" eval="True"/>
            <field name="tax_group_id" ref="tax_group_iva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {'repartition_type': 'base'}),

                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('cta210201'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {'repartition_type': 'base'}),

                (0,0, {
                    'repartition_type': 'tax',
                    'account_id': ref('cta210201'),
                }),
            ]"/>
        </record>
    </data>
</odoo>
