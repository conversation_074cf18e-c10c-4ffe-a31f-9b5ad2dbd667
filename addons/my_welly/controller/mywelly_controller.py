import base64
import calendar
import csv
import json
import locale
import logging
import math
import re
from datetime import datetime, timedelta, timezone, date
from io import StringIO
import os

import pytz
import werkzeug
import urllib.parse
import jwt
import requests
from bs4 import BeautifulSoup
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization

from odoo import http, fields, api
from odoo.addons.welly_base.fields import selection
from odoo.http import request
from odoo.sql_db import db_connect
from odoo.service import db
from odoo.tools import config

_logger = logging.getLogger(__name__)


# JWT authen
def verify_token(token, is_update_welly_id: bool, check_domain: bool):
    try:
        SECRET_KEY = request.env['ir.config_parameter'].sudo().get_param('jwt.secret')
        # G<PERSON><PERSON><PERSON> mã chuỗi khóa công khai từ base64
        public_key_bytes = base64.b64decode(SECRET_KEY)

        # Tạ<PERSON> đối tượng <PERSON>ey từ chuỗi khóa công khai
        public_key = serialization.load_der_public_key(
            public_key_bytes,
            backend=default_backend()
        )

        # X<PERSON><PERSON> thực JWT
        payload = jwt.decode(
            token,
            public_key,
            algorithms=["RS256"],
            options={"verify_aud": False}
        )
        welly_id = payload.get("sub")
        partner_phone = payload.get("preferred_username")
        # lấy sdt từ jwt có dạng "+84xxx" về "0xxx"
        if partner_phone and partner_phone.startswith("+84"):
            partner_phone = '0' + partner_phone[3:]

        if check_domain:
            return partner_phone, welly_id, None
        else:
            # nếu là lần đầu đăng nhập thì cập nhật welly_id vào db không thì lấy ra và so sánh
            if is_update_welly_id:
                partner = request.env['res.partner'].sudo().search([
                    ('phone', '=', partner_phone)
                ])
                if partner:
                    partner.write({'welly_id': welly_id, 'is_deleted_welly_id': False})
            else:
                partner = request.env['res.partner'].sudo().search([
                    ('welly_id', '=', welly_id),
                    ('phone', '=', partner_phone)
                ])
        if not partner:
            return None, None, "Tài khoản không hợp lệ. Đăng ký tư vấn để được hỗ trợ!"
        if partner.is_deleted_welly_id:
            return None, None, "Tài khoản của bạn đã bị xóa khỏi hệ thống."
        return partner.id, None, None  # Token hợp lệ, trả về partner_id

    except jwt.ExpiredSignatureError:
        return None, None, "Token expired"
    except jwt.InvalidTokenError:
        return None, None, "Invalid token"
    except Exception as e:
        return None, None, str(e)


def get_and_verify_jwt_from_header(is_update_welly_id: bool):
    # Lấy header Authorization
    auth_header = request.httprequest.headers.get('Authorization')
    if auth_header and auth_header.startswith('Bearer '):
        token = auth_header[7:]  # Tách Bearer để lấy token
    else:
        return None, "Token is missing or invalid"

    # Xác thực JWT
    partner_id, welly_id, error = verify_token(token=token, is_update_welly_id=is_update_welly_id, check_domain=False)
    return partner_id, error

# dùng cho api lấy địa điểm của user, kiểm tra xem user có tồn tại dữ liệu ở những tenancy nào
def get_and_verify_jwt_from_header_none_domain(is_update_welly_id: bool):
    # Lấy header Authorization
    auth_header = request.httprequest.headers.get('Authorization')
    if auth_header and auth_header.startswith('Bearer '):
        token = auth_header[7:]  # Tách Bearer để lấy token
    else:
        return None, "Token is missing or invalid"

    # Xác thực JWT
    partner_phone, welly_id, error = verify_token(token=token, is_update_welly_id=is_update_welly_id, check_domain=True)
    return partner_phone, welly_id, error

def generate_jwt_token(partner_phone, expires_delta=None):
    partner = request.env['res.partner'].sudo().search([
        ('phone', '=', partner_phone)
    ])
    if not partner:
        return ""
    if not partner.welly_id:
        return ""

    payload = {
        "sub": f"{partner.welly_id}",
        "preferred_username": f"+84{partner_phone[1:]}"
    }
    private_key_str = request.env['ir.config_parameter'].sudo().get_param('jwt_secret_admin')
    private_key_bytes = base64.b64decode(private_key_str)

    private_key = serialization.load_der_private_key(
        private_key_bytes,
        password=None,
        backend=default_backend()
    )

    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(hours=1)

    payload.update({
        "exp": expire,
        "iat": datetime.utcnow()
    })

    token = jwt.encode(
        payload,
        private_key,
        algorithm='RS256'
    )
    return token

def get_data_and_log_request(request):
    data = json.loads(request.httprequest.data)
    _logger.info(f"Request: {data}  - Header: {request.httprequest.headers}")
    return data

class BaseResponse:

    @staticmethod
    def success(data=None, message="SUCCESS", index=None):
        response = {
            "errorCode": 0,
            "message": message,
            "data": data
        }

        if index:
            response["index"] = index
        _logger.info(f"Response: {response}")
        ex = json.dumps(response)
        response = request.make_response(ex, status=200)
        response.headers['Content-Type'] = 'application/json'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Headers'] = 'POST, GET, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Origin, X-Requested-With, Content-Type, Accept'
        return response

    @staticmethod
    def error(error_code, message="Error", data=None):
        request.env.cr.rollback()
        data = {
            "errorCode": error_code,
            "message": message,
            "data": data
        }
        response = None
        _logger.info(f"Response: {data}")
        if error_code == 1 or error_code == 400 or error_code == 404:
            response = request.make_response(json.dumps(data), status=200)
        else:
            response = request.make_response(json.dumps(data), status=error_code)
        response.headers['Content-Type'] = 'application/json'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Headers'] = 'POST, GET, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Origin, X-Requested-With, Content-Type, Accept'
        return response


class MyWellyController(http.Controller):
    # api update phone +84 về 0
    @http.route('/api/updatePhone', type='http', auth='public', methods=['POST'], csrf=False)
    def updatePhone(self, **kwargs):
        try:
            partners = request.env['res.partner'].sudo().search([])

            for partner in partners:
                if partner.phone:
                    partner_phone = self.format_phone_number(partner.phone)
                    partner.write({'phone': partner_phone})
            return BaseResponse.success()
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/updateSaleOrderId', type='http', auth='public', methods=['POST'], csrf=False)
    def updateSaleOrderId(self, **kwargs):
        try:
            domain = [
                ('invoice_ids.sale_order_id', '=', False),  # account.move có sale_order_id là NULL
                ('invoice_ids', '!=', False)  # Có liên kết với account.move qua invoice_ids
            ]

            # Thực hiện tìm kiếm với domain từ sale.order
            sale_orders = request.env['sale.order'].sudo().search(domain)
            for r in sale_orders:
                moves = r.mapped('invoice_ids')
                for m in moves:
                    m.sale_order_id = r.id
            return BaseResponse.success()
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/updateAvatar', type='http', auth='public', methods=['POST'], csrf=False)
    def updateAvatar(self, **kwargs):
        try:
            partners = request.env['res.partner'].sudo().search(
                domain=[('image_1920', '!=', False)]
            )
            for partner in partners:
                if partner.image_1920:
                    partner.write({'avatar_mywelly': partner.image_1920})
            return BaseResponse.success()
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api merge dữ liệu thay đổi ở biên lai
    @http.route('/api/mergeAccountMove', type='http', auth='public', methods=['POST'], csrf=False)
    def merge_account_move(self, **kwargs):
        try:
            account_moves = request.env['account.move'].sudo().search(
                domain=[('journal_id', '=', int(1))]
            )
            for account in account_moves:
                exit_welly_account_move = request.env['welly.partner.account.move'].sudo().search(
                    [('account_move_id', '=', account.id),
                     ('partner_id', '=', account.partner_id.id)])
                if not exit_welly_account_move:
                    partner_id = account.partner_id.id
                    if not partner_id:
                        print(account.id)
                    else:
                        new_welly_account_move = request.env['welly.partner.account.move'].sudo().create({
                            'account_move_id': account.id,
                            'partner_id': account.partner_id.id})
                # cập nhật location
                location_id = account.welly_location.id
                if location_id:
                    location_ids = []
                    location_ids.append(location_id)
                    account.write({'welly_location_many2_many': [(6, 0, location_ids)]})
            return BaseResponse.success()
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api merge dữ liệu thay đổi ở biên lai location
    @http.route('/api/mergeAccountMoveLocation', type='http', auth='public', methods=['POST'], csrf=False)
    def merge_account_move_location(self, **kwargs):
        try:
            account_moves = request.env['account.move'].sudo().search(
                domain=[('journal_id', '=', int(1))]
            )
            for account in account_moves:
                # cập nhật location
                location_id = account.welly_location.id
                if location_id:
                    location_ids = []
                    location_ids.append(location_id)
                    account.write({'welly_location_many2_many': [(6, 0, location_ids)]})
            return BaseResponse.success()
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api merge dữ liệu thay đổi ở hợp đồng: pt_staff_id, marketing_staff_id
    @http.route('/api/mergeContractStaff', type='http', auth='public', methods=['POST'], csrf=False)
    def merge_contract_staff_id(self, **kwargs):
        try:
            contract_records = request.env['welly.contract'].sudo().search(
                domain=[('id', '!=', 0)]
            )
            for contract in contract_records:
                contract.write({'marketing_staff_id': contract.welly_invoice_id.marketing_staff_id.ids,
                                'pt_staff_id': contract.welly_invoice_id.pt_staff_id.ids})
            return BaseResponse.success()
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api merge dữ liệu thay đổi ở hợp đồng: pt_staff_id, marketing_staff_id
    @http.route('/api/mergeContractLocation', type='http', auth='public', methods=['POST'], csrf=False)
    def merge_contract_location(self, **kwargs):
        try:
            contract_records = request.env['welly.contract'].sudo().search(
                domain=[('id', '!=', 0)]
            )
            for contract in contract_records:
                # cập nhật location
                location_id = contract.welly_location.id
                if location_id:
                    location_ids = []
                    location_ids.append(location_id)
                    contract.write({'welly_location_many2_many': [(6, 0, location_ids)]})
            return BaseResponse.success()
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api merge dữ liệu thay đổi ở hợp đồng
    @http.route('/api/mergeContract', type='http', auth='public', methods=['POST'], csrf=False)
    def merge_contract(self, **kwargs):
        try:
            contract_records = request.env['welly.contract'].sudo().search(
                domain=[('id', '!=', 0)]
            )
            for contract in contract_records:
                # cập nhật available_session_number
                if contract.welly_invoice_id and contract.service_type == selection.ServiceType.PT:
                    contract.write({'available_session_number': contract.welly_invoice_id.session_number})
                # tạo dữ liệu trong bảng welly.partner.account.move với trường hợp đồng
                exit_welly_account_move = request.env['welly.partner.account.move'].sudo().search(
                    [('account_move_id', '=', contract.welly_invoice_id.id),
                     ('partner_id', '=', contract.partner_id.id)])
                if exit_welly_account_move:
                    exit_welly_account_move.write({'welly_contract_id': contract.id})

                    # tạo dữ liệu bảng trung gian many2many hợp đồng khách hàng
                    partner_ids = []
                    partner_ids.append(exit_welly_account_move.partner_id.id)
                    contract.write({'partner_account_ids': [(6, 0, partner_ids)]})
            return BaseResponse.success()
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy thông tin contract
    @http.route('/api/contract/getContractInfo', type='http', auth='public', methods=['POST'], csrf=False)
    def get_contract_info(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            contract_id = data.get('contract_id')
            if not contract_id:
                return BaseResponse.error(error_code=1, message="partner_id, contract_id required")
            # lấy ra danh sách con của partner_id
            partner_rc = request.env['res.partner'].sudo().browse(int(partner_id))
            list_id_child = partner_rc.child_my_welly_id.ids
            list_id_child.append(int(partner_id))

            contract_records = request.env['welly.contract'].sudo().search_read(
                domain=[('id', '=', int(contract_id))],
                fields=['id', 'service_type', 'name', 'coach_id', 'welly_location_many2_many', 'state',
                        'partner_name_print',
                        'date_start',
                        'date_end',
                        'registration_form_name_print', 'pay_amount', 'pay_amount_to_text',
                        'sale_order_template_name_print', 'welly_gift_name_print', 'exercise_form_name_print',
                        'available_session_number', 'free_session_number', 'total_free_session_number', 'create_date',
                        'session_number', 'exercise_form_id']
            )
            if not contract_records:
                return BaseResponse.error(error_code=1, message="contract_records not exits")
            # lọc response
            for record in contract_records:
                record['date_start'] = str(record['date_start'])
                date_end = record['date_end']
                record['valid_time'] = None
                record['valid_time_type'] = None
                if date_end:
                    record['date_end'] = str(record['date_end'])
                    record['valid_time'] = self.convert_valid_time_type(date_end)
                    record['valid_time_type'] = "day"
                else:
                    record['date_end'] = None
                record['code'] = record['name']
                record.pop('name', None)

                record['coach_name'] = ""
                record['coach_image'] = ""
                if record['coach_id']:
                    record['coach_name'] = record['coach_id'][1]
                    record['coach_id'] = record['coach_id'][0]
                    record['coach_image'] = None
                    user = request.env['res.users'].sudo().browse(int(record['coach_id']))
                    base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
                    image = request.env['ir.attachment'].sudo().search(
                        [('res_model', '=', 'res.partner'), ('res_id', '=', user.partner_id.id),
                         ('res_field', '=', 'image_128')], limit=1)
                    if image:
                        access_token = image.generate_access_token()[0]
                        record['coach_image'] = f"{base_url}/web/image/{image.id}/image_128?access_token={access_token}"
                else:
                    record['coach_id'] = None
                record['location'] = []
                if len(record['welly_location_many2_many']) > 0:
                    welly_location = request.env['welly.location'].sudo()
                    for l in record['welly_location_many2_many']:
                        location = welly_location.search([('id', '=', int(l))])
                        record['location'].append(location.name)

                record.pop('welly_location_many2_many', None)

                record['partner_name'] = record['partner_name_print']
                record.pop('partner_name_print', None)

                record['registration_form_name'] = record['registration_form_name_print']
                record.pop('registration_form_name_print', None)

                record['name'] = record['sale_order_template_name_print'] if record['sale_order_template_name_print'] else None
                record.pop('sale_order_template_name_print', None)

                record['gift_name'] = record['welly_gift_name_print']
                record.pop('welly_gift_name_print', None)

                record['exercise_form_name'] = record['exercise_form_name_print'] if record[
                    'exercise_form_name_print'] else None
                record.pop('exercise_form_name_print', None)
                if record['service_type'] == 'member':
                    record['session_number'] = 0
                    record['available_session_number'] = 0
                total_available_session_number = record['available_session_number'] + record['free_session_number']
                record['total_session_number'] = record['session_number'] + record['total_free_session_number']
                record.pop('session_number', None)
                record['next_session_number'] = record['total_session_number'] - total_available_session_number + 1
                # Số buổi đã tập của hđ
                record['used_session_number'] = record['total_session_number'] - total_available_session_number
                record['available_session_number'] = total_available_session_number
                record['create_date'] = self._convert_utc_to_timestamp_milliseconds((record['create_date']))
                record['limit_participants'] = None
                if record['exercise_form_id']:
                    record['limit_participants'] = request.env['welly.exercise.form'].sudo().browse(
                        record['exercise_form_id'][0]).limit_participants
                    record.pop('exercise_form_id', None)
            return BaseResponse.success(data=contract_records[0])
        except Exception as e:
            logging.error(e)
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy list contract theo state và partner
    @http.route('/api/contract/getListContract', type='http', auth='public', methods=['POST'], csrf=False)
    def getListContract(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            # lấy ra danh sách con của partner_id
            partner_rc = request.env['res.partner'].sudo().browse(int(partner_id))
            list_id_child = partner_rc.child_my_welly_id.ids
            list_id_child.append(int(partner_id))

            state = data.get('state')
            page = data.get('page', 1)
            size = data.get('size', 10)

            # tổng hợp state
            query_state = (1, '=', 1)
            and_query = (1, '=', 1)
            installment_state = (1, '=', 1)
            if state == 'draft':
                query_state = ('state', 'in', ['draft', 'confirm_admin', 'sign_coo', 'sign_customer', 'confirm_recep',
                                               'waiting_active'])
            elif state == 'activated':
                and_query = '|'
                query_state = ('state', 'in', ['activated'])
                installment_state = ('is_installment_activated', '=', True)
            elif state == 'done':
                query_state = ('state', 'in', ['done'])
            elif state == 'cancel':
                query_state = ('state', 'in', ['reject', 'cancel'])
            # build query
            domain = [
                ('partner_account_ids', 'in', list_id_child),
                and_query,
                query_state,
                installment_state
            ]

            # phân trang
            offset = (page - 1) * size
            total_records = request.env['welly.contract'].sudo().search_count(domain)
            total_page = (total_records + size - 1) // size
            index = {
                "page": page,
                "size": size,
                "total_page": total_page,
                "total_record": total_records
            }

            # query và lọc response
            contract_records = request.env['welly.contract'].sudo().search_read(
                domain=domain,
                fields=['id', 'service_type', 'name', 'coach_id', 'welly_location_many2_many', 'state',
                        'partner_name_print',
                        'date_start',
                        'date_end',
                        'registration_form_name_print', 'pay_amount', 'pay_amount_to_text',
                        'sale_order_template_name_print', 'welly_gift_name_print', 'exercise_form_name_print',
                        'available_session_number', 'free_session_number', 'total_free_session_number', 'create_date',
                        'session_number',
                        'remaining_amount', 'payment_state', 'payment_type_welly', 'is_installment_activated'],
                order='create_date desc',
                offset=offset,
                limit=size
            )
            if not contract_records:
                return BaseResponse.success(data=[])
            # lọc response
            for record in contract_records:
                record['date_start'] = str(record['date_start'])
                date_end = record['date_end']
                record['valid_time'] = None
                record['valid_time_type'] = None
                if date_end:
                    record['date_end'] = str(record['date_end'])
                    record['valid_time'] = self.convert_valid_time_type(date_end)
                    record['valid_time_type'] = "day"
                else:
                    record['date_end'] = None
                record['code'] = record['name']
                record.pop('name', None)

                record['coach_name'] = ""
                record['coach_image'] = ""
                if record['coach_id']:
                    record['coach_name'] = record['coach_id'][1]
                    record['coach_id'] = record['coach_id'][0]
                    record['coach_image'] = None
                    user = request.env['res.users'].sudo().browse(int(record['coach_id']))
                    base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
                    image = request.env['ir.attachment'].sudo().search(
                        [('res_model', '=', 'res.partner'), ('res_id', '=', user.partner_id.id),
                         ('res_field', '=', 'image_128')], limit=1)
                    if image:
                        access_token = image.generate_access_token()[0]
                        record['coach_image'] = f"{base_url}/web/image/{image.id}/image_128?access_token={access_token}"
                else:
                    record['coach_id'] = None

                record['location'] = []
                if len(record['welly_location_many2_many']) > 0:
                    welly_location = request.env['welly.location'].sudo()
                    for l in record['welly_location_many2_many']:
                        location = welly_location.search([('id', '=', int(l))])
                        record['location'].append(location.name)

                record.pop('welly_location_many2_many', None)

                record['partner_name'] = record['partner_name_print']
                record.pop('partner_name_print', None)

                record['registration_form_name'] = record['registration_form_name_print']
                record.pop('registration_form_name_print', None)

                record['name'] = record['sale_order_template_name_print'] if record['sale_order_template_name_print'] else None
                record.pop('sale_order_template_name_print', None)

                record['gift_name'] = record['welly_gift_name_print']
                record.pop('welly_gift_name_print', None)

                record['exercise_form_name'] = record['exercise_form_name_print'] if record[
                    'exercise_form_name_print'] else None
                record.pop('exercise_form_name_print', None)
                if record['service_type'] == 'member':
                    record['session_number'] = 0
                    record['available_session_number'] = 0
                total_available_session_number = record['available_session_number'] + record['free_session_number']
                record['total_session_number'] = record['session_number'] + record['total_free_session_number']
                record.pop('session_number', None)
                record['next_session_number'] = record['total_session_number'] - total_available_session_number + 1
                # Số buổi đã tập của hđ
                record['used_session_number'] = record['total_session_number'] - total_available_session_number
                record['available_session_number'] = total_available_session_number
                record['create_date'] = self._convert_utc_to_timestamp_milliseconds((record['create_date']))

                if record['is_installment_activated']:
                    if record['payment_state'] == "partial":
                        record['state'] = "installment_activated"
                    if record['payment_state'] == "paid":
                        record['state'] = "activated"
                else:
                    record['is_installment_activated'] = None
            return BaseResponse.success(data=contract_records, index=index)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy thông tin hội viên và người liên hệ trong hợp đồng
    @http.route('/api/contract/getPartnerContractInfo', type='http', auth='public', methods=['POST'], csrf=False)
    def get_partner_contract_info(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            contract_id = data.get('contract_id')
            if not contract_id:
                return BaseResponse.error(error_code=1, message="partner_id, contract_id required")
            contract_records = request.env['welly.contract'].sudo().browse(int(contract_id))
            if not contract_records:
                return BaseResponse.error(error_code=1, message="Contract not found")
            # make response
            partner = contract_records.partner_id
            contact = contract_records.other_contact_id
            guardian = contract_records.guardian_id
            rs = {
                "partner": {
                    "partner_id": partner.id,
                    "partner_name": partner.name if partner.name else None,
                    "partner_code": partner.welly_code if partner.welly_code else None,
                    "partner_id_number": partner.partner_id_number if partner.partner_id_number else None,
                    "partner_birthdate": self.convert_date_format(
                        str(partner.birthdate)) if partner.birthdate else None,
                    "partner_gender": partner.gender if partner.gender else None,
                    "partner_email": partner.email if partner.email else None,
                    "partner_nationality": partner.nationality_id.name if partner.nationality_id.name else None,
                    "partner_phone": partner.phone if partner.phone else None,
                    "partner_address": partner.contact_address if partner.contact_address else None,
                },
                "contact": None,
                "guardian": None
            }
            if contact:
                rs['contact'] = {
                    "contact_name": contact.name if contact.name else None,
                    "contact_id_number": contact.partner_id_number if contact.partner_id_number else None,
                    "contact_address": contact.contact_address if contact.contact_address else None,
                    "contact_phone": contact.phone if contact.phone else None,
                    "contact_nationality": contact.nationality_id.name if contact.nationality_id.name else None
                }
            if guardian:
                rs['guardian'] = {
                    "guardian_name": guardian.name if guardian.name else None,
                    "guardian_id_number": guardian.nationality_id.name if guardian.nationality_id.name else None,
                    "guardian_address": guardian.contact_address if guardian.contact_address else None,
                    "guardian_phone": guardian.phone if guardian.phone else None,
                    "guardian_nationality": guardian.nationality_id.name if guardian.nationality_id.name else None
                }
            return BaseResponse.success(data=rs)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy thông tin quyền lợi hội viên theo hđ
    @http.route('/api/contract/getServiceBenefit', type='http', auth='public', methods=['POST'], csrf=False)
    def get_contract_service_benefit(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            contract_id = data.get('contract_id')
            if not contract_id:
                return BaseResponse.error(error_code=1, message="contract_id required")
            contract_records = request.env['welly.contract'].sudo().browse(int(contract_id))
            if not contract_records:
                return BaseResponse.error(error_code=1, message="Contract not found")
            # make response
            product_template = contract_records.sale_order_template_id.sale_order_template_line_ids
            pd = ''
            for line in product_template:
                product_note = line.product_id.welly_service_benefit or ''
                pd += product_note
            soup = BeautifulSoup(pd, 'html.parser')
            # Tìm tất cả các thẻ <li> và trích xuất nội dung của chúng
            result = [li.get_text() for li in soup.find_all('li')]
            return BaseResponse.success(data=result)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api danh sách thanh toán của khách hàng account.payment
    @http.route('/api/contract/getPartnerPayment', type='http', auth='public', methods=['POST'], csrf=False)
    def get_payment_partner(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)

            # lấy ra danh sách con của partner_id
            partner_rc = request.env['res.partner'].sudo().browse(int(partner_id))
            list_id_child = partner_rc.child_my_welly_id.ids
            list_id_child.append(int(partner_id))

            page = data.get('page', 1)
            size = data.get('size', 10)
            domain = [('partner_id', '=', list_id_child),
                      ('state', '=', 'posted')]

            # phân trang và tính tổng số tiền
            offset = (page - 1) * size
            total_records = request.env['account.payment'].sudo().search_count(domain)
            result = request.env['account.payment'].sudo().read_group(domain, ['amount'], [])
            if result and result[0]['amount'] is not None:
                total_amount = result[0]['amount']
            else:
                total_amount = 0
            total_page = (total_records + size - 1) // size
            index = {
                "page": page,
                "size": size,
                "total_page": total_page,
                "total_record": total_records,
                "total_payment": total_amount
            }

            payment_records = request.env['account.payment'].sudo().search(
                domain=domain,
                order='create_date desc',
                offset=offset,
                limit=size
            )
            if not payment_records:
                return BaseResponse.success(data=[])
            result = []
            for payment in payment_records:
                welly_payment = request.env['welly.payment.line'].sudo().search(
                    domain=[('payment_id', '=', int(payment.id))])
                account_move = welly_payment.welly_account_move_id
                rs = {
                    "receipt_id": account_move.id,
                    "receipt_payment_state": account_move.payment_state,
                    "account_move_code": account_move.name,
                    "service_name": account_move.sale_order_template_name_print if account_move.sale_order_template_name_print else None,
                    "amount": payment.amount,
                    "payment_id": welly_payment.id,
                    "payment_type": account_move.payment_type_welly if account_move.payment_type_welly else None,
                    "payment_method": welly_payment.payment_id.journal_id.name if welly_payment.payment_id.journal_id.name else None,
                    "remaining_amount": welly_payment.remaining_amount,
                    "next_payment_date": welly_payment.next_payment_date.strftime(
                        '%d/%m/%Y') if welly_payment.next_payment_date else '',
                    "time_payment": self._convert_utc_to_timestamp_milliseconds(payment.create_date)
                }
                result.append(rs)
            return BaseResponse.success(data=result, index=index)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api danh sách biên lai của khách hàng account.move
    @http.route('/api/contract/getPartnerReceipt', type='http', auth='public', methods=['POST'], csrf=False)
    def get_acount_move_partner(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            payment_state = data.get('payment_state')
            page = data.get('page', 1)
            size = data.get('size', 10)

            # lấy ra danh sách con của partner_id
            partner_rc = request.env['res.partner'].sudo().browse(int(partner_id))
            list_id_child = partner_rc.child_my_welly_id.ids
            list_id_child.append(int(partner_id))

            query_payment_state = (1, '=', 1)
            if payment_state:
                query_payment_state = ('payment_state', 'in', payment_state.split(','))
            domain = [
                ('partner_account_move_ids.partner_id', '=', list_id_child),
                ('approval_state', 'in', ['posted', 'approved']),
                ('journal_id', '=', 1),
                query_payment_state
            ]

            # phân trang và tính tổng số tiền
            offset = (page - 1) * size
            total_records = request.env['account.move'].sudo().search_count(domain)
            total_page = (total_records + size - 1) // size
            index = {
                "page": page,
                "size": size,
                "total_page": total_page,
                "total_record": total_records
            }

            move_records = request.env['account.move'].sudo().search(
                domain=domain,
                order='create_date desc',
                offset=offset,
                limit=size
            )
            if not move_records:
                return BaseResponse.success(data=[])
            result = []
            for move in move_records:
                welly_payment = request.env['welly.payment.line'].sudo().search([
                    ('welly_account_move_id', '=', int(move.id)),
                    ('payment_id.state', '=', 'posted')
                ], order='id DESC', limit=1)
                rs = {
                    "receipt_id": move.id,
                    "receipt_payment_state": move.payment_state,
                    "account_move_code": move.name,
                    "total_receipt_amount": move.pay_amount,
                    "service_name": move.sale_order_template_name_print if move.sale_order_template_name_print else None,
                    "payment_id": welly_payment.id if welly_payment.id else None,
                    "payment_type": move.payment_type_welly if move.payment_type_welly else None,
                    "payment_method": welly_payment.payment_id.journal_id.name if welly_payment.payment_id.journal_id.name else None,
                    "remaining_amount": welly_payment.remaining_amount,
                    "next_payment_date": welly_payment.next_payment_date.strftime(
                        '%d/%m/%Y') if welly_payment.next_payment_date else ''
                }
                result.append(rs)
            return BaseResponse.success(data=result, index=index)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy ra danh sách các dịch vụ welly.service.type
    @http.route('/api/getListServiceType', type='http', auth='public', methods=['POST'], csrf=False)
    def get_service_type_list(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            location_id = data.get('location_id')
            if not location_id:
                return BaseResponse.error(error_code=1, message="location_id required")
            location = request.env['welly.location'].sudo().search([('id', '=', location_id)])
            # Lấy danh sách các dịch vụ từ mô hình welly.service.type
            service_types = request.env['welly.service.type'].sudo().search(
                [('company_id', '=', location.company_id.id)]
            )

            # Tạo danh sách các dịch vụ để trả về
            services = [{'id': service.id, 'name': service.name} for service in service_types]

            return BaseResponse.success(data=services)

        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/get_countries', type='http', auth='public', methods=['POST'], csrf=False)
    def get_countries(self, **kwargs):
        try:
            country_model = http.request.env['res.country'].sudo()
            countries = country_model.search([])

            country_list = []
            for country in countries:
                country_data = {
                    'id': country.id,
                    'name': country.name,
                }
                country_list.append(country_data)

            response = {'countries': country_list}
            return BaseResponse.success(data=response)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api update wellyID với partner theo sdt
    @http.route('/api/partner/updateWellyId', type='http', auth='public', methods=['POST'], csrf=False)
    def update_welly_id_partner(self, **kwargs):
        try:
            partner_id, error = get_and_verify_jwt_from_header(is_update_welly_id=True)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            return BaseResponse.success(data=partner_id)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api update passcode đăng nhập mobile 3rd
    @http.route('/api/partner/updatePassCode', type='http', auth='public', methods=['POST'], csrf=False)
    def update_pass_code_partner(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)

            pass_code = data.get('pass_code')
            if not pass_code:
                return BaseResponse.error(error_code=1, message="pass_code required")
            # Kiểm tra xem pass_code có đúng 6 ký tự và chỉ chứa số không
            if len(str(pass_code)) != 6:
                return BaseResponse.error(error_code=400, message="Mã PIN phải là số 6 ký tự.")
            # parse pass_code to str
            pass_code = str(pass_code)
            partner_record = request.env['res.partner'].sudo().search(
                domain=[('id', '=', int(partner_id))]
            )
            # nếu đã có passcode từ trước có thì update chưa có thì tạo mới
            pc = partner_record._hash_pass_code(pass_code)
            if partner_record.mobile_pass_code:
                check_old_code = partner_record.check_mobile_pass_code(pass_code)
                # mã pin mới ko đc trùng với mã pin cũ
                if check_old_code:
                    return BaseResponse.error(error_code=400, message="Nhập mã PIN mới không trùng với mã PIN cũ.")
                else:
                    partner_record.update({'mobile_pass_code': pc})
            else:
                partner_record.update({'mobile_pass_code': pc})
            return BaseResponse.success(data=[])
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api reset passcode đăng nhập mobile 3rd
    @http.route('/api/partner/resetPassCode', type='http', auth='public', methods=['POST'], csrf=False)
    def reset_pass_code_partner(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)

            pass_code = data.get('pass_code')
            if not pass_code:
                return BaseResponse.error(error_code=1, message="pass_code required")
            # Kiểm tra xem pass_code có đúng 6 ký tự và chỉ chứa số không
            if len(str(pass_code)) != 6:
                return BaseResponse.error(error_code=400, message="Mã PIN phải là số 6 ký tự.")
            # parse pass_code to str
            pass_code = str(pass_code)
            partner_record = request.env['res.partner'].sudo().search(
                domain=[('id', '=', int(partner_id))]
            )
            pc = partner_record._hash_pass_code(pass_code)
            partner_record.update({'mobile_pass_code': pc})
            return BaseResponse.success(data=[])
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api check passcode đăng nhập mobile 3rd
    @http.route('/api/partner/checkPassCode', type='http', auth='public', methods=['POST'], csrf=False)
    def check_pass_code_partner(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            pass_code = data.get('pass_code')
            partner_record = request.env['res.partner'].sudo().search(
                domain=[('id', '=', int(partner_id))]
            )
            if partner_record.mobile_pass_code:
                if pass_code:
                    # Kiểm tra xem pass_code có đúng 6 ký tự và chỉ chứa số không
                    if len(str(pass_code)) != 6:
                        return BaseResponse.error(error_code=400, message="Mã PIN phải là số 6 ký tự.")
                    # parse pass_code to str
                    pass_code = str(pass_code)
                    if not partner_record.check_mobile_pass_code(pass_code):
                        return BaseResponse.error(error_code=400, message="Mã PIN không chính xác.")
                    else:
                        return BaseResponse.success(data=[])
                else:
                    return BaseResponse.error(error_code=400, message="Mã PIN không chính xác.")
            else:
                return BaseResponse.error(error_code=404, message="Mã PIN chưa được thiết lập.")
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy thông tin partner
    @http.route('/api/partner/getInfo', type='http', auth='public', methods=['POST'], csrf=False)
    def get_partner_info(self, **post):
        try:
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)

            partner = request.env['res.partner'].sudo().browse(int(partner_id))
            if not partner.exists():
                return BaseResponse.error(error_code=1, message="Partner not found")
            avatar = None

            base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
            image = request.env['ir.attachment'].sudo().search(
                [('res_model', '=', 'res.partner'), ('res_id', '=', partner_id),
                 ('res_field', '=', 'avatar_mywelly')], limit=1)
            if image:
                access_token = image.generate_access_token()[0]
                avatar = f"{base_url}/web/image/{image.id}/image_128?access_token={access_token}"

            response_data = {
                'display_name': partner.display_name or None,
                'avatar': avatar,
                'partner_id': partner_id,
                'welly_code': partner.welly_code or None,
                'phone': partner.phone or None,
                'email': partner.email or None,
                'partner_id_number': partner.partner_id_number or None,
                'birthdate': self.convert_date_format(str(partner.birthdate)) if partner.birthdate else None,
                'gender': partner.gender or None,
                'address': partner.street or None,
                'nationality': partner.nationality_id.name if partner.nationality_id else None,
                'fcm_token': partner.fcm_token if partner.fcm_token else ""
            }

            return BaseResponse.success(data=response_data)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api update thông tin partner
    @http.route('/api/partner/updateInfo', type='http', auth='public', methods=['POST'], csrf=False)
    def update_partner_info(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)

            display_name = data.get('display_name')
            phone = data.get('phone')
            email = data.get('email')
            partner_id_number = data.get('partner_id_number')
            birthdate = data.get('birthdate')
            gender = data.get('gender')
            nationality_id = data.get('nationality_id')
            street = data.get('address')
            fcm_token = data.get('fcm_token')
            is_deleted = data.get('is_deleted')

            partner_record = request.env['res.partner'].sudo().search(
                domain=[('id', '=', int(partner_id))]
            )

            update_partner = {}
            if display_name and display_name != partner_record.display_name:
                update_partner.update({'display_name': display_name})
                update_partner.update({'name': display_name})
            if phone and phone != partner_record.phone:
                update_partner.update({'phone': phone})
            if email and email != partner_record.email:
                update_partner.update({'email': email})
            if partner_id_number and partner_id_number != partner_record.partner_id_number:
                update_partner.update({'partner_id_number': partner_id_number})
            if birthdate:
                birthdate_format = "%d-%m-%Y"
                birthdate = datetime.strptime(birthdate, birthdate_format).date()
                update_partner.update({'birthdate': birthdate})
            if gender:
                update_partner.update({'gender': gender})
            if nationality_id:
                update_partner.update({'nationality_id': int(nationality_id)})
            if street:
                update_partner.update({'street': street})
            if fcm_token:
                update_partner.update({'fcm_token': fcm_token})
            if is_deleted and isinstance(is_deleted, bool):
                update_partner.update({'is_deleted_welly_id': is_deleted})
            partner_record.update(update_partner)
            return BaseResponse.success(data=[])
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api xóa fcm_token khi logout
    @http.route('/api/partner/deleteFcmToken', type='http', auth='public', methods=['POST'], csrf=False)
    def delete_fcm_token(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)

            fcm_token = data.get('fcm_token')
            if not fcm_token:
                return BaseResponse.error(error_code=1, message="fcm_token required")
            partner_record = request.env['res.partner'].sudo().browse(int(partner_id))
            tokens = partner_record.fcm_token.split(',')
            if tokens and fcm_token in tokens:
                tokens.remove(fcm_token)
                partner_record.write({'fcm_token': ','.join(tokens)})
            return BaseResponse.success(data=[])
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/partner/updateAvatar', type='http', auth='public', methods=['POST'], csrf=False)
    def update_avatar_partner(self, **post):
        try:
            # Xác thực JWT từ header và lấy partner_id
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)

            # Kiểm tra file được gửi lên
            if 'avatar_file' not in request.httprequest.files:
                return BaseResponse.error(error_code=1, message=str("avatar_file cần thiết"))

            # Lấy file object
            file_object = request.httprequest.files['avatar_file']
            if file_object.filename == '':
                return BaseResponse.error(error_code=1, message=str("file rỗng"))

            # Đọc nội dung file và mã hóa base64
            file_content = base64.b64encode(file_object.read())

            # Tìm bản ghi partner và cập nhật ảnh đại diện
            partner_record = request.env['res.partner'].sudo().search([('id', '=', int(partner_id))], limit=1)
            partner_record.avatar_mywelly = file_content
            return BaseResponse.success(data=[])
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api update device_id đăng nhập mobile 3rd
    @http.route('/api/partner/updateDeviceId', type='http', auth='public', methods=['POST'], csrf=False)
    def update_device_id_partner(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)

            device_id = str(data.get('device_id'))
            if not device_id:
                return BaseResponse.error(error_code=1, message="device_id required")
            partner_record = request.env['res.partner'].sudo().search(
                domain=[('id', '=', int(partner_id))]
            )
            partner_record.update({'device_id': device_id})
            return BaseResponse.success(data=[])
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api check device_id đăng nhập mobile 3rd
    @http.route('/api/partner/checkDeviceId', type='http', auth='public', methods=['POST'], csrf=False)
    def check_device_id_partner(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)

            device_id = str(data.get('device_id'))
            if not device_id:
                return BaseResponse.error(error_code=1, message="device_id required")
            partner_record = request.env['res.partner'].sudo().search(
                domain=[('id', '=', int(partner_id))]
            )
            if partner_record.device_id != device_id:
                return BaseResponse.error(error_code=400, message="device_id wrong")
            return BaseResponse.success(data=[])
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api Lấy list Thông báo của partner
    @http.route('/api/partner/getListNotification', type='http', auth='public', methods=['POST'], csrf=False)
    def get_list_notification_partner(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            # dữ liệu nhận vào client có thể có nhiều type phân cách bởi dấu , ví dụ: type: "training,event"
            type = data.get('type')
            page = data.get('page', 1)
            size = data.get('size', 10)

            noti_r = request.env['notification.queue'].sudo()

            # build query
            query_type = (1, '=', 1)
            if type:
                query_type = ('type', 'in', type.split(','))
            domain = [
                ('partner_id', '=', int(partner_id)),
                ('type', 'not in', ['training_telegram']),
                query_type
            ]
            # phân trang
            offset = (page - 1) * size
            total_records = noti_r.search_count(domain)
            total_page = (total_records + size - 1) // size
            # lấy tổng noti chưa đọc
            domain_unread = [
                ('partner_id', '=', int(partner_id)),
                ('type', 'not in', ['training_telegram']),
                ('is_read', '=', False)
            ]
            total_unread = noti_r.search_count(domain_unread)
            index = {
                "page": page,
                "size": size,
                "total_page": total_page,
                "total_record": total_records,
                "total_unread": total_unread
            }

            # query và lọc response
            notification_records = noti_r.search_read(
                domain=domain,
                fields=['id', 'title', 'content_summary', 'type', 'image_title', 'heading', 'content_detail',
                        'is_read', 'read_date', 'calendar_id', 'contract_id', 'create_date', 'partner_id'],
                order='create_date desc',
                offset=offset,
                limit=size
            )
            if not notification_records:
                return BaseResponse.success(data=[])

            # lọc response
            for record in notification_records:
                record['partner_id'] = record['partner_id'][0] if record['partner_id'] else None
                record['title'] = record['title'] or None
                record['content_summary'] = record['content_summary'] or None
                record['type'] = record['type'] or None
                record['image_title'] = record['image_title'] or None
                record['heading'] = record['heading'] or None
                record['content_detail'] = record['content_detail'] or None
                record['read_date'] = self._convert_utc_to_timestamp_milliseconds(record['read_date']) if record[
                    'read_date'] else None
                record['create_date'] = self._convert_utc_to_timestamp_milliseconds(record['create_date']) if record[
                    'create_date'] else None
                record['calendar_id'] = record['calendar_id'][0] if record['calendar_id'] else None
                record['contract_id'] = record['contract_id'][0] if record['contract_id'] else None
            return BaseResponse.success(data=notification_records, index=index)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy thông tin chi tiết Notification và update thành is_read
    @http.route('/api/partner/getNotificationDetail', type='http', auth='public', methods=['POST'], csrf=False)
    def get_notification_detail(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)

            notification_id = data.get('notification_id')
            if not notification_id:
                return BaseResponse.error(error_code=1, message="notification_id required")

            noti_r = request.env['notification.queue'].sudo().search(domain=[
                ('id', '=', int(notification_id)),
                ('partner_id', '=', int(partner_id))
            ])

            if not noti_r:
                return BaseResponse.error(error_code=1, message="Notification not found")

            response_data = {
                'title': noti_r.title or None,
                'content_summary': noti_r.content_summary or None,
                'type': noti_r.type or None,
                'image_title': noti_r.image_title or None,
                'heading': noti_r.heading or None,
                'content_detail': noti_r.content_detail or None,
                'is_read': noti_r.is_read,
                'read_date': self._convert_utc_to_timestamp_milliseconds(
                    noti_r.read_date) if noti_r.read_date else None,
                'create_date': self._convert_utc_to_timestamp_milliseconds(
                    noti_r.create_date) if noti_r.create_date else None,
                'calendar_id': noti_r.calendar_id.id if noti_r.calendar_id else None,
                'contract_id': noti_r.contract_id.id if noti_r.contract_id else None,
            }
            noti_r.update({'is_read': True,
                           'read_date': fields.Datetime.now()})
            return BaseResponse.success(data=response_data)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/book_pt/test', type='http', auth='public', methods=['POST'], csrf=False)
    def attendee_test(self, **kwargs):
        data = get_data_and_log_request(request)
        partner_id = data.get('partner_id')
        location_id = data.get('location_id')

        event = request.env['calendar.event']
        rs = event.check_booking(partner_id, location_id)

        return BaseResponse.success(data=rs)

    # api khách checkin tại quầy lễ tân
    @http.route('/api/book_pt/attendee_action', type='http', auth='public', methods=['POST'], csrf=False)
    def attendee_action(self, **kwargs):
        data = get_data_and_log_request(request)
        attendee_id = data.get('attendee_id')
        action = data.get('action')

        event_model = request.env['calendar.attendee']
        attendee = event_model.sudo().browse(int(attendee_id))

        new_state = ''
        if action == 'accept':
            new_state = 'accepted'

        if attendee and new_state:
            attendee.write({'state': new_state})

        return {'result': 'success', 'attendee_id': attendee_id, 'action': action}

    # api lấy ra List địa điểm tập
    @http.route('/api/book_pt/getListLocation', type='http', auth='public', methods=['POST'], csrf=False)
    def get_location_list(self, **kwargs):
        try:
            location = request.env['welly.location'].sudo().search_read(
                fields=['id', 'name', 'company_id']
            )
            base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
            rs = []
            for record in location:
                record['company_name'] = record['company_id'][1]
                record['company_id'] = record['company_id'][0]
                rs.append(record)
                if base_url == 'https://fitness.mywelly.vn':
                    pass
                else:
                    break  # dừng vòng for app đang chỉ muốn hiển thị cho một phòng tập
            return BaseResponse.success(data=rs)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy ra List hợp đồng của khách và các hợp đồng của chủ thẻ phụ
    @http.route('/api/book_pt/getListContractActive', type='http', auth='public', methods=['POST'], csrf=False)
    def get_contract_list(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)

            location_id = data.get('location_id')
            if not location_id:
                return BaseResponse.error(error_code=1, message="location_id required")
            # lấy ra danh sách con của partner_id
            partner_rc = request.env['res.partner'].sudo().browse(int(partner_id))
            list_id_child = partner_rc.child_my_welly_id.ids
            list_id_child.append(int(partner_id))

            contract_records = request.env['welly.contract'].sudo().search_read(
                domain=[('partner_account_ids', 'in', list_id_child),
                        ('welly_location_many2_many', 'in', int(location_id)),
                        ('service_type', '=', 'pt'),
                        ('state', '=', 'activated'),
                        ('available_session_number', '>', 0),
                        ('date_end', '>', fields.Datetime.now())],
                fields=['sale_order_template_name_print', 'available_session_number', 'exercise_form_id',
                        'welly_service_checkin_time_from', 'welly_service_checkin_time_to',
                        'free_session_number', 'coach_id', 'partner_account_ids']
            )
            for record in contract_records:
                record['service_checkin_time_from'] = record.pop('welly_service_checkin_time_from', None)
                record['service_checkin_time_to'] = record.pop('welly_service_checkin_time_to', None)
                record['service_name'] = record.pop('sale_order_template_name_print', None)
                record['exercise_form_name'] = None
                record['limit_participants'] = None
                if record['exercise_form_id']:
                    record['limit_participants'] = request.env['welly.exercise.form'].sudo().browse(
                        record['exercise_form_id'][0]).limit_participants
                    record['exercise_form_name'] = record['exercise_form_id'][1]
                record.pop('exercise_form_id', None)
                total_available_session_number = record['available_session_number'] + record['free_session_number']
                record['available_session_number'] = total_available_session_number
                record['coach'] = None
                if record['coach_id']:
                    record['coach'] = record['coach_id'][1]
                record.pop('coach_id', None)
                # lấy ra danh sách người sử dụng dịch vụ
                contract_partners = []
                if record['partner_account_ids']:
                    for partner_id in record['partner_account_ids']:
                        partner_info = request.env['res.partner'].sudo().browse(int(partner_id))
                        avatar = None
                        base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
                        image = request.env['ir.attachment'].sudo().search(
                            [('res_model', '=', 'res.partner'), ('res_id', '=', partner_info.id),
                             ('res_field', '=', 'avatar_mywelly')], limit=1)
                        if image:
                            access_token = image.generate_access_token()[0]
                            avatar = f"{base_url}/web/image/{image.id}/image_128?access_token={access_token}"
                        contract_partners.append({'id': partner_info.id, 'name': partner_info.display_name, 'avatar': avatar})
                record['contract_partners'] = contract_partners
                record.pop('partner_account_ids', None)
            return BaseResponse.success(data=contract_records)
        except Exception as e:
            logging.error(e)
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy ra danh sách hlv theo hợp đồng
    @http.route('/api/book_pt/getListCoachByContract', type='http', auth='public', methods=['POST'], csrf=False)
    def get_list_coach_by_contract(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            contract_id = data.get('contract_id')
            if not contract_id:
                return BaseResponse.error(error_code=1, message="contract_id required")
            contract_rc = request.env['welly.contract'].sudo().browse(int(contract_id))
            if not contract_rc:
                return BaseResponse.error(error_code=1, message="contract not exist")
            service_names = contract_rc.welly_service_ids.mapped('name')
            domain = []
            for service in service_names:
                # Thêm điều kiện tìm kiếm cho mỗi tên dịch vụ
                domain.append(('category_ids.name', 'ilike', service))
            # Thêm các toán tử '|' vào domain
            if len(domain) > 1:
                full_domain = ['|'] * (len(domain) - 1) + domain
            else:
                full_domain = domain
            # Truy vấn thông tin từ model welly.pt.management dựa trên service_names
            list_pt = request.env['welly.pt.management'].sudo().search(full_domain)
            rs = []
            if list_pt:
                for i in list_pt:
                    image = None
                    base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
                    image_rc = request.env['ir.attachment'].sudo().search(
                        [('res_model', '=', 'res.partner'), ('res_id', '=', i.user_id.partner_id.id),
                         ('res_field', '=', 'image_128')], limit=1)
                    if image_rc:
                        access_token = image_rc.generate_access_token()[0]
                        image = f"{base_url}/web/image/{image_rc.id}/image_128?access_token={access_token}"

                    coach_phone = None
                    employee = request.env['hr.employee'].sudo().search([('pt_manager_id', '=', i.id)])
                    if employee and employee.mobile_phone:
                        coach_phone = employee.mobile_phone
                    rc = {
                        'id': i.user_id.id,
                        'name': i.user_id.name,
                        'category_ids': i.category_ids.mapped('name'),
                        'image': image,
                        'phone': coach_phone
                    }
                    rs.append(rc)
            return BaseResponse.success(data=rs)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy ra lịch tập theo hợp đồng và ngày đối với lịch - nhóm, lấy ra cả lịch của khách đã hủy nhưng lịch vẫn tồn tại
    @http.route('/api/book_pt/getCalendarActive', type='http', auth='public', methods=['POST'], csrf=False)
    def get_calendar_active(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)

            contract_id = data.get('contract_id')
            start_time = data.get('start')
            stop_time = data.get('stop')
            session_type = data.get('session_type')
            partner_id = data.get('partner_id')
            if not contract_id:
                return BaseResponse.error(error_code=1, message="contract_id required")
            if not partner_id or not isinstance(partner_id, list):
                return BaseResponse.error(error_code=1, message="partner_id must be a non-empty list")

            contract_records = request.env['welly.contract'].sudo().browse(int(contract_id))
            if not contract_records:
                return BaseResponse.error(error_code=1, message="contract_records not exits")
            # query builder
            contract_pt_id = contract_records.coach_id.id
            if contract_pt_id:
                domain = [('event_type', '=', 'pt'),
                          ('state', '=', 'draft'),
                          '|',
                          '&',
                          ('pt_id', '=', int(contract_pt_id)),
                          ('pt_id_substitute', '=', False),
                          ('pt_id_substitute', '=', int(contract_pt_id)),
                          ('exercise_id', '=', contract_records.exercise_form_id.id),
                          ('start', '>=', self.convert_timestamp_milliseconds_to_utc(start_time)),
                          ('start', '<=', self.convert_timestamp_milliseconds_to_utc(stop_time))]
            else:
                domain = [('event_type', '=', 'pt'),
                          ('state', '=', 'draft'),
                          ('exercise_id', '=', contract_records.exercise_form_id.id),
                          ('start', '>=', self.convert_timestamp_milliseconds_to_utc(start_time)),
                          ('start', '<=', self.convert_timestamp_milliseconds_to_utc(stop_time))]
            if session_type and int(session_type) > 0:
                domain.append(('session_type', '=', session_type))
            calendar_records = request.env['calendar.event'].sudo().search_read(
                domain=domain,
                fields=['id', 'start', 'stop', 'attendee_ids', 'exercise_id', 'pt_id', 'pt_id_substitute',
                        'service_id'],
                order='start asc'
            )
            for record in calendar_records:
                record['start'] = self._convert_utc_to_timestamp_milliseconds((record['start']))
                record['stop'] = self._convert_utc_to_timestamp_milliseconds((record['stop']))
                record['service'] = None
                if record['service_id']:
                    record['service'] = record['service_id'][1]
                record.pop('service_id', None)
                record['coach_name'] = None
                if record['pt_id_substitute']:
                    record['coach_name'] = record['pt_id_substitute'][1]
                elif record['pt_id']:
                    record['coach_name'] = record['pt_id'][1]
                record.pop('pt_id_substitute', None)
                record.pop('pt_id', None)
                limit_participants = request.env['welly.exercise.form'].sudo().browse(
                    record['exercise_id'][0]).limit_participants
                attendees = request.env['calendar.attendee'].sudo().search([
                    ('event_id', '=', record['id'])])
                accepted_attendee = attendees.filtered(lambda attendee: attendee.state in ['accepted', 'tentative', 'await_pt_confirm'])
                partners = []
                for pid in partner_id:
                    # Tìm attendee nào có partner_id khớp với giá trị pid
                    matched_attendee = attendees.filtered(lambda attendee: attendee.partner_id.id == pid)
                    if matched_attendee:
                        attendee = matched_attendee[0]  # Lấy attendee đầu tiên phù hợp
                        is_in_calendar = True
                        message = 'Bạn đã ở trong ca tập!'
                        # Các điều kiện khác nhau dựa trên trạng thái của attendee
                        if attendee.state == 'needsAction':
                            message = 'Bạn đã ở trong ca tập. Cần có hành động!'
                        elif attendee.state == 'tentative':
                            message = 'Lịch đang chờ bạn xác nhận!'
                        elif attendee.state == 'await_pt_confirm':
                            message = 'Lịch đang chờ HLV xác nhận!'
                        elif attendee.state == 'accepted' and attendee.is_checked_in:
                            message = 'Bạn đã xác nhận và Check-In thành công buổi tập!'
                        elif attendee.state == 'accepted' and not attendee.is_checked_in:
                            message = 'Bạn đã xác nhận buổi tập nhưng chưa Check-In!'
                        elif attendee.state == 'declined':
                            is_in_calendar = False
                            message = 'Lịch đã bị từ chối!'
                        result_info = {
                            'partner_name': attendee.partner_id.display_name,
                            'is_in_calendar': is_in_calendar,
                            'message': message
                        }
                    else:
                        # Không tìm thấy attendee khớp với partner_id
                        result_info = {
                            'partner_name': request.env['res.partner'].sudo().browse(pid).display_name,
                            'is_in_calendar': False,
                            'message': None
                        }
                    partners.append(result_info)
                record['partners'] = partners
                record['available_slot'] = limit_participants - len(accepted_attendee)
                record.pop('exercise_id', None)
                record.pop('attendee_ids', None)
            return BaseResponse.success(data=calendar_records)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api đăng ký vào lớp đã tạo sẵn
    @http.route('/api/book_pt/createAttendee', type='http', auth='public', methods=['POST'], csrf=False)
    def create_attendee(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            event_id = data.get('calendar_id')
            contract_id = data.get('contract_id')
            note = data.get('note')
            partner_id = data.get('partner_id')
            if not event_id or not contract_id:
                return BaseResponse.error(error_code=1, message="calendar_id, contract_id required")
            if not partner_id or not isinstance(partner_id, list):
                return BaseResponse.error(error_code=1, message="partner_id must be a non-empty list")
            notification_q = request.env['notification.queue'].sudo()
            new_notis = []
            for pid in partner_id:
                existing_attendee = request.env['calendar.attendee'].sudo().search([
                    ('partner_id', '=', pid),
                    ('event_id', '=', event_id),
                ], limit=1)
                if not existing_attendee:
                    event = request.env['calendar.event'].sudo().browse(int(event_id))
                    event.write({
                        'partner_ids': [(4, int(pid))]
                    })
                    contract_records = request.env['welly.contract'].sudo().browse(int(contract_id))
                    attendee = request.env['calendar.attendee'].sudo().search([
                        ('partner_id', '=', int(pid)),
                        ('event_id', '=', int(event_id)),
                    ], order='id ASC', limit=1)
                    total_ss = contract_records.session_number
                    available_session_number = contract_records.available_session_number
                    session_number = total_ss - available_session_number + 1
                    if attendee:
                        attendee.write({
                            'note': note,
                            'state': 'await_pt_confirm',
                            'contract_id': contract_records.id,
                            'session_number': session_number
                        })

                    # tạo thông báo qua firebase cho PT
                    new_noti = notification_q.create({
                        'partner_id': attendee.event_id.pt_id.partner_id.id,
                        'title': "Xác nhận lịch tập PT",
                        'content_summary': f'{attendee.partner_id.name} đã tham gia lịch PT, hãy xác nhận! '
                                           f'Ca tập {self._convert_date_format(str(event.start + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")} - {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")}'
                                           f' {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%d/%m/%Y")}',
                        'type': 'training',
                        'calendar_id': event.id
                    })
                else:
                    exist_attendee = existing_attendee[0]
                    if exist_attendee.state in ['partner_declined', 'declined']:
                        exist_attendee.write({
                            'note': note,
                            'state': 'tentative',
                            'write_date': fields.Datetime.now()
                        })
                        new_noti = notification_q.create({
                            'partner_id': exist_attendee.event_id.pt_id.partner_id.id,
                            'title': "Xác nhận lịch tập PT",
                            'content_summary': f'{exist_attendee.partner_id.name} đã tham gia lịch PT, hãy xác nhận! '
                                               f'Ca tập {self._convert_date_format(str(exist_attendee.event_id.start + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")} - {self._convert_date_format(str(exist_attendee.event_id.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")}'
                                               f' {self._convert_date_format(str(exist_attendee.event_id.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%d/%m/%Y")}',
                            'type': 'training',
                            'calendar_id': exist_attendee.event_id.id
                        })
                    else:
                        return BaseResponse.error(error_code=1, message=f"{exist_attendee.partner_id.display_name} đã trong lớp học")
                new_notis.append(new_noti)
            for noti in new_notis:
                notification_q.send_notification_to_firebase(noti)
            return BaseResponse.success(data=event_id)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api tạo mới lịch tập 1:1
    @http.route('/api/book_pt/getLocationInContract', type='http', auth='public', methods=['POST'], csrf=False)
    def get_welly_location_in_contract(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            contract_id = data.get('contract_id')
            if not contract_id:
                return BaseResponse.error(error_code=1,
                                          message="contract_id required")
            contract_records = request.env['welly.contract'].sudo().browse(int(contract_id))
            if not contract_records:
                return BaseResponse.error(error_code=1, message="contract_records not exits")
            rs = []
            if contract_records.welly_location_many2_many:
                for l in contract_records.welly_location_many2_many:
                    location = {
                        'location_id': l.id,
                        'name': l.name,
                    }
                    rs.append(location)
            return BaseResponse.success(data=rs)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api tạo mới lịch tập 1:1
    @http.route('/api/book_pt/createCalendar', type='http', auth='public', methods=['POST'], csrf=False)
    def create_calendar(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            contract_id = data.get('contract_id')
            start_time = data.get('start')
            stop_time = data.get('stop')
            note = data.get('note')
            session_type = data.get('session_type')
            location_id = data.get('location_id')
            coach_id = data.get('coach_id')
            partner_id = data.get('partner_id')
            if not contract_id or not start_time or not stop_time or not partner_id:
                return BaseResponse.error(error_code=1,
                                          message="contract_id, partner_id, start_time, stop_time required")
            contract_records = request.env['welly.contract'].sudo().browse(int(contract_id))
            if not contract_records:
                return BaseResponse.error(error_code=1, message="contract_records not exits")
            # Create calendar event
            start_utc = self.convert_timestamp_milliseconds_to_utc(start_time)
            stop_utc = self.convert_timestamp_milliseconds_to_utc(stop_time)

            start_utc7 = (start_utc + timedelta(hours=7)).strftime('%H:%M')
            stop_utc7 = (stop_utc + timedelta(hours=7)).strftime('%H:%M')
            start_utc7_float = float(start_utc7[:2]) + float(start_utc7[3:5]) / 60
            stop_utc7_float = float(stop_utc7[:2]) + float(stop_utc7[3:5]) / 60
            checkin_time_from = contract_records.checkin_time_from
            checkin_time_to = contract_records.checkin_time_to

            if start_utc7_float < checkin_time_from or stop_utc7_float > checkin_time_to:
                return BaseResponse.error(error_code=1, message="Thời gian đặt lịch ngoài khung giờ hợp lệ của hợp đồng")

            start_time_utc = fields.Datetime.to_string(start_utc)
            stop_time_utc = fields.Datetime.to_string(stop_utc)

            if coach_id:
                pt_id = coach_id
            else:
                pt_id = contract_records.coach_id.id

            event = request.env['calendar.event'].sudo().create_calendar_pt(partner_id,
                                                                            start_time_utc,
                                                                            stop_time_utc,
                                                                            note,
                                                                            session_type,
                                                                            pt_id,
                                                                            contract_records.exercise_form_id.id,
                                                                            contract_records.welly_service_ids[0].id,
                                                                            location_id,
                                                                            contract_records.id)

            attendee = request.env['calendar.attendee'].sudo().search([
                ('partner_id', '=', int(partner_id)),
                ('event_id', '=', int(event.id)),
            ], order='id ASC', limit=1)

            if attendee:
                attendee.write({
                    'note': note,
                    'state': 'await_pt_confirm',
                    'partner_action': partner_id,
                    'contract_id': contract_records.id
                })

            # tạo thông báo qua firebase cho PT
            notification_q = request.env['notification.queue'].sudo()
            new_noti = notification_q.create({
                'partner_id': attendee.event_id.pt_id.partner_id.id,
                'title': "Xác nhận lịch tập PT",
                'content_summary': f'{attendee.partner_id.name} đã tạo lịch PT, hãy xác nhận! '
                                   f'Ca tập {self._convert_date_format(str(event.start + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")} - {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")}'
                                   f' {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%d/%m/%Y")}',
                'type': 'training',
                'calendar_id': event.id
            })
            notification_q.send_notification_to_firebase(new_noti)
            return BaseResponse.success(data=event.id)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api hủy tham gia lịch tập
    @http.route('/api/book_pt/cancelAttendCalendar', type='http', auth='public', methods=['POST'], csrf=False)
    def cancel_attend_calendar(self, **kwargs):
        try:
            current_time = fields.Datetime.now()
            data = get_data_and_log_request(request)
            partner, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            event_id = data.get('calendar_id')
            partner_id = data.get('partner_id')
            note = data.get('note')
            if not event_id or not partner_id:
                return BaseResponse.error(error_code=1, message="calendar_id, partner_id required")
            attendee = request.env['calendar.attendee'].sudo().search([('event_id', '=', int(event_id)),
                                                                       ('partner_id', '=', int(partner_id))])
            event = request.env['calendar.event'].sudo().search(
                [('id', '=', int(event_id)), ('partner_ids', 'in', [int(partner_id)])])
            if not attendee or not event:
                return BaseResponse.error(error_code=1, message="Lịch không tồn tại, hoặc bạn không có trong lịch này.")

            time_can_cancel = event.company_id.time_partner_can_decline_booking_app_mywelly
            # # rule: chỉ có thể hủy lịch tập trước time diễn ra buổi tập
            # if (event.start - current_time) < timedelta(minutes=time_can_cancel):
            #     return BaseResponse.error(error_code=1, message= f"Lịch tập chỉ có thể hủy trước buổi tập ít nhất {time_can_cancel} phút")
            if not (event.state == 'draft' and attendee.state in ['needsAction', 'tentative', 'await_pt_confirm'] and attendee.is_checked_in == False):
                return BaseResponse.error(error_code=1, message= f"Chỉ được hủy lịch khi trạng thái là chờ xác nhận và bạn chưa checkin ở CLB!")
            attendee.partner_decline_button(partner_action=int(partner_id))
            attendee.declined_note = note
            # tạo thông báo qua firebase cho PT
            if event.pt_id_substitute:
                id_notify = event.pt_id_substitute.partner_id.id
            else:
                id_notify = event.pt_id.partner_id.id
            notification_q = request.env['notification.queue'].sudo()
            new_noti = notification_q.create({
                'partner_id': id_notify,
                'title': f'{attendee.partner_id.name} từ chối tham gia lịch PT',
                'content_summary': f'{attendee.partner_id.name} đã từ chối tham gia lịch tập. '
                                   f'Ca tập {self._convert_date_format(str(event.start + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")} - {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")}'
                                   f' {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%d/%m/%Y")}',
                'type': 'training',
                'calendar_id': event.id
            })
            notification_q.send_notification_to_firebase(new_noti)
            return BaseResponse.success(data=True)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api khách xác nhận tham gia lịch tập
    @http.route('/api/book_pt/acceptAttendCalendar', type='http', auth='public', methods=['POST'], csrf=False)
    def accept_attend_calendar(self, **kwargs):
        try:
            current_time = fields.Datetime.now()
            data = get_data_and_log_request(request)
            partner, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            event_id = data.get('calendar_id')
            partner_id = data.get('partner_id')
            note = data.get('note')
            if not event_id or not partner_id:
                return BaseResponse.error(error_code=1, message="calendar_id, partner_id required")
            attendee = request.env['calendar.attendee'].sudo().search([('event_id', '=', int(event_id)),
                                                                       ('partner_id', '=', int(partner_id))])
            event = request.env['calendar.event'].sudo().search(
                [('id', '=', int(event_id)), ('partner_ids', 'in', [int(partner_id)])])
            if not attendee or not event:
                return BaseResponse.error(error_code=1, message="Lịch không tồn tại, hoặc bạn không có trong lịch này.")

            # Quá thời gian bắt đầu lịch thì không thể xác nhận
            if (current_time - event.start) > timedelta(minutes=0):
                return BaseResponse.error(error_code=1, message="Lịch đã bắt đầu, không thể xác nhận tham gia!")

            # if attendee.state not in ['needsAction', 'tentative']:
            #     return BaseResponse.error(error_code=1, message="Khách hàng có trạng thái khác [Cần có tác vụ, Chờ khách hàng xác nhận], không thể xác nhận tham gia!")
            #
            # # TH lịch: nháp, khách: needAction -> Chờ PT xác nhận
            # if attendee.state == 'needsAction':
            #     attendee.write({
            #         'state': 'await_pt_confirm',
            #         'partner_action': int(partner_id)
            #     })
            # # TH lịch: nháp, khách: Chờ KH xác nhận -> Đã xác nhận
            # if attendee.state == 'tentative':
            #     attendee.write({
            #         'state': 'accepted',
            #         'partner_action': int(partner_id)
            #     })
            attendee.partner_confirm_button(partner_action=int(partner_id))

            # tạo thông báo qua firebase cho PT
            if event.pt_id_substitute:
                id_notify = event.pt_id_substitute.partner_id.id
            else:
                id_notify = event.pt_id.partner_id.id
            notification_q = request.env['notification.queue'].sudo()
            new_noti = notification_q.create({
                'partner_id': id_notify,
                'title': f'{attendee.partner_id.name} xác nhận tham gia lịch PT',
                'content_summary': f'{attendee.partner_id.name} đã xác nhận tham gia lịch tập. '
                                   f'Ca tập {self._convert_date_format(str(event.start + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")} - {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")}'
                                   f' {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%d/%m/%Y")}',
                'type': 'training',
                'calendar_id': event.id
            })
            notification_q.send_notification_to_firebase(new_noti)
            return BaseResponse.success(data=True)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api xác nhận hủy khi hlv hủy lịch
    @http.route('/api/book_pt/confirmPtDeclined', type='http', auth='public', methods=['POST'], csrf=False)
    def confirm_pt_declined(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            event_id = data.get('calendar_id')
            if not event_id:
                return BaseResponse.error(error_code=1, message="calendar_id required")
            attendee = request.env['calendar.attendee'].sudo().search([('event_id', '=', int(event_id)),
                                                                       ('partner_id', '=', int(partner_id))])
            if not attendee:
                return BaseResponse.error(error_code=1, message="Lịch không tồn tại, hoặc bạn không có trong lịch này.")
            if attendee.state == 'declined':
                attendee.write({
                    'partner_action': partner_id,
                })
            else:
                attendee.write({
                    'state': 'declined',
                    'partner_action': partner_id,
                })
            event = attendee.event_id
            # tạo thông báo qua firebase cho PT
            if event.pt_id_substitute:
                id_notify = event.pt_id_substitute.partner_id.id
            else:
                id_notify = event.pt_id.partner_id.id
            notification_q = request.env['notification.queue'].sudo()
            new_noti = notification_q.create({
                'partner_id': id_notify,
                'title': f'{attendee.partner_id.name} đồng ý hủy lịch PT',
                'content_summary': f'{attendee.partner_id.name} đã đồng ý cho bạn hủy lịch tập. '
                                   f'Ca tập {self._convert_date_format(str(event.start + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")} - {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")}'
                                   f' {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%d/%m/%Y")}',
                'type': 'training',
                'calendar_id': event.id
            })
            notification_q.send_notification_to_firebase(new_noti)
            return BaseResponse.success(data=True)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # API lấy ra lịch tập của partner
    @http.route('/api/book_pt/getCalendarByPartner', type='http', auth='public', methods=['POST'], csrf=False)
    def get_calendar_partner(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            state = data.get('state')
            start_time = data.get('start')
            stop_time = data.get('stop')
            page = data.get('page', 1)
            size = data.get('size', 10)
            order = data.get('order', 'start ASC')

            # lấy ra danh sách con của partner_id
            partner_rc = request.env['res.partner'].sudo().browse(int(partner_id))
            list_id_child = partner_rc.child_my_welly_id.ids
            list_id_child.append(int(partner_id))

            # Tổng hợp state
            # Chờ xác nhận
            if state == 'draft':
                attendee_state_condition = "AND (ca.state IN ('needsAction', 'tentative', 'await_pt_confirm'))"
                event_state_condition = "AND (ce.state IN ('draft', 'await_confirm'))"
            # Chờ tập
            elif state == 'pt_confirm':
                attendee_state_condition = "AND (ca.state IN ('accepted'))"
                event_state_condition = "AND (ce.state IN ('draft'))"
            # Khách đã đến
            elif state == 'accepted':
                attendee_state_condition = "AND (ca.is_checked_in = True) AND (ca.state = 'accepted')"
                event_state_condition = "AND (ce.state IN ('accepted'))"
            # Đang tập
            elif state == 'during_practice':
                attendee_state_condition = "AND (ca.state IN ('accepted', 'during_practice'))"
                event_state_condition = "AND (ce.state IN ('during_practice'))"
            # Hoàn thành
            elif state == 'done':
                attendee_state_condition = "AND (ca.state IN ('done', 'cheat_commission'))"
                event_state_condition = "AND (ce.state IN ('done'))"
            # PT hủy
            elif state == 'declined':
                attendee_state_condition = "AND (ca.state == 'declined') AND (ca.partner_action = ce.pt_id OR ca.partner_action = ce.pt_id_substitute)"
                event_state_condition = ""
            # Khách hủy
            elif state == 'partner_declined':
                attendee_state_condition = "AND (ca.state == 'declined') AND (ca.partner_action = ca.partner_id)"
                event_state_condition = ""
            # Hủy
            elif state == 'cancel':
                attendee_state_condition = "AND (ca.state IN ('declined'))"
                event_state_condition = ""
            # Hoàn buổi tập
            elif state == 'refund_session':
                attendee_state_condition = "AND (ca.state IN ('refund_session'))"
                event_state_condition = ""
            # Khách bỏ tập
            elif state == 'partner_skip_session':
                attendee_state_condition = "AND (ca.state IN ('partner_skip_session'))"
                event_state_condition = ""
            elif state == "" or not state:
                attendee_state_condition = ""
                event_state_condition = ""

            # build query
            start_condition = ""
            stop_condition = ""
            if start_time:
                start = self.convert_timestamp_milliseconds_to_utc(start_time)
                start_condition = f"AND (ce.start >= '{start}')"
            if stop_time:
                stop = self.convert_timestamp_milliseconds_to_utc(stop_time)
                stop_condition = f"AND (ce.start <= '{stop}')"

            # phân trang
            count_query = f"""
                SELECT COUNT(ce.id) as total_records
                FROM calendar_event ce
                LEFT JOIN calendar_attendee ca ON ca.event_id = ce.id
                WHERE ce.event_type = 'pt'
                  AND ca.partner_id in %s
                  {event_state_condition}
                  {attendee_state_condition}
                  {start_condition}
                  {stop_condition}
            """
            request.env.cr.execute(count_query, (tuple(list_id_child),))
            total_records_result = request.env.cr.dictfetchone()
            total_records = total_records_result.get('total_records', 0)
            total_page = (total_records + size - 1) // size
            index = {
                "page": page,
                "size": size,
                "total_page": total_page,
                "total_record": total_records
            }

            # query và lọc response
            sql_query = f"""
                        SELECT ce.id,
                                ce.is_past_booking,
                                ce.name,
                                ce.notes,
                                ce.start,
                                ce.stop,
                                ce.state,
                                ce.pt_id as coach_id,
                                wef.name as exercise_form_name,
                                st.name as session_type,
                                wl.name as location,
                                ca.partner_id as partner_id
                        FROM calendar_event ce
                            LEFT JOIN calendar_attendee ca ON ca.event_id = ce.id
                            LEFT JOIN calendar_event_class_type st ON st.id = ce.session_type
                            LEFT JOIN welly_location wl ON wl.id = ce.location_id
                            LEFT JOIN welly_exercise_form wef ON wef.id = ce.exercise_id
                        WHERE ce.event_type = 'pt'
                          AND ca.partner_id in %s
                          {event_state_condition}
                          {attendee_state_condition}
                          {start_condition}
                          {stop_condition}
                        ORDER BY ce.{order}
                        OFFSET %s
                        LIMIT %s
                    """

            offset = (page - 1) * size
            request.env.cr.execute(sql_query,
                                   (tuple(list_id_child), offset, size))
            calendar_records = request.env.cr.dictfetchall()
            for record in calendar_records:
                record['start'] = self._convert_utc_to_timestamp_milliseconds((record['start']))
                record['stop'] = self._convert_utc_to_timestamp_milliseconds((record['stop']))
                record['note'] = record['notes'] or None
                record.pop('notes', None)

                record['is_past_booking'] = record['is_past_booking'] or False

                record['calendar_state'] = record['state'] or None
                record.pop('state', None)

                record['coach_name'] = None
                record['coach_phone'] = None
                record['coach_avg_rate'] = None
                record['coach_image'] = None
                if record['coach_id']:
                    pt = request.env['welly.pt.management'].sudo().search([('user_id', '=', int(record['coach_id']))])
                    if pt:
                        record['coach_avg_rate'] = pt.rating_avg
                        employee = request.env['hr.employee'].sudo().search([('pt_manager_id', '=', pt.id)])
                        if employee and employee.mobile_phone:
                            record['coach_phone'] = employee.mobile_phone
                    record['coach_image'] = None
                    user = request.env['res.users'].sudo().browse(int(record['coach_id']))
                    record['coach_name'] = user.name
                    base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
                    image = request.env['ir.attachment'].sudo().search(
                        [('res_model', '=', 'res.partner'),
                         ('res_id', '=', user.partner_id.id),
                         ('res_field', '=', 'image_128')], limit=1)
                    if image:
                        access_token = image.generate_access_token()[0]
                        record['coach_image'] = f"{base_url}/web/image/{image.id}/image_128?access_token={access_token}"

                # kiểm tra partner đã đánh giá buổi tập chưa
                record['rate_id'] = None
                record['rate_msg'] = None
                record['rate_rating'] = None
                rating = request.env['welly.rating'].sudo().search([('event_id', '=', int(record['id'])),
                                                                    ('partner_id', '=', int(record['partner_id']))])
                if rating:
                    record['rate_id'] = rating[0].id
                    record['rate_msg'] = rating[0].message
                    record['rate_rating'] = rating[0].rating
                # xử lý trạng thái của lịch tập
                attendee_rc = request.env['calendar.attendee'].sudo().search([('event_id', '=', int(record['id'])),
                                                                              ('partner_id', '=', int(record['partner_id']))])
                contracts = attendee_rc.contract_id
                record['contract_total_session_number'] = None
                record['contract_available_session_number'] = None
                record['contract_free_session_number'] = None
                record['contract_session_number'] = None
                record['contract_name'] = None
                record['contract_id'] = None
                record['contract_service_name'] = None
                record['contract_date_start'] = None
                record['contract_date_end'] = None
                record['is_pt_booked'] = False
                if contracts:
                    record[
                        'contract_available_session_number'] = contracts.available_session_number + contracts.free_session_number
                    record['contract_free_session_number'] = contracts.free_session_number
                    total_ss = contracts.session_number + contracts.total_free_session_number
                    record['contract_session_number'] = attendee_rc.session_number
                    record['contract_total_session_number'] = total_ss
                    if contracts.service_type == 'member':
                        record['contract_total_session_number'] = contracts.total_free_session_number if contracts.total_free_session_number else None
                        record['contract_available_session_number'] = contracts.free_session_number if contracts.free_session_number else None
                    record['contract_name'] = contracts.name
                    record['contract_id'] = contracts.id
                    record['contract_service_name'] = contracts.sale_order_template_name_print if contracts.sale_order_template_name_print else None
                    record['contract_date_start'] = str(contracts.date_start)
                    if contracts.date_end:
                        record['contract_date_end'] = str(contracts.date_end)
                    else:
                        record['contract_date_end'] = None

                attendee_state = attendee_rc.state
                attendee_is_checked_in = attendee_rc.is_checked_in
                calendar_state = record['calendar_state']

                if attendee_state in ['needsAction', 'tentative', 'await_pt_confirm'] and calendar_state in ['draft', 'await_confirm']:
                    record['calendar_state'] = 'draft'
                if attendee_state == 'accepted' and calendar_state == 'draft':
                    record['calendar_state'] = 'pt_confirm'
                if attendee_is_checked_in and calendar_state == 'accepted' and attendee_state in ['accepted', 'tentative']:
                    record['calendar_state'] = 'accepted'
                if attendee_state in ['during_practice', 'accepted'] and calendar_state == 'during_practice':
                    record['calendar_state'] = 'during_practice'
                if calendar_state in ['done'] and attendee_state in ['done', 'cheat_commission']:
                    record['calendar_state'] = 'done'
                if attendee_state == 'declined' and (attendee_rc.partner_action == attendee_rc.event_id.pt_id or attendee_rc.partner_action == attendee_rc.event_id.pt_id_substitute):
                    record['calendar_state'] = 'declined'
                if attendee_state == 'declined' and attendee_rc.partner_action == attendee_rc.partner_id:
                    record['calendar_state'] = 'partner_declined'
                if attendee_state == 'declined':
                    record['calendar_state'] = 'cancel'
                if attendee_state == 'refund_session':
                    record['calendar_state'] = 'refund_session'
                if attendee_state == 'partner_skip_session':
                    record['calendar_state'] = 'partner_skip_session'
                if record['calendar_state'] == 'draft' and attendee_state in ['tentative', 'needsAction']:
                    record['is_pt_booked'] = True
                record['attendee_id'] = attendee_rc.id
                record['attendee_create_at'] = self._convert_utc_to_timestamp_milliseconds(attendee_rc.write_date)
                record['attendee_note'] = None
                if attendee_rc.note:
                    record['attendee_note'] = attendee_rc.note
                record.pop('exercise_id', None)
                record['attendee_partner_id'] = attendee_rc.partner_id.id
                record['attendee_partner_name'] = attendee_rc.partner_id.display_name
                record.pop('partner_id', None)
            return BaseResponse.success(data=calendar_records, index=index)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy ra chi tiết lịch tập
    @http.route('/api/book_pt/getCalendarDetail', type='http', auth='public', methods=['POST'], csrf=False)
    def get_calendar_detail(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            event_id = data.get('calendar_id')
            partner_id = data.get('partner_id')
            if not partner_id:
                return BaseResponse.error(error_code=1, message="partner_id required")
            if not event_id:
                return BaseResponse.error(error_code=1, message="Lịch không tồn tại")

            calendar_records = request.env['calendar.event'].sudo().search_read(
                domain=[('id', '=', int(event_id)), ('partner_ids', 'in', [int(partner_id)])],
                fields=['id', 'name', 'notes', 'is_past_booking', 'start', 'stop', 'state', 'pt_id', 'exercise_id', 'session_type',
                        'location_id'],
            )
            if not calendar_records:
                return BaseResponse.error(error_code=1, message="Lịch không tồn tại, hoặc bạn không có trong lịch này.")
            for record in calendar_records:
                record['location'] = None
                if record['location_id']:
                    record['location'] = record['location_id'][1]
                    record.pop('location_id', None)
                if not record['session_type']:
                    record['session_type'] = None
                if not record['exercise_id']:
                    record['exercise_id'] = None
                record['start'] = self._convert_utc_to_timestamp_milliseconds((record['start']))
                record['stop'] = self._convert_utc_to_timestamp_milliseconds((record['stop']))
                record['exercise_form_name'] = record['exercise_id'][1]

                record['note'] = record['notes'] or None
                record.pop('notes', None)

                record['is_past_booking'] = record['is_past_booking'] or False

                record['calendar_state'] = record['state'] or None
                record.pop('state', None)

                # xử lý thông tin của coach
                record['coach_name'] = ""
                record['coach_image'] = ""
                record['coach_id'] = False
                record['coach_rate_id'] = None
                record['coach_rate_msg'] = None
                record['coach_rate_rating'] = None
                record['coach_avg_rate'] = None
                record['coach_phone'] = None
                if record['pt_id']:
                    record['coach_id'] = record['pt_id'][0]
                    record['coach_name'] = record['pt_id'][1]
                    # đánh giá của khach voi hlv
                    rating = request.env['welly.rating'].sudo().search([('user_id', '=', int(record['coach_id'])),
                                                                        ('partner_id', '=', int(partner_id))])
                    if rating:
                        record['coach_rate_id'] = rating.id
                        record['coach_rate_msg'] = rating.message
                        record['coach_rate_rating'] = rating.rating
                    pt = request.env['welly.pt.management'].sudo().search([('user_id', '=', int(record['coach_id']))])
                    if pt:
                        record['coach_avg_rate'] = pt.rating_avg
                        employee = request.env['hr.employee'].sudo().search([('pt_manager_id', '=', pt.id)])
                        if employee and employee.mobile_phone:
                            record['coach_phone'] = employee.mobile_phone
                    record['coach_image'] = None
                    user = request.env['res.users'].sudo().browse(int(record['coach_id']))
                    base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
                    image = request.env['ir.attachment'].sudo().search(
                        [('res_model', '=', 'res.partner'), ('res_id', '=', user.partner_id.id),
                         ('res_field', '=', 'image_128')], limit=1)
                    if image:
                        access_token = image.generate_access_token()[0]
                        record['coach_image'] = f"{base_url}/web/image/{image.id}/image_128?access_token={access_token}"
                record.pop('pt_id', None)

                if record['session_type']:
                    record['session_type'] = record['session_type'][1]

                # kiểm tra partner đã đánh giá buổi tập chưa
                record['rate_id'] = None
                record['rate_msg'] = None
                record['rate_rating'] = None
                rating = request.env['welly.rating'].sudo().search([('event_id', '=', int(event_id)),
                                                                    ('partner_id', '=', int(partner_id))])
                if rating:
                    record['rate_id'] = rating[0].id
                    record['rate_msg'] = rating[0].message
                    record['rate_rating'] = rating[0].rating
                # xử lý trạng thái của lịch tập
                attendee_rc = request.env['calendar.attendee'].sudo().search([('event_id', '=', int(event_id)),
                                                                              ('partner_id', '=',
                                                                               int(partner_id))])

                contracts = attendee_rc.contract_id
                record['contract_total_session_number'] = None
                record['contract_available_session_number'] = None
                record['contract_free_session_number'] = None
                record['contract_session_number'] = None
                record['contract_id'] = None
                record['contract_name'] = None
                record['contract_service_name'] = None
                record['contract_date_start'] = None
                record['contract_date_end'] = None
                record['is_pt_booked'] = False
                if contracts:
                    record[
                        'contract_available_session_number'] = contracts.available_session_number + contracts.free_session_number
                    record['contract_free_session_number'] = contracts.free_session_number
                    total_ss = contracts.session_number + contracts.total_free_session_number
                    record['contract_session_number'] = attendee_rc.session_number
                    record['contract_total_session_number'] = total_ss
                    if contracts.service_type == 'member':
                        record['contract_total_session_number'] = contracts.total_free_session_number if contracts.total_free_session_number else None
                        record['contract_available_session_number'] = contracts.free_session_number if contracts.free_session_number else None
                    record['used_session_number'] = record['contract_total_session_number'] - record['contract_available_session_number']
                    record['contract_id'] = contracts.id
                    record['contract_service_name'] = contracts.sale_order_template_name_print if contracts.sale_order_template_name_print else None
                    record['contract_date_start'] = str(contracts.date_start)
                    if contracts.date_end:
                        record['contract_date_end'] = str(contracts.date_end)
                    else:
                        record['contract_date_end'] = None

                attendee_state = attendee_rc.state
                calendar_state = record['calendar_state']
                attendee_is_checked_in = attendee_rc.is_checked_in

                if attendee_state in ['needsAction', 'tentative', 'await_pt_confirm'] and calendar_state in ['draft', 'await_confirm']:
                    record['calendar_state'] = 'draft'
                if attendee_state == 'accepted' and calendar_state == 'draft':
                    record['calendar_state'] = 'pt_confirm'
                if attendee_is_checked_in and calendar_state == 'accepted' and attendee_state in ['accepted', 'tentative']:
                    record['calendar_state'] = 'accepted'
                if attendee_state in ['during_practice', 'accepted'] and calendar_state == 'during_practice':
                    record['calendar_state'] = 'during_practice'
                if calendar_state in ['done'] and attendee_state in ['done', 'cheat_commission']:
                    record['calendar_state'] = 'done'
                if attendee_state == 'declined' and (attendee_rc.partner_action == attendee_rc.event_id.pt_id or attendee_rc.partner_action == attendee_rc.event_id.pt_id_substitute):
                    record['calendar_state'] = 'declined'
                if attendee_state == 'declined' and attendee_rc.partner_action == attendee_rc.partner_id:
                    record['calendar_state'] = 'partner_declined'
                if attendee_state == 'declined':
                    record['calendar_state'] = 'cancel'
                if attendee_state == 'refund_session':
                    record['calendar_state'] = 'refund_session'
                if attendee_state == 'partner_skip_session':
                    record['calendar_state'] = 'partner_skip_session'
                if record['calendar_state'] == 'draft' and attendee_state in ['tentative', 'needsAction']:
                    record['is_pt_booked'] = True

                record['attendee_id'] = attendee_rc.id
                record['attendee_create_at'] = self._convert_utc_to_timestamp_milliseconds(attendee_rc.write_date)
                record['attendee_note'] = None
                if attendee_rc.note:
                    record['attendee_note'] = attendee_rc.note
                record.pop('exercise_id', None)
                record['attendee_partner_id'] = attendee_rc.partner_id.id
                record['attendee_partner_name'] = attendee_rc.partner_id.display_name
            return BaseResponse.success(data=calendar_records[0])
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy thông tin lịch tập sắp đến giờ tập gần nhất
    @http.route('/api/book_pt/getFirstCalendar', type='http', auth='public', methods=['POST'], csrf=False)
    def get_first_calendar(self, **kwargs):
        try:
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            calendar_record = request.env['calendar.event'].sudo().search_read(
                domain=[('partner_ids', 'in', [int(partner_id)]), ('attendee_ids.state', 'in', ['pt_confirm']),
                        ('state', '=', 'draft')],
                fields=['id', 'name', 'notes', 'start', 'stop', 'pt_id', 'exercise_id', 'session_type',
                        'location_id'],
                order='start asc',
                limit=1
            )
            if not calendar_record:
                return BaseResponse.success(data=[])
            for record in calendar_record:
                record['location'] = None
                if record['location_id']:
                    record['location'] = record['location_id'][1]
                    record.pop('location_id', None)
                if record['session_type']:
                    record['session_type'] = record['session_type'][1]
                else:
                    record['session_type'] = None
                if record['exercise_id']:
                    record['exercise_form_name'] = record['exercise_id'][1]
                record['start'] = self._convert_utc_to_timestamp_milliseconds((record['start']))
                record['stop'] = self._convert_utc_to_timestamp_milliseconds((record['stop']))
                record['coach_name'] = None
                if record['pt_id']:
                    record['coach_name'] = record['pt_id'][1]
                record['notes'] = None
                if record['notes']:
                    record['note'] = record['notes']
                record.pop('notes', None)
                record.pop('exercise_id', None)
                record.pop('pt_id', None)
                attendee_rc = request.env['calendar.attendee'].sudo().search(
                    [('event_id', '=', int(calendar_record[0]['id'])),
                     ('partner_id', '=',
                      int(partner_id))])

                contracts = attendee_rc.contract_id
                record['contract_total_session_number'] = None
                record['contract_session_number'] = None
                record['contract_service_name'] = None
                if contracts:
                    total_ss = contracts.session_number + contracts.total_free_session_number
                    record['contract_session_number'] = attendee_rc.session_number
                    record['contract_total_session_number'] = total_ss
                    if contracts.service_type == 'member':
                        record['contract_total_session_number'] = contracts.total_free_session_number if contracts.total_free_session_number else None
                    record['contract_service_name'] = contracts.sale_order_template_name_print if contracts.sale_order_template_name_print else None
            return BaseResponse.success(data=calendar_record[0])
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy thông tin lịch tập gần nhất
    @http.route('/api/book_pt/getCalendarNearest', type='http', auth='public', methods=['POST'], csrf=False)
    def get_first_calendar_nearest(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            limit = data.get('limit') if data.get('limit') else 3
            # lấy ra danh sách con của partner_id
            partner_rc = request.env['res.partner'].sudo().browse(int(partner_id))
            list_id_child = partner_rc.child_my_welly_id.ids
            list_id_child.append(int(partner_id))
            now = datetime.now()

            sql_query = f"""
                        SELECT ce.id,
                                ce.name,
                                ce.is_past_booking,
                                ce.notes as note,
                                ce.start,
                                ce.stop,
                                ce.state,
                                ce.create_date,
                                ce.pt_id as pt_id,
                                wef.name as exercise_form_name,
                                st.name as session_type,
                                wl.name as location,
                                ca.partner_id as partner_id
                        FROM calendar_event ce
                            LEFT JOIN calendar_attendee ca ON ca.event_id = ce.id
                            LEFT JOIN calendar_event_class_type st ON st.id = ce.session_type
                            LEFT JOIN welly_location wl ON wl.id = ce.location_id
                            LEFT JOIN welly_exercise_form wef ON wef.id = ce.exercise_id
                        WHERE ce.event_type = 'pt'
                          AND ca.partner_id in %s
                          AND (
                              ce.start >= %s 
                              OR ce.state = 'during_practice'
                          )
                          AND ce.state != 'reject'
                          AND (
                              ca.state IN ('await_pt_confirm', 'cheat_commission', 'needsAction', 'tentative', 'accepted', 'refund_session')
                          )
                        ORDER BY ce.start asc
                        LIMIT %s
                    """

            request.env.cr.execute(sql_query,
                                   (tuple(list_id_child), now, limit))
            calendar_record = request.env.cr.dictfetchall()
            if not calendar_record:
                return BaseResponse.success(data=[])
            for record in calendar_record:
                record['start'] = self._convert_utc_to_timestamp_milliseconds((record['start']))
                record['stop'] = self._convert_utc_to_timestamp_milliseconds((record['stop']))
                record['create_date'] = self._convert_utc_to_timestamp_milliseconds((record['create_date']))
                record['coach_name'] = None
                if record['pt_id']:
                    pt = request.env['res.users'].sudo().browse(int(record['pt_id']))
                    if pt:
                        record['coach_name'] = pt.partner_id.name
                record.pop('pt_id', None)
                attendee_rc = request.env['calendar.attendee'].sudo().search(
                    [('event_id', '=', int(record['id'])),
                     ('partner_id', '=', int(record['partner_id']))])

                record['is_past_booking'] = record['is_past_booking'] or False

                contracts = attendee_rc.contract_id
                record['contract_total_session_number'] = None
                record['contract_session_number'] = None
                record['contract_service_name'] = None
                record['is_pt_booked'] = False
                if contracts:
                    total_ss = contracts.session_number + contracts.total_free_session_number
                    record['contract_session_number'] = attendee_rc.session_number
                    record['contract_total_session_number'] = total_ss
                    if contracts.service_type == 'member':
                        record['contract_total_session_number'] = contracts.total_free_session_number if contracts.total_free_session_number else None
                    record['contract_service_name'] = contracts.sale_order_template_name_print if contracts.sale_order_template_name_print else None

                attendee_state = attendee_rc.state
                calendar_state = record['state']
                attendee_is_checked_in = attendee_rc.is_checked_in

                if attendee_state in ['needsAction', 'tentative', 'await_pt_confirm'] and calendar_state in ['draft', 'await_confirm']:
                    record['calendar_state'] = 'draft'
                if attendee_state == 'accepted' and calendar_state == 'draft':
                    record['calendar_state'] = 'pt_confirm'
                if attendee_is_checked_in and calendar_state == 'accepted' and attendee_state in ['accepted', 'tentative']:
                    record['calendar_state'] = 'accepted'
                if attendee_state in ['during_practice', 'accepted'] and calendar_state == 'during_practice':
                    record['calendar_state'] = 'during_practice'
                if calendar_state in ['done'] and attendee_state in ['done', 'cheat_commission']:
                    record['calendar_state'] = 'done'
                if attendee_state == 'declined' and (attendee_rc.partner_action == attendee_rc.event_id.pt_id or attendee_rc.partner_action == attendee_rc.event_id.pt_id_substitute):
                    record['calendar_state'] = 'declined'
                if attendee_state == 'declined' and attendee_rc.partner_action == attendee_rc.partner_id:
                    record['calendar_state'] = 'partner_declined'
                if attendee_state == 'declined':
                    record['calendar_state'] = 'cancel'
                if attendee_state == 'refund_session':
                    record['calendar_state'] = 'refund_session'
                if attendee_state == 'partner_skip_session':
                    record['calendar_state'] = 'partner_skip_session'
                if record['calendar_state'] == 'draft' and attendee_state in ['tentative', 'needsAction']:
                    record['is_pt_booked'] = True

                record.pop('state', None)
                record['attendee_create_at'] = self._convert_utc_to_timestamp_milliseconds(attendee_rc.write_date)
                record['attendee_partner_id'] = attendee_rc.partner_id.id
                record['attendee_partner_name'] = attendee_rc.partner_id.display_name
                record.pop('partner_id', None)
            return BaseResponse.success(data=calendar_record)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api đánh giá buổi tập
    @http.route('/api/book_pt/createCalendarRating', type='http', auth='public', methods=['POST'], csrf=False)
    def create_calendar_rating(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            event_id = data.get('calendar_id')
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            message = data.get('message')
            rating = data.get('rating')
            rating_image_url = data.get('rating_image_url')

            if not event_id:
                return BaseResponse.error(error_code=1, message="calendar_id are required")

            # Tạo mới bản ghi welly.rating
            new_rating = request.env['welly.rating'].sudo().create({
                'event_id': int(event_id),
                'partner_id': int(partner_id),
                'message': message,
                'rating': float(rating),
                'rating_image_url': rating_image_url
            })
            return BaseResponse.success(data={'id': new_rating.id})

        except Exception as e:
            return BaseResponse.error(error_code=1, message=str('Bạn đã đánh giá rồi!'))

    # api chỉnh sửa đánh giá buổi tập
    @http.route('/api/book_pt/editRating', type='http', auth='public', methods=['POST'], csrf=False)
    def edit_rating(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            rate_id = data.get('rate_id')
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            message = data.get('message')
            rating = data.get('rating')
            rating_image_url = data.get('rating_image_url')

            if not rate_id:
                return BaseResponse.error(error_code=1, message="rate_id are required")

            rate = request.env['welly.rating'].sudo().search([('id', '=', int(rate_id)),
                                                              ('partner_id', '=', int(partner_id))])
            rate.write({
                'message': message,
                'rating': rating,
                'rating_image_url': rating_image_url
            })

            if rate.user_id:
                rates = request.env['welly.rating'].sudo().search([('user_id', '=', rate.user_id.id)])
                if rates:
                    rating_avg = sum(rating.rating for rating in rates) / len(rates)
                else:
                    rating_avg = 0
                pt_mng = request.env['welly.pt.management'].sudo().search([('user_id', '=', rate.user_id.id)])
                pt_mng.write({'rating_avg': rating_avg})
            return BaseResponse.success(data={'id': rate.id})

        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api tính toán hiển thị màu chú thích cho màn lịch tập
    @http.route('/api/book_pt/calculateColor', type='http', auth='public', methods=['POST'], csrf=False)
    def calculate_color_booking(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            date_from = data.get('date_from')
            date_to = data.get('date_to')
            if not date_from or not date_to:
                return BaseResponse.error(error_code=1, message="date_from, date_to required")
            result = []
            date_format = "%d/%m/%Y"

            # Chuyển định dạng ngày từ input
            start_date_gmt7 = datetime.strptime(date_from, date_format) - timedelta(hours=7)

            # Lấy ra danh sách ngày trong khoảng từ date_from đến date_to
            current_date_gmt = datetime.strptime(date_from, date_format)
            end_date_gmt = datetime.strptime(date_to, date_format)
            while current_date_gmt <= end_date_gmt:
                date_str = current_date_gmt.strftime(date_format)

                # Tìm kiếm các sự kiện trong ngày
                events = request.env['calendar.event'].sudo().search([
                    ('start', '>=', start_date_gmt7),
                    ('start', '<=', start_date_gmt7 + timedelta(days=1)),
                    ('partner_ids', 'in', [int(partner_id)])
                ])

                color = 'gray'
                # Tìm tất cả các attendee liên quan đến sự kiện
                attendees = request.env['calendar.attendee'].sudo().search([
                    ('event_id', 'in', events.ids),
                    ('partner_id', '=', int(partner_id))
                ])

                # Xác định màu sắc dựa trên trạng thái của attendee trong sự kiện đó
                if attendees:
                    if any(attendee.state in ['needsAction', 'tentative'] for attendee in attendees):
                        color = 'orange'
                    elif all(attendee.state in ['accepted', 'await_pt_confirm', 'during_practice', 'done'] for attendee in attendees):
                        color = 'green'
                    if all(attendee.state in ['declined', 'refund_session', 'cheat_commission', 'partner_skip_session'] for attendee in
                           attendees):
                        color = 'gray'

                result.append({'date': date_str, 'color': color})
                # Di chuyển đến ngày tiếp theo
                current_date_gmt += timedelta(days=1)
                start_date_gmt7 += timedelta(days=1)

            return BaseResponse.success(data=result)
        except Exception as e:
            logging.error(e)
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/fetchPt', type='http', auth='public', methods=['POST'], csrf=False)
    def create_records(self, **kwargs):
        user = request.env['res.users']
        pt = request.env['welly.pt.management']
        all_users = user.sudo().search([])
        for user in all_users:
            if user.has_group('welly_base.group_pt') or user.has_group('welly_base.group_pt_manager'):
                total = pt.sudo().get_total_student(user.id)
                pt.sudo().create({
                    'user_id': user.id,
                    'total_student': total
                })

    # api lấy ra list dịch vụ
    @http.route('/api/getListService', type='http', auth='public', methods=['POST'], csrf=False)
    def get_service_list(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            location_id = data.get('location_id')
            if not location_id:
                return BaseResponse.error(error_code=1, message="location_id required")
            location = request.env['welly.location'].sudo().search([('id', '=', location_id)])

            service_types = request.env['welly.pt.category'].sudo().search(
                [('company_id', '=', location.company_id.id)]
            )

            # Tạo danh sách các dịch vụ để trả về
            services = [{'id': service.id, 'name': service.name} for service in service_types]

            return BaseResponse.success(data=services)

        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy ra list pt theo dịch vụ body: {"service_id":1}
    @http.route('/api/getListPt', type='http', auth='public', methods=['POST'], csrf=False)
    def get_pt_list(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            service_id = data.get('service_id')
            location_id = data.get('location_id')
            domain = []
            if service_id:
                domain.append(('category_ids', 'in', int(service_id)))
            if location_id:
                location = request.env['welly.location'].sudo().search([('id', '=', location_id)])
                domain.append(('user_id.company_id.id', '=', location.company_id.id))
            # Truy vấn thông tin từ model welly.pt.management dựa trên user_ids
            result = request.env['welly.pt.management'].sudo().search_read(
                domain=domain,
                fields=['user_id', 'description', 'rating_avg', 'experience', 'total_student', 'category_ids']
            )

            # Lấy danh sách tất cả Category tags
            category_ids = list(
                set(category_id for record in result for category_id in record.get('category_ids', [])))
            category_names = dict(
                request.env['welly.pt.category'].sudo().browse(category_ids).mapped(lambda c: (c.id, c.name)))

            # Tách trường 'user_id' thành 'user_id' và 'name'
            for record in result:
                user_id = record.get('user_id', False)
                if not record['description']:
                    record['description'] = None
                if user_id:
                    user = request.env['res.users'].browse(user_id[0])
                    record.update({
                        'user_id': user_id[0],
                        'name': user.name
                    })
                    record['image'] = None
                    base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
                    image = request.env['ir.attachment'].sudo().search(
                        [('res_model', '=', 'res.partner'), ('res_id', '=', user.partner_id.id),
                         ('res_field', '=', 'image_128')], limit=1)
                    if image:
                        access_token = image.generate_access_token()[0]
                        record['image'] = f"{base_url}/web/image/{image.id}/image_128?access_token={access_token}"
                record['category_ids'] = [category_names.get(category_id) for category_id in
                                          record.get('category_ids', [])]
            return BaseResponse.success(data=result)

        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy thông tin của pt theo id body: {"user_id":1}
    @http.route('/api/getPtInfo', type='http', auth='public', methods=['POST'], csrf=False)
    def get_pt_management(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id = data.get('user_id')
            if not user_id:
                return BaseResponse.error(error_code=1, message="user_id required")

            pt_management_data = request.env['welly.pt.management'].sudo().search_read(
                domain=[('user_id', '=', int(user_id))],
                fields=['user_id', 'description', 'rating_avg', 'experience', 'total_student', 'category_ids',
                        'certificate_ids']
            )
            # Lấy danh sách tất cả WellyPTCategory một lần
            category_ids = list(
                set(category_id for record in pt_management_data for category_id in record.get('category_ids', [])))
            category_names = dict(
                request.env['welly.pt.category'].sudo().browse(category_ids).mapped(lambda c: (c.id, c.name)))

            # Lấy danh sách tất cả WellyPTCertificate một lần
            certificate_ids = list(set(certificate_id for record in pt_management_data for certificate_id in
                                       record.get('certificate_ids', [])))
            certificates_info = dict(request.env['welly.pt.certificate'].sudo().browse(certificate_ids).mapped(
                lambda c: (c.id, {'name': c.name or None, 'image': c.image or None})))

            # Tách trường 'user_id' thành 'user_id' và 'name'
            for record in pt_management_data:
                if not record['description']:
                    record['description'] = None
                user_id = record.get('user_id', False)
                if user_id:
                    user = request.env['res.users'].sudo().browse(user_id[0])
                    record.update({
                        'user_id': user_id[0],
                        'name': user.name
                    })
                    record['image'] = None
                    base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
                    image = request.env['ir.attachment'].sudo().search(
                        [('res_model', '=', 'res.partner'), ('res_id', '=', user.partner_id.id),
                         ('res_field', '=', 'image_128')], limit=1)
                    if image:
                        access_token = image.generate_access_token()[0]
                        record['image'] = f"{base_url}/web/image/{image.id}/image_128?access_token={access_token}"

                # Lấy danh sách tên category_ids từ dict đã tạo
                record['category_ids'] = [category_names.get(category_id) for category_id in
                                          record.get('category_ids', [])]

                # Lấy danh sách thông tin certificate_ids từ dict đã tạo
                record['certificate_ids'] = [{'id': certificate_id, **certificates_info.get(certificate_id, {})} for
                                             certificate_id in record.get('certificate_ids', [])]
            return BaseResponse.success(data=pt_management_data)

        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy ra list rating theo user_id
    @http.route('/api/getRatingByUser', type='http', auth='public', methods=['POST'], csrf=False)
    def get_rating_list_by_user(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id = data.get('user_id')
            partner_id = data.get('partner_id')
            # todo check input partner_id
            if not user_id:
                return BaseResponse.error(error_code=1, message="user_id required")
            domain = [('user_id', '=', int(user_id))]
            if partner_id:
                domain.append(('partner_id', '=', int(partner_id)))
            rating_records = request.env['welly.rating'].sudo().search_read(
                domain=domain,
                fields=['rating', 'rating_image_url', 'create_date', 'message', 'partner_id']
            )

            if rating_records:
                for record in rating_records:
                    if 'create_date' in record:
                        record['create_date'] = str(record['create_date'] + timedelta(hours=7))
                    if 'partner_id' in record:
                        partner = request.env['res.partner'].sudo().browse(record['partner_id'][0])
                        record['partner_name'] = partner.name
                        record['partner_image'] = None
                        base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
                        image = request.env['ir.attachment'].sudo().search(
                            [('res_model', '=', 'res.partner'), ('res_id', '=', partner.id),
                             ('res_field', '=', 'image_128')], limit=1)
                        if image:
                            access_token = image.generate_access_token()[0]
                            record[
                                'partner_image'] = f"{base_url}/web/image/{image.id}/image_128?access_token={access_token}"

                        record['partner_id'] = partner.id
            return BaseResponse.success(data=rating_records)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy ra list rating theo user_id
    @http.route('/api/checkPermissionRate', type='http', auth='public', methods=['POST'], csrf=False)
    def check_permission_rate(self, **kwargs):
        data = get_data_and_log_request(request)
        pt_id = data.get('user_id')
        partner_id, error = get_and_verify_jwt_from_header(False)
        if error:
            return BaseResponse.error(error_code=401, message=error)
        return BaseResponse.success(data=self.is_partner_user_service_with_pt(pt_id, partner_id))

    @http.route('/api/createRating', type='http', auth='public', methods=['POST'], csrf=False)
    def create_rating(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id = data.get('user_id')
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            message = data.get('message')
            rating = data.get('rating')
            rating_image_url = data.get('rating_image_url')

            if not user_id:
                return BaseResponse.error(error_code=1, message="user_id, are required")
            if not self.is_partner_user_service_with_pt(user_id, partner_id):
                return BaseResponse.error(error_code=1, message="Partner not permission")
            # Tạo mới bản ghi welly.rating
            new_rating = request.env['welly.rating'].sudo().create({
                'user_id': int(user_id),
                'partner_id': int(partner_id),
                'message': message,
                'rating': float(rating),
                'rating_image_url': rating_image_url
            })

            return BaseResponse.success(data={'id': new_rating.id})

        except Exception as e:
            return BaseResponse.error(error_code=1, message=str('Bạn đã đánh giá rồi!'))

    @http.route('/api/deleteRating', type='http', auth='public', methods=['POST'], csrf=False)
    def delete_rating(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            rate_id = data.get('rate_id')
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)

            if not rate_id:
                return BaseResponse.error(error_code=1, message="rate_id are required")
            rate = request.env['welly.rating'].sudo().search([('id', '=', int(rate_id)),
                                                              ('partner_id', '=', int(partner_id))])
            if not rate:
                return BaseResponse.error(error_code=1, message="Đánh giá của bạn không tồn tại!")
            rate_user = rate.user_id
            rate.unlink()
            if rate_user:
                rates = request.env['welly.rating'].sudo().search([('user_id', '=', rate_user.id)])
                if rates:
                    rating_avg = sum(rating.rating for rating in rates) / len(rates)
                else:
                    rating_avg = 0
                pt_mng = request.env['welly.pt.management'].sudo().search([('user_id', '=', rate_user.id)])
                pt_mng.write({'rating_avg': rating_avg})
            return BaseResponse.success(data=True)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/qr_code/gen_code_from_contract', type='http', auth='public', methods=['POST'], csrf=False)
    def gen_code_from_contract(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            contract_id = data.get('contract_id')
            is_new = data.get('is_new')
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            if not contract_id:
                return BaseResponse.error(data=[], message="thiếu contract_id", error_code=1)
            # chặn sử dụng dịch vụ
            block_config = config.get('block_gen_qr') if config.get('block_gen_qr') else None
            if block_config:
                blocks = block_config.split(',')
                current_db = request.db
                blocks = [db.strip() for db in blocks]
                if current_db in blocks:
                    module_path = os.path.dirname(os.path.abspath(__file__))
                    image_path = os.path.join(module_path, '..', 'static', 'image', 'block_qr.png')
                    image_path = os.path.abspath(image_path)
                    if not os.path.exists(image_path):
                        return BaseResponse.error(data=[], message="Dịch vụ tạo QR chưa hỗ trợ trên cơ sở này",
                                                  error_code=1)
                    with open(image_path, 'rb') as image_file:
                        img_bytes = image_file.read()
                        img_base64 = base64.b64encode(img_bytes).decode('utf-8')
                        data_uri = f'data:image/png;base64,{img_base64}'
                    rs = {
                        'qr_code': data_uri,
                        'exp': 0
                    }
                    return BaseResponse.success(data=rs)

            # query hợp đồng
            contract_record = request.env['welly.contract'].sudo().browse(int(contract_id))
            if not contract_record:
                return BaseResponse.error(data=[], message="không tìm được hợp đồng", error_code=1)

            qrcode = request.env['welly.qr.code'].sudo()
            qrcode_record = qrcode.search(
                [('welly_contract_id', '=', contract_record.id),
                 ('expire_time', '>', fields.Datetime.now()),
                 ('is_scanned', '=', False)
                 ],
                limit=1, order='id desc')

            # case tạo mới mã qr khi khách bấm refresh hoặc mã cũ đã hết hạn
            if is_new or not qrcode_record:
                # Tạo Mới QR Code
                time_expire = contract_record.company_id.expire_time_qr if contract_record.company_id.expire_time_qr else 180
                qr_code_data = {
                    'welly_contract_id': contract_record.id,
                    'expire_time': fields.Datetime.now() + timedelta(seconds=int(time_expire))
                }
                qr_new = qrcode.create(qr_code_data)
                rs = {'qr_code': qr_new.qr_code_png,
                      'exp': self._convert_utc_to_timestamp_milliseconds(qr_new.expire_time)}
                return BaseResponse.success(data=rs)
            if qrcode_record:
                rs = {'qr_code': qrcode_record.qr_code_png,
                      'exp': self._convert_utc_to_timestamp_milliseconds(qrcode_record.expire_time)}
                return BaseResponse.success(data=rs)
        except Exception as e:
            return BaseResponse.error(data=[],
                                      message=str(e),
                                      error_code=1)

    def is_partner_user_service_with_pt(self, pt_id, partner_id):
        contracts = request.env['welly.contract'].sudo().search([
            ('state', 'in', ['activated', 'done']),
            ('coach_id', '=', pt_id)
        ])
        partner_ids = contracts.mapped('partner_id.id')
        for partner in partner_ids:
            if partner == partner_id:
                return True
        return False

    def _convert_utc_to_timestamp_milliseconds(self, utc_time):
        timestamp_seconds = utc_time.timestamp()
        timestamp_milliseconds = int(timestamp_seconds * 1000)
        return timestamp_milliseconds

    def convert_timestamp_milliseconds_to_utc(self, timestamp_milliseconds):
        timestamp_seconds = timestamp_milliseconds / 1000
        utc_time = datetime.utcfromtimestamp(timestamp_seconds)
        utc_time = utc_time.replace(tzinfo=timezone.utc)
        return utc_time

    def convert_amount_to_vi(self, amount):
        locale.setlocale(locale.LC_ALL, 'vi_VN.UTF-8')
        formatted_amount = locale.currency(amount, grouping=True)
        return formatted_amount

    # định dạng dd-MM-yyyy
    def convert_date_format(self, input_date):
        # Chuyển đổi định dạng từ chuỗi sang đối tượng datetime
        datetime_object = datetime.strptime(input_date, "%Y-%m-%d")

        # Chuyển đổi đối tượng datetime thành chuỗi mới với định dạng mong muốn
        formatted_date = datetime_object.strftime("%d-%m-%Y")

        return formatted_date

    # ví dụ: input_date: str time; original_format = "%Y-%m-%d"; destination_format= "%d-%m-%Y"
    def _convert_date_format(self, input_date, original_format, destination_format):
        datetime_object = datetime.strptime(input_date, original_format)
        formatted_date = datetime_object.strftime(destination_format)
        return formatted_date

    def format_phone_number(self, phone):
        # Loại bỏ các ký tự không mong muốn
        phone = ''.join(filter(str.isdigit, phone))

        # Nếu có dấu +84 ở đầu, thì thay thế thành số 0
        if phone.startswith('84'):
            phone = '0' + phone[2:]
        elif phone.startswith('+84'):
            phone = '0' + phone[3:]

        # Xóa dấu cách
        phone = phone.replace(' ', '')

        return phone

    ################# API LỊCH TẬP YOGA GROUP-X #################
    # Download CSV file from Google Drive (yêu cầu link public)
    # Cần cấu hình config parameter welly.schedule.google.drive.url trong thông số hệ thống
    def download_csv_file(self, url):
        response = requests.get(url)
        if response.status_code == 200:
            return StringIO(response.content.decode())
        else:
            _logger.warning("Failed to download the CSV file.")
            return None

    # Tính toán trạng thái của lịch tập dựa vào thời gian hiện tại
    def calc_status(self, schedule, date):
        # Loại bỏ toàn bộ các ký tự khác number và ':' của schedule
        start, end = re.sub(r'[^0-9:\-]', '', schedule).split('-')
        # Tính toán thời gian bắt đầu buổi tập
        # Đồng thời convert sang múi giờ UTC+7
        start_time = datetime.strptime(f'{date} {start}', '%d/%m/%Y %H:%M') - timedelta(hours=7)
        end_time = datetime.strptime(f'{date} {end}', '%d/%m/%Y %H:%M') - timedelta(hours=7)
        # Kiểm tra trạng thái 0: đã diễn ra, 1: sắp diễn ra, 2: đang diễn ra, 3: chưa diễn ra dựa vào start_time và end_time so sánh với thời gian hiện tại
        now = datetime.now()
        if end_time < now:
            status = 0
        elif start_time - now < timedelta(hours=1) and start_time - now > timedelta(hours=0):
            status = 1
        elif start_time <= now and end_time >= now:
            status = 2
        else:
            status = 3
        return int(start_time.timestamp()), int(end_time.timestamp()), status

    def convert_valid_time_type(self, date_end):
        current_date = fields.Date.today()
        date_end = fields.Date.from_string(date_end)
        valid_time = (date_end - current_date).days
        return valid_time

    # Đọc lịch tập từ file CSV
    def read_schedule(self, csv_file):
        yoga_schedule = []
        groupx_schedule = []

        reader = csv.reader(csv_file)
        # Đọc dữ liệu ngày trong tuần của file CSV
        dates = next(reader)[2:9]
        # Skip the second row
        next(reader)
        is_yoga = True
        while True:
            row = next(reader)
            # Lấy thời gian tập và tên lớp tập
            schedule = row[0]
            class_name = row[2:9]
            if is_yoga:
                if schedule == "-":
                    is_yoga = False
                    continue
                level = next(reader)[2:9]
                class_name_translated = next(reader)[2:9]
                coach = next(reader)[2:9]
                # Lưu thông tin lịch tập, mapping lại các trường data vào dictionary
                for i, date in enumerate(dates):
                    timestamp = int(datetime.strptime(date, "%d/%m/%Y").timestamp())
                    start_time, end_time, status = self.calc_status(schedule, date)
                    yoga_schedule.append({
                        "timestamp": timestamp * 1000,
                        "date": date,
                        "start_time": start_time * 1000,
                        "end_time": end_time * 1000,
                        "status": status,
                        "schedule": schedule,
                        "class_name": class_name[i],
                        "class_name_translated": class_name_translated[i],
                        "level": level[i],
                        "coach": coach[i]
                    })
            else:
                if not row[1]:
                    break
                coach = next(reader)[2:9]
                # Lưu thông tin lịch tập, mapping lại các trường data vào dictionary
                for i, date in enumerate(dates):
                    timestamp = int(datetime.strptime(date, "%d/%m/%Y").timestamp())
                    start_time, end_time, status = self.calc_status(schedule, date)
                    groupx_schedule.append({
                        "timestamp": timestamp * 1000,
                        "date": date,
                        "start_time": start_time * 1000,
                        "end_time": end_time * 1000,
                        "status": status,
                        "schedule": schedule,
                        "name": class_name[i],
                        "coach": coach[i]
                    })

        return yoga_schedule, groupx_schedule

    @http.route('/api/getSchedule', type='http', auth='public', methods=['GET'], csrf=False)
    def get_schedule(self, start, end, type):
        # Thời gian nhận vào là timestamp miniseconds, chuyển sang datetime UTC
        start_datetime_utc = self.convert_timestamp_milliseconds_to_utc(int(start))
        end_datetime_utc = self.convert_timestamp_milliseconds_to_utc(int(end))
        res = []
        if type in ['yoga','groupx']:
            group_class_schedule = request.env['event.event'].sudo().search(
                [('class_id.class_type', '=', type),
                 ('date_begin', '>=', start_datetime_utc),
                 ('date_end', '<=', end_datetime_utc)
                 ],
                order='date_begin asc'
            )
            if group_class_schedule:
                for record in group_class_schedule:
                    # Lấy thông tin ngày bắt đầu ở múi UTC convert sang múi giờ UTC+7 và lấy date
                    date_to_string = record.date_begin.astimezone(timezone(timedelta(hours=7))).date().strftime('%d/%m/%Y')
                    start_time = record.date_begin.astimezone(timezone(timedelta(hours=7)))
                    end_time = record.date_end.astimezone(timezone(timedelta(hours=7)))
                    schedule = f"{start_time.strftime('%H:%M')}-{end_time.strftime('%H:%M')}"
                    # Kiểm tra trạng thái 0: đã diễn ra, 1: sắp diễn ra, 2: đang diễn ra, 3: chưa diễn ra dựa vào
                    # start_time và end_time so sánh với thời gian hiện tại
                    now = datetime.now().astimezone(timezone(timedelta(hours=7)))
                    status = None
                    if end_time < now:
                        status = 0
                    elif timedelta(hours=1) > start_time - now > timedelta(hours=0):
                        status = 1
                    elif start_time <= now <= end_time:
                        status = 2
                    else:
                        status = 3
                    start_time_to_timestamp_milliseconds = int(start_time.timestamp()*1000)
                    end_time_to_timestamp_milliseconds = int(end_time.timestamp()*1000)
                    res.append({
                        "timestamp": '',
                        "date": date_to_string,       # ngày tập UTC+7
                        "start_time": start_time_to_timestamp_milliseconds,        # timestamp thời gian bắt đầu tập
                        "end_time": end_time_to_timestamp_milliseconds,        # timestamp thời gian kết thúc tập
                        "stage_type": record.stage_id.stage_type or '',    # loại trạng thái của lịch
                        "status": status,       # trạng thái thời gian của lịch tập
                        "schedule": schedule,   # thời gian tập theo múi giờ UTC+7
                        "name": record.name or '',    # tên lớp tập nhập tay
                        "class_name": record.class_id.name or '',    # tên lớp tập danh mục
                        "class_name_translated": record.class_id.name_translated or '',    # tên lớp tập danh mục dịch
                        "level": record.level_id.name or '',    # cấp độ lớp tập
                        "coach": record.teacher_name or '',   # tên giáo viên
                    })
        if res:
            return BaseResponse.success(data=list(res))
        else:
            return BaseResponse.error(error_code=1, message="Không tìm thấy lịch tập")

    @http.route('/api/getLocationCompany', type='http', auth='public', methods=['GET'], csrf=False)
    def get_location_company(self):
        try:
            #version 4.9: kiểm tra nếu chưa có token thì thông báo khách update app
            auth_header = request.httprequest.headers.get('Authorization')
            if not auth_header or not auth_header.startswith('Bearer '):
                return BaseResponse.error(error_code=401, message="Phiên bản của bạn đã là bản cũ, hãy cập nhật phiên bản mới nhất để sử dụng ứng dụng!")
            partner_phone, welly_id, error = get_and_verify_jwt_from_header_none_domain(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            # Lấy danh sách tất cả các database
            db = config.get('use_mywelly') if config.get('use_mywelly') else None
            if not db:
                return BaseResponse.error(error_code=1, message='Chưa cấu hình use_mywelly')
            db_list = db.split(',')
            result = []
            for db_name in db_list:
                try:
                    # Kết nối tới database tương ứng
                    conn = db_connect(db_name)
                    with conn.cursor() as cr:
                        # Thiết lập environment cho database hiện tại
                        env = api.Environment(cr, 1, {})
                        # kiểm tra cơ sở này có thông tin của partner không
                        check_partner = env['res.partner'].sudo().search([
                            ('phone', '=', partner_phone)
                        ])
                        if check_partner:
                            # Lấy thông tin công ty đầu tiên
                            hidden_value = env['ir.config_parameter'].sudo().get_param('hidden_feature_mywelly')
                            endpoint = env['ir.config_parameter'].sudo().get_param('mywelly_host')
                            mywelly_address_name = env['ir.config_parameter'].sudo().get_param('mywelly_address_name')
                            company = env['res.company'].search([], order='id asc', limit=1)
                            if company and mywelly_address_name:
                                result.append({
                                    'company_name': f'{mywelly_address_name if mywelly_address_name else ""}',
                                    'company_address': f'{company.street  if mywelly_address_name else ""}',
                                    'company_hotline': f'{company.phone.replace(" ", "")  if company.phone else ""}',
                                    'company_email': f'{company.email  if company.email else ""}',
                                    'company_longitude': f'{company.longitude  if company.longitude else ""}',
                                    'company_latitude': f'{company.latitude  if company.latitude else ""}',
                                    'endpoint': endpoint if endpoint else "",
                                    'hidden_feature': hidden_value if hidden_value else "",
                                    'sub_domain': db_name
                                })
                except Exception as e:
                    logging.error(str(e))

            if len(result) < 1:
                request.env['res.partner'].sudo().delete_user_on_gymo_id(gymo_id=welly_id)
                return BaseResponse.error(error_code=401, message="Số điện thoại không tồn tại trên hệ thống")
            return BaseResponse.success(data=result)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/getLocationCompanyPublic', type='http', auth='public', methods=['GET'], csrf=False)
    def get_location_company_public(self):
        try:
            # Lấy danh sách tất cả các cơ sở để liên hệ tư vấn
            db = config.get('use_mywelly') if config.get('use_mywelly') else None
            if not db:
                return BaseResponse.error(error_code=1, message='Chưa cấu hình use_mywelly')
            db_list = db.split(',')
            result = []
            for db_name in db_list:
                try:
                    # Kết nối tới database tương ứng
                    conn = db_connect(db_name)
                    with conn.cursor() as cr:
                        # Thiết lập environment cho database hiện tại
                        env = api.Environment(cr, 1, {})
                        # Lấy thông tin công ty đầu tiên
                        hidden_value = env['ir.config_parameter'].sudo().get_param('hidden_feature_mywelly')
                        endpoint = env['ir.config_parameter'].sudo().get_param('mywelly_host')
                        mywelly_address_name = env['ir.config_parameter'].sudo().get_param('mywelly_address_name')
                        company = env['res.company'].search([], order='id asc', limit=1)
                        location = env['welly.location'].search([('is_default', '=', True)], order='id asc', limit=1)
                        if company and location:
                            result.append({
                                'company_name': f'{mywelly_address_name if mywelly_address_name else ""}',
                                'company_hotline': f'{company.phone.replace(" ", "") if company.phone else ""}',
                                'company_email': f'{company.email if company.email else ""}',
                                'company_address': f'{location.name if location.name else ""}',
                                'location_id': location.id,
                                'company_longitude': f'{company.longitude if company.longitude else ""}',
                                'company_latitude': f'{company.latitude if company.latitude else ""}',
                                'endpoint': endpoint if endpoint else "",
                                'hidden_feature': hidden_value if hidden_value else "",
                                'sub_domain': db_name
                            })
                except Exception as e:
                    logging.error(str(e))
            return BaseResponse.success(data=result)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/getCompanyInfoBySubdomain', type='http', auth='public', methods=['POST'], csrf=False)
    def get_company_info(self):
        data = get_data_and_log_request(request)
        sub_domain = data.get('sub_domain')
        # Lấy danh sách tất cả các database
        db_list = db.list_dbs(force=True)
        result = {}
        if sub_domain in db_list:
            try:
                # Kết nối tới database tương ứng
                conn = db_connect(sub_domain)
                with conn.cursor() as cr:
                    # Thiết lập environment cho database hiện tại
                    env = api.Environment(cr, 1, {})
                    # Lấy thông tin công ty đầu tiên
                    hidden_value = env['ir.config_parameter'].sudo().get_param('hidden_feature_mywelly')
                    endpoint = env['ir.config_parameter'].sudo().get_param('mywelly_host')
                    company = env['res.company'].search([], order='id asc', limit=1)
                    if company:
                        result = {
                            'company_name': f'{company.name} - {company.street}',
                            'company_address': f'{company.street}',
                            'company_hotline': f'{company.phone.replace(" ", "")}',
                            'company_email': f'{company.email}',
                            'endpoint': endpoint if endpoint else "",
                            'hidden_feature': hidden_value if hidden_value else "",
                            'sub_domain': sub_domain,
                            'check_in_top_1': company.check_in_top_1,
                            'check_in_top_2': company.check_in_top_2,
                            'check_in_top_3': company.check_in_top_3,
                            'branch_name': company.app_mywelly_branch_name,
                            'time_decline_booking': company.time_partner_can_decline_booking_app_mywelly,
                        }
            except Exception as e:
                logging.error(str(e))
        else:
            return BaseResponse.error(error_code=1, message="Không tồn tại sub_domain")
        return BaseResponse.success(data=result)

    # api lấy token để đăng nhập với role admin
    @http.route('/api/getTokenAdmin', type='http', auth='public', methods=['POST'], csrf=False)
    def login_generate_jwt_token(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            username = data.get('username')
            passwd = data.get('password')
            partner_phone = data.get('partner_phone')
            mywelly_admin = request.env['ir.config_parameter'].sudo().get_param('mywelly_admin')
            mywelly_passwd = request.env['ir.config_parameter'].sudo().get_param('mywelly_passwd')
            if username != mywelly_admin or passwd != mywelly_passwd:
                return BaseResponse.error(error_code=1, message='Sai thông tin đăng nhập.')
            token = generate_jwt_token(partner_phone)
            return BaseResponse.success(data=token)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/uploadFeedbackMyWelly', type='http', auth='public', methods=['POST'], csrf=False)
    def upload_file_tele(self, **kwargs):
        try:
            BOT_TOKEN = request.env['ir.config_parameter'].sudo().get_param('token_telegram')
            CHAT_ID = '-4270790220'

            if 'file' not in request.httprequest.files:
                return BaseResponse.error(error_code=1, message='No file uploaded')

            files = request.httprequest.files.getlist('file')
            phone = request.params.get('phone')
            description = request.params.get('description')

            if not files:
                return BaseResponse.error(error_code=1, message='No file uploaded')

            try:
                # Lưu file tạm thời
                tmp_dir = '/tmp'
                if not os.path.exists(tmp_dir):
                    os.makedirs(tmp_dir)
                # Gửi File
                for file in files:
                    file_path = os.path.join(tmp_dir, werkzeug.utils.secure_filename(file.filename))
                    with open(file_path, 'wb') as f:
                        f.write(file.read())

                    # Gửi file đến Telegram
                    with open(file_path, 'rb') as f:
                        response = requests.post(
                            f'https://api.telegram.org/bot{BOT_TOKEN}/sendDocument',
                            data={'chat_id': CHAT_ID},
                            files={'document': f},
                            timeout=60
                        )

                    # Xóa file sau khi gửi
                    os.remove(file_path)

                # gửi caption
                message = ""
                if phone:
                    message = f'Số điện thoại: {phone}. '
                if description:
                    message = f'{message} Mô tả vấn đề: {description}. '
                encoded_text = urllib.parse.quote(message)
                complete_url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage?chat_id={CHAT_ID}&text={encoded_text}"
                response = requests.get(complete_url)
                if response.status_code == 200 and response.ok:
                    return BaseResponse.success(data=[])
                else:
                    return BaseResponse.error(error_code=1, message="FAIL")
            except Exception as e:
                return BaseResponse.error(error_code=1, message=str(e))
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # API đánh dấu đã đọc tất cả thông báo của khách hàng
    @http.route('/api/partner/markAllNotificationsAsRead', type='http', auth='public', methods=['POST'], csrf=False)
    def mark_all_notifications_as_read(self, **kwargs):
        try:
            # Lấy partner_id hoặc error từ header request
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            # Lấy danh sách noti chưa đọc của khách hàng tương ứng trừ type training_telegram
            notifications_sent = request.env['notification.queue'].sudo().search(
                domain=[
                    ('partner_id', '=', int(partner_id)),
                    ('type', 'not in', ['training_telegram']),
                    ('is_read', '=', False)
                ]
            )
            # Đánh dấu các thông báo trong danh sách là đã đọc
            if notifications_sent:
                notifications_sent.write({'is_read': True})
            return BaseResponse.success()
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))


    # api lấy ra bảng xếp hạng check-in khách hàng theo tháng
    @http.route('/api/getRankingCheckinByPartner', type='http', auth='public', methods=['POST'], csrf=False)
    def get_ranking_checkin_by_partner(self, **kwargs):
        data = get_data_and_log_request(request)
        month = data.get('month')
        page = data.get('page', 1)
        size = data.get('size', 10)
        partner_id, error = get_and_verify_jwt_from_header(False)
        if error:
            return BaseResponse.error(error_code=401, message=error)
        if not month:
            return BaseResponse.error(error_code=1, message="month required")

        # Xử lý month dạng "mmYYYY"
        try:
            # Lấy ngày đầu tiên của tháng
            start_time = datetime.strptime(month, "%m%Y")
            # Lấy số ngày trong tháng
            _, last_day = calendar.monthrange(start_time.year, start_time.month)
            # Lấy ngày cuối cùng của tháng
            end_time = start_time.replace(day=last_day, hour=23, minute=59, second=59)
        except ValueError:
            return BaseResponse.error(error_code=1, message="Invalid month format. Expected format is 'mmYYYY'.")

        # Chuyển múi giờ sang GMT + 7
        user_tz = pytz.timezone('Asia/Bangkok')
        start_time = pytz.utc.localize(start_time).astimezone(user_tz).replace(tzinfo=None)
        end_time = pytz.utc.localize(end_time).astimezone(user_tz).replace(tzinfo=None)

        query_count = """
        WITH checkin_data AS (
            SELECT
                ts.partner_id
            FROM
                member_timesheet ts
            JOIN
                welly_contract wc ON ts.contract_id = wc.id
            WHERE
                ts.contract_id IS NOT NULL
                AND ts.create_date >= %s
                AND ts.create_date <= %s
                AND ts.time_in >= %s
                AND ( ts.time_out <= %s OR ts.time_out is null)
                AND wc.sale_order_template_name_print NOT ILIKE '%%Nội Bộ%%'
            GROUP BY
                ts.partner_id,
                wc.service_type
        )
        SELECT
            COUNT(DISTINCT partner_id) as total_records
        FROM
            checkin_data;
        """
        request.env.cr.execute(query_count, (start_time, end_time, start_time, end_time))
        query_count = request.env.cr.fetchall()

        # phân trang
        offset = (page - 1) * size
        total_records = query_count[0][0]
        total_page = (total_records + size - 1) // size
        index = {
            "page": page,
            "size": size,
            "total_page": total_page,
            "total_record": total_records
        }

        query = """
        WITH checkin_data AS (
            SELECT 
                ts.partner_id,
                wc.service_type,
                COUNT(DISTINCT CASE
                    WHEN wc.service_type = 'member' AND DATE(ts.time_in) = DATE(ts.time_out) OR ts.time_out is null THEN 
                        TO_CHAR(ts.time_in, 'YYYY-MM-DD')
                    ELSE
                        TO_CHAR(ts.id, 'FM9999999999999999999')
                END) AS check_in_count
            FROM 
                member_timesheet ts
            JOIN 
                welly_contract wc ON ts.contract_id = wc.id
            WHERE 
                ts.contract_id IS NOT NULL
                AND ts.create_date >= %s
                AND ts.create_date <= %s
                AND ts.time_in >= %s
                AND ( ts.time_out <= %s OR ts.time_out is null)
                AND wc.sale_order_template_name_print not ilike '%%Nội Bộ%%'
            GROUP BY 
                ts.partner_id,
                wc.service_type
        )
        SELECT 
            partner_id,
            SUM(check_in_count) AS count_check_in
        FROM 
            checkin_data
        GROUP BY 
            partner_id
        ORDER BY
            count_check_in desc
        LIMIT %s OFFSET %s;
        """

        print(request.env.cr.mogrify(query, (start_time, end_time, start_time, end_time, size, offset)))
        request.env.cr.execute(query, (start_time, end_time, start_time, end_time, size, offset))
        query_rs = request.env.cr.fetchall()

        company = request.env['res.company'].sudo().browse(1)
        check_in_top_1 = company.check_in_top_1
        check_in_top_2 = company.check_in_top_2
        check_in_top_3 = company.check_in_top_3

        base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
        # lọc thông tin và trả về response
        response = []
        for partner_id, count_check_in in query_rs:
            medal = None
            if count_check_in >= check_in_top_1:
                medal = 'top_1'
            elif count_check_in >= check_in_top_2:
                medal = 'top_2'
            elif count_check_in >= check_in_top_3:
                medal = 'top_3'

            partner_info = request.env['res.partner'].sudo().browse(int(partner_id))
            avatar = None
            image = request.env['ir.attachment'].sudo().search(
                [('res_model', '=', 'res.partner'), ('res_id', '=', partner_id),
                 ('res_field', '=', 'avatar_mywelly')], limit=1)
            if image:
                access_token = image.generate_access_token()[0]
                avatar = f"{base_url}/web/image/{image.id}/image_128?access_token={access_token}"
            rs = {
                "partner_id": partner_id,
                "partner_name": partner_info.name,
                "partner_avatar": avatar,
                "count_check_in": int(count_check_in),
                "medal": medal
            }
            response.append(rs)

        return BaseResponse.success(data=response, index=index)

    @http.route('/api/getBannerApp', type='http', auth='public', methods=['GET'], csrf=False)
    def get_banner_app(self, **kw):
        try:
            banners = request.env['banner.banner'].sudo().search([('is_mywelly_banner', '=', True)], order='id desc')
            res = []
            for e in banners:
              res.append(e.link)
            return BaseResponse.success(data=res)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/update_gymo_id', type='http', auth='public', methods=['GET'], csrf=False)
    def update_gymo_id(self, **kw):
        try:
            partners_env = request.env['res.partner'].sudo()
            partners = partners_env.search([('welly_id', '=', False)])
            # chia nhỏ danh sách partners thành các danh sách nhỏ có độ dài 200
            #tạo ra các queue với hàm self.with_delay().chunk_update_gymo_id(dict)
            chunk_size = 200
            total = len(partners)
            chunk_count = math.ceil(total / chunk_size)
            for i in range(chunk_count):
                chunk = partners[i * chunk_size:(i + 1) * chunk_size]
                chunk_ids = chunk.ids
                # Đưa vào hàng đợi xử lý
                partners_env.with_delay().chunk_update_gymo_id(chunk_ids)
            return BaseResponse.success(data='Success chunk')
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/timesheet/historyByPartner', type='http', auth='public', csrf=False, methods=['POST'])
    def get_timesheet_by_partner(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.error(error_code=401, message=error)
            time_in_ts = data.get('time_in')
            time_out_ts = data.get('time_out')

            if not partner_id or not time_in_ts or not time_out_ts:
                return BaseResponse.error(data=[], message="thiếu time_in, time_out", error_code=1)

            time_in_dt = self.convert_timestamp_milliseconds_to_utc(time_in_ts)
            time_out_dt = self.convert_timestamp_milliseconds_to_utc(time_out_ts)

            Timesheet = request.env['member.timesheet'].sudo()
            domain = [
                ('partner_id', '=', int(partner_id)),
                ('time_in', '>=', time_in_dt),
                ('time_in', '<=', time_out_dt),
            ]

            records = Timesheet.search(domain, order='time_in desc')
            result = []

            for rec in records:
                result.append({
                    "time_in": self._convert_utc_to_timestamp_milliseconds(rec.time_in) if rec.time_in else None,
                    "time_out": self._convert_utc_to_timestamp_milliseconds(rec.time_out) if rec.time_out else None,
                    "date_str": self._convert_utc_to_client_date_str(rec.time_in) if rec.time_in else None,
                    "time_in_str": self._convert_utc_to_client_hour_str(rec.time_in) if rec.time_in else None,
                    "time_out_str": self._convert_utc_to_client_hour_str(rec.time_out) if rec.time_out else None,
                    "partner_id": rec.partner_id.id,
                    "partner_name": rec.partner_id.name,
                    "type": rec.type,
                    "status": rec.status,
                    "service_type": rec.service_type if rec.service_type else None,
                    "description": rec.sale_order_template_id.name if rec.sale_order_template_id else None,
                    "calendar_id": rec.calendar_id.id if rec.calendar_id else None,
                    "contract_id": rec.contract_id.id if rec.contract_id else None,
                    "contract_name": rec.contract_id.name if rec.contract_id else None,
                })
            return BaseResponse.success(data=result)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    def _convert_utc_to_client_hour_str(self, dt, tz='Asia/Ho_Chi_Minh'):
        """Chuyển UTC datetime về giờ phút giây string theo múi giờ +7"""
        local_dt = pytz.utc.localize(dt).astimezone(pytz.timezone(tz))
        return local_dt.strftime("%H:%M:%S")

    def _convert_utc_to_client_date_str(self, dt, tz='Asia/Ho_Chi_Minh'):
        """Chuyển datetime UTC thành chuỗi ngày định dạng dd/MM/yyyy theo múi giờ client (+7)"""
        if not dt:
            return None
        local_dt = pytz.utc.localize(dt).astimezone(pytz.timezone(tz))
        return local_dt.strftime("%d/%m/%Y")
