# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_payumoney
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# Du<PERSON><PERSON>hi <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Last-Translator: Dung <PERSON><PERSON>hi <<EMAIL>>, 2019\n"
"Language-Team: Vietnamese (https://www.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer__payumoney_merchant_key
msgid "Merchant Key"
msgstr ""

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer__payumoney_merchant_salt
msgid "Merchant Salt"
msgstr ""

#. module: payment_payumoney
#: model:ir.model.fields.selection,name:payment_payumoney.selection__payment_acquirer__provider__payumoney
msgid "PayUmoney"
msgstr "PayUmoney"

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment.py:0
#, python-format
msgid "PayUmoney: invalid shasign, received %s, computed %s, for data %s"
msgstr ""

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment.py:0
#, python-format
msgid "PayUmoney: received data for reference %s; multiple orders found"
msgstr ""

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment.py:0
#, python-format
msgid "PayUmoney: received data for reference %s; no order found"
msgstr ""

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment.py:0
#, python-format
msgid ""
"PayUmoney: received data with missing reference (%s) or pay_id (%s) or "
"shashign (%s)"
msgstr ""

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "NCC dịch vụ Thanh toán"

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_transaction
msgid "Payment Transaction"
msgstr "Giao dịch thanh toán"

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer__provider
msgid "Provider"
msgstr "Nhà cung cấp"
