<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record model="ir.ui.view" id="stock_picking_calendar">
            <field name="name">stock.picking.calendar</field>
            <field name="model">stock.picking</field>
            <field name="priority" eval="2"/>
            <field name="arch" type="xml">
                <calendar string="Calendar View" date_start="scheduled_date" color="partner_id" event_limit="5" quick_add="False">
                    <field name="partner_id" filters="1"/>
                    <field name="origin"/>
                    <field name="picking_type_id"/>
                    <field name="state"/>
                </calendar>
            </field>
        </record>

        <record model="ir.ui.view" id="stock_picking_kanban">
            <field name="name">stock.picking.kanban</field>
            <field name="model">stock.picking</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" sample="1">
                    <field name="name"/>
                    <field name="partner_id"/>
                    <field name="location_dest_id"/>
                    <field name="state"/>
                    <field name="scheduled_date"/>
                    <field name="activity_state"/>
                    <progressbar field="activity_state" colors='{"planned": "success", "today": "warning", "overdue": "danger"}'/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top mb8">
                                    <field name="priority" widget="priority"/>
                                    <div class="o_kanban_record_headings ms-1">
                                        <strong class="o_kanban_record_title"><span><t t-esc="record.name.value"/></span></strong>
                                    </div>
                                    <strong>
                                            <field name="state" widget="label_selection" options="{'classes': {'draft': 'default', 'cancel': 'danger', 'waiting': 'warning', 'confirmed': 'warning', 'done': 'success'}}"/>
                                    </strong>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <t t-esc="record.partner_id.value"/>
                                        <field name="activity_ids" widget="kanban_activity"/>
                                        <field name="json_popover" nolabel="1" widget="stock_rescheduling_popover" attrs="{'invisible': [('json_popover', '=', False)]}"/>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <t t-esc="record.scheduled_date.value and record.scheduled_date.value.split(' ')[0] or False"/>
                                        <field name="user_id" widget="many2one_avatar_user" attrs="{'invisible': [('user_id', '=', False)]}"/>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="vpicktree" model="ir.ui.view">
            <field name="name">stock.picking.tree</field>
            <field name="model">stock.picking</field>
            <field name="arch" type="xml">
                <tree string="Picking list" multi_edit="1" sample="1">
                    <header>
                        <button name="do_unreserve" type="object" string="Unreserve"/>
                        <button name="action_assign" type="object" string="Check Availability"/>
                    </header>
                    <field name="company_id" invisible="1"/>
                    <field name="priority" optional="show" widget="priority" nolabel="1"/>
                    <field name="name" decoration-bf="1"/>
                    <field name="location_id" options="{'no_create': True}" string="From" groups="stock.group_stock_multi_locations" optional="show"/>
                    <field name="location_dest_id" options="{'no_create': True}" string="To" groups="stock.group_stock_multi_locations" optional="show"/>
                    <field name="partner_id" optional="show"/>
                    <field name="is_signed" string="Signed" optional="hide" groups="stock.group_stock_sign_delivery"/>
                    <field name="user_id" optional="hide" widget="many2one_avatar_user"/>
                    <field name="scheduled_date" optional="show" widget="remaining_days" attrs="{'invisible':[('state', 'in', ('done', 'cancel'))]}"/>
                    <field name="picking_type_code" invisible="1"/>
                    <field name="products_availability_state" invisible="1" options='{"lazy": true}'/>
                    <field name="products_availability" options='{"lazy": true}'
                        optional="hide"
                        attrs="{'invisible': ['|', ('picking_type_code', '!=', 'outgoing'), ('state', 'not in', ['confirmed', 'waiting', 'assigned'])]}"
                        decoration-success="state == 'assigned' or products_availability_state == 'available'"
                        decoration-warning="state != 'assigned' and products_availability_state in ('expected', 'available')"
                        decoration-danger="state != 'assigned' and products_availability_state == 'late'"/>
                    <field name="date_deadline" optional="hide" widget="remaining_days" attrs="{'invisible':[('state', 'in', ('done', 'cancel'))]}"/>
                    <field name="date_done" string="Effective Date" optional="hide"/>
                    <field name="origin" optional="show"/>
                    <field name="backorder_id" optional="hide"/>
                    <field name="picking_type_id" optional="hide"/>
                    <field name="company_id" groups="base.group_multi_company" optional="show"/>
                    <field name="state" optional="show" widget="badge" 
                           decoration-danger="state=='cancel'"
                           decoration-info="state== 'assigned'"
                           decoration-muted="state == 'draft'"
                           decoration-success="state == 'done'"
                           decoration-warning="state not in ('draft','cancel','done','assigned')"/>
                    <field name="activity_exception_decoration" widget="activity_exception"/>
                    <field name="json_popover" widget="stock_rescheduling_popover" nolabel="1" attrs="{'invisible': [('json_popover', '=', False)]}"/>
                </tree>
            </field>
        </record>

        <record id="view_picking_form" model="ir.ui.view">
            <field name="name">stock.picking.form</field>
            <field name="model">stock.picking</field>
            <field eval="12" name="priority"/>
            <field name="arch" type="xml">
                <form string="Transfer" js_class="picking_form">

                <field name="is_locked" invisible="1"/>
                <field name="show_mark_as_todo" invisible="1"/>
                <field name="show_check_availability" invisible="1"/>
                <field name="show_validate" invisible="1"/>
                <field name="show_lots_text" invisible="1"/>
                <field name="immediate_transfer" invisible="1"/>
                <field name="picking_type_code" invisible="1"/>
                <field name="hide_picking_type" invisible="1"/>
                <field name="show_operations" invisible="1" readonly="1"/>
                <field name="show_allocation" invisible="1"/>
                <field name="show_reserved" invisible="1" readonly="1"/>
                <field name="move_line_exist" invisible="1"/>
                <field name="has_packages" invisible="1"/>
                <field name="picking_type_entire_packs" invisible="1"/>
                <field name="use_create_lots" invisible="1"/>
                <field name="show_set_qty_button" invisible="1"/>
                <field name="show_clear_qty_button" invisible="1"/>
                <field name="company_id" invisible="1"/>

                <header>
                    <button name="action_confirm" attrs="{'invisible': [('show_mark_as_todo', '=', False)]}" string="Mark as Todo" type="object" class="oe_highlight" groups="base.group_user" data-hotkey="x"/>
                    <button name="action_assign" attrs="{'invisible': [('show_check_availability', '=', False)]}" string="Check Availability" type="object" class="oe_highlight" groups="base.group_user" data-hotkey="q"/>
                    <button name="button_validate" attrs="{'invisible': ['|', ('state', 'in', ('waiting','confirmed')), ('show_validate', '=', False)]}" string="Validate" type="object" class="oe_highlight" groups="stock.group_stock_user" data-hotkey="v"/>
                    <button name="button_validate" attrs="{'invisible': ['|', ('state', 'not in', ('waiting', 'confirmed')), ('show_validate', '=', False)]}" string="Validate" type="object" groups="stock.group_stock_user" class="o_btn_validate" data-hotkey="v"/>
                    <button name="action_set_quantities_to_reservation" attrs="{'invisible': [('show_set_qty_button', '=', False)]}" string="Set quantities" type="object" groups="stock.group_stock_user" class="o_btn_validate" data-hotkey="g"/>
                    <button name="action_clear_quantities_to_zero" attrs="{'invisible': [('show_clear_qty_button', '=', False)]}" string="Clear quantities" type="object" groups="stock.group_stock_user" class="o_btn_validate" data-hotkey="g"/>
                    <widget name="signature" string="Sign" highlight="1"
                            attrs="{'invisible': ['|', '|', ('id', '=', False), ('picking_type_code', '!=', 'outgoing'), ('state', '!=', 'done')]}"
                            full_name="partner_id" groups="stock.group_stock_sign_delivery"/>
                    <widget name="signature" string="Sign"
                            attrs="{'invisible': ['|', '|', ('id', '=', False), ('picking_type_code', '!=', 'outgoing'), ('state', '=', 'done')]}"
                            full_name="partner_id" groups="stock.group_stock_sign_delivery"/>
                    <button name="do_print_picking" string="Print" groups="stock.group_stock_user" type="object" attrs="{'invisible': [('state', '!=', 'assigned')]}" data-hotkey="o"/>
                    <button string="Print Labels" type="object" name="action_open_label_type"/>
                    <button name="%(action_report_delivery)d" string="Print" attrs="{'invisible': [('state', '!=', 'done')]}" type="action" groups="base.group_user" data-hotkey="o"/>
                    <button name="%(act_stock_return_picking)d" string="Return" attrs="{'invisible': [('state', '!=', 'done')]}" type="action" groups="base.group_user" data-hotkey="k"/>
                    <button name="do_unreserve" string="Unreserve" groups="base.group_user" type="object" attrs="{'invisible': ['|', '|', '|', ('picking_type_code', '=', 'incoming'), ('immediate_transfer', '=', True), '&amp;', ('state', '!=', 'assigned'), ('move_type', '!=', 'one'), '&amp;', ('state', 'not in', ('assigned', 'confirmed')), ('move_type', '=', 'one')]}" data-hotkey="w"/>
                    <button name="button_scrap" type="object" string="Scrap" attrs="{'invisible': ['|', '&amp;', ('picking_type_code', '=', 'incoming'), ('state', '!=', 'done'), '&amp;', ('picking_type_code', '=', 'outgoing'), ('state', '=', 'done')]}" data-hotkey="y"/>
                    <button name="action_toggle_is_locked" attrs="{'invisible': ['|', ('state', 'in', ('draft','cancel')), ('is_locked', '=', False)]}" string="Unlock" groups="stock.group_stock_manager" type="object" help="If the picking is unlocked you can edit initial demand (for a draft picking) or done quantities (for a done picking)." data-hotkey="l"/>
                    <button name="action_toggle_is_locked" attrs="{'invisible': [('is_locked', '=', True)]}" string="Lock" groups="stock.group_stock_manager" type="object" data-hotkey="l"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,confirmed,assigned,done" />
                    <button name="action_cancel" attrs="{'invisible': [('state', 'not in', ('assigned', 'confirmed', 'draft', 'waiting'))]}" string="Cancel" groups="base.group_user" type="object" data-hotkey="z"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <field name="has_scrap_move" invisible="True"/>
                        <field name="has_tracking" invisible="True"/>
                        <button name="action_see_move_scrap" string="Scraps" type="object"
                            class="oe_stat_button" icon="fa-arrows-v"
                            attrs="{'invisible': [('has_scrap_move', '=', False)]}"/>
                        <button name="action_see_packages" string="Packages" type="object"
                            class="oe_stat_button" icon="fa-cubes"
                            attrs="{'invisible': [('has_packages', '=', False)]}"/>
                        <button name="%(action_stock_report)d" icon="fa-arrow-up" class="oe_stat_button" string="Traceability" type="action" attrs="{'invisible': ['|', ('state', '!=', 'done'), ('has_tracking', '=', False)]}" groups="stock.group_production_lot"/>
                        <button name="action_view_reception_report" string="Allocation" type="object"
                            context="{'default_picking_ids': [id]}"
                            class="oe_stat_button" icon="fa-list"
                            attrs="{'invisible': [('show_allocation', '=', False)]}"
                            groups="stock.group_reception_report"/>
                        <!-- Use the following button to avoid onchange on one2many -->
                        <button name="action_picking_move_tree"
                            class="oe_stat_button"
                            icon="fa-arrows-v"
                            type="object"
                            help="List view of operations"
                            groups="base.group_no_one"
                            attrs="{'invisible': ['|', '&amp;', ('show_operations', '=', True), '|', ('is_locked', '=', True), ('state', '=', 'done'), '&amp;', ('state', '=', 'done'), ('is_locked', '=', True)]}"
                            context="{'picking_type_code': picking_type_code, 'default_picking_id': id, 'form_view_ref':'stock.view_move_form', 'address_in_id': partner_id, 'default_picking_type_id': picking_type_id, 'default_location_id': location_id, 'default_location_dest_id': location_dest_id}">
                            <div class="o_form_field o_stat_info">
                                <span class="o_stat_text">Operations</span>
                            </div>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1 class="d-flex">
                            <field name="priority" widget="priority" class="me-3" attrs="{'invisible': [('name','=','/')]}"/>
                            <field name="name" attrs="{'invisible': [('name','=','/')]}"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <div class="o_td_label">
                                <label for="partner_id" string="Delivery Address" style="font-weight:bold;"
                                       attrs="{'invisible': [('picking_type_code', '!=', 'outgoing')]}"/>
                                <label for="partner_id" string="Receive From" style="font-weight:bold;"
                                       attrs="{'invisible': [('picking_type_code', '!=', 'incoming')]}"/>
                                <label for="partner_id" string="Contact" style="font-weight:bold;"
                                       attrs="{'invisible': [('picking_type_code', 'in', ['incoming', 'outgoing'])]}"/>
                            </div>
                            <field name="partner_id" nolabel="1"/>
                            <field name="picking_type_id" attrs="{'invisible': [('hide_picking_type', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="location_id" groups="!stock.group_stock_multi_locations" invisible="1"/>
                            <field name="location_dest_id" groups="!stock.group_stock_multi_locations" invisible="1"/>
                            <field name="location_id" options="{'no_create': True}" groups="stock.group_stock_multi_locations" attrs="{'invisible': [('picking_type_code', '=', 'incoming')]}"/>
                            <field name="location_dest_id" options="{'no_create': True}" groups="stock.group_stock_multi_locations" attrs="{'invisible': [('picking_type_code', '=', 'outgoing')]}"/>
                            <field name="backorder_id" attrs="{'invisible': [('backorder_id','=',False)]}"/>
                        </group>
                        <group>
                            <label for="scheduled_date"/>
                            <div class="o_row">
                                <field name="scheduled_date" attrs="{'required': [('id', '!=', False)]}"
                                    decoration-warning="state not in ('done', 'cancel') and scheduled_date &lt; now"
                                    decoration-danger="state not in ('done', 'cancel') and scheduled_date &lt; current_date"
                                    decoration-bf="state not in ('done', 'cancel') and (scheduled_date &lt; current_date or scheduled_date &lt; now)"/>
                                <field name="json_popover" nolabel="1" widget="stock_rescheduling_popover" attrs="{'invisible': [('json_popover', '=', False)]}"/>
                            </div>
                            <field name="date_deadline" 
                                attrs="{'invisible': ['|', ('state', 'in', ('done', 'cancel')), ('date_deadline', '=', False)]}"
                                decoration-danger="date_deadline and date_deadline &lt; current_date"
                                decoration-bf="date_deadline and date_deadline &lt; current_date"/>
                            <field name="products_availability_state" invisible="1"/>
                            <field name="products_availability"
                                attrs="{'invisible': ['|', ('picking_type_code', '!=', 'outgoing'), ('state', 'not in', ['confirmed', 'waiting', 'assigned'])]}"
                                decoration-success="state == 'assigned' or products_availability_state == 'available'"
                                decoration-warning="state != 'assigned' and products_availability_state in ('expected', 'available')"
                                decoration-danger="state != 'assigned' and products_availability_state == 'late'"/>
                            <field name="date_done" string="Effective Date" attrs="{'invisible': [('state', '!=', 'done')]}"/>
                            <field name="origin" placeholder="e.g. PO0032"/>
                            <field name="owner_id" groups="stock.group_tracking_owner"
                                   attrs="{'invisible': [('picking_type_code', '!=', 'incoming')]}"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Detailed Operations"
                            name="detailed_operations"
                            attrs="{'invisible': [('show_operations', '=', False)]}">
                            <field name="move_line_nosuggest_ids"
                                   attrs="{'readonly': ['|', '|', ('show_operations', '=', False), ('state', '=', 'cancel'), '&amp;', ('state', '=', 'done'), ('is_locked', '=', True)], 'invisible': [('show_reserved', '=', True)]}"
                                   context="{'tree_view_ref': 'stock.view_stock_move_line_detailed_operation_tree', 'default_picking_id': id, 'default_location_id': location_id, 'default_location_dest_id': location_dest_id, 'default_company_id': company_id}"/>
                            <field name="move_line_ids_without_package"
                                   attrs="{'readonly': ['|', '|', ('show_operations', '=', False), ('state', '=', 'cancel'), '&amp;', ('state', '=', 'done'), ('is_locked', '=', True)], 'invisible': [('show_reserved', '=', False)]}"
                                   context="{'tree_view_ref': 'stock.view_stock_move_line_detailed_operation_tree', 'default_picking_id': id, 'default_location_id': location_id, 'default_location_dest_id': location_dest_id, 'default_company_id': company_id}"/>
                            <field name="package_level_ids_details"
                                   context="{'default_location_id': location_id, 'default_location_dest_id': location_dest_id, 'default_company_id': company_id}"
                                   attrs="{'readonly': [('state', '=', 'done')], 'invisible': ['|', ('picking_type_entire_packs', '=', False), ('show_operations', '=', False)]}" />
                            <button class="oe_highlight" name="action_put_in_pack" type="object" string="Put in Pack" attrs="{'invisible': [('state', 'in', ('draft', 'done', 'cancel'))]}" groups="stock.group_tracking_lot" data-hotkey="shift+g"/>
                        </page>

                        <page string="Operations" name="operations">
                            <field name="move_ids_without_package" mode="tree,kanban"
                                widget="stock_move_one2many"
                                attrs="{'readonly': ['&amp;', ('state', '=', 'done'), ('is_locked', '=', True)]}"
                                context="{'default_company_id': company_id, 'default_date': scheduled_date, 'default_date_deadline': date_deadline, 'picking_type_code': picking_type_code, 'default_picking_id': id, 'form_view_ref':'stock.view_move_form', 'address_in_id': partner_id, 'default_picking_type_id': picking_type_id, 'default_location_id': location_id, 'default_location_dest_id': location_dest_id, 'default_partner_id': partner_id}"
                                add-label="Add a Product">
                                <tree decoration-danger="not parent.immediate_transfer and state != 'done' and quantity_done > reserved_availability and show_reserved_availability" decoration-muted="scrapped == True or state == 'cancel' or (state == 'done' and is_locked == True)" string="Stock Moves" editable="bottom">
                                    <field name="company_id" invisible="1"/>
                                    <field name="name" invisible="1"/>
                                    <field name="state" invisible="1" readonly="0"/>
                                    <field name="picking_type_id" invisible="1"/>
                                    <field name="location_id" invisible="1"/>
                                    <field name="location_dest_id" invisible="1"/>
                                    <field name="partner_id" invisible="1"/>
                                    <field name="scrapped" invisible="1"/>
                                    <field name="picking_code" invisible="1"/>
                                    <field name="product_type" invisible="1"/>
                                    <field name="show_details_visible" invisible="1"/>
                                    <field name="show_reserved_availability" invisible="1"/>
                                    <field name="show_operations" invisible="1" readonly="1"/>
                                    <field name="additional" invisible="1"/>
                                    <field name="move_lines_count" invisible="1"/>
                                    <field name="is_locked" invisible="1"/>
                                    <field name="product_uom_category_id" invisible="1"/>
                                    <field name="has_tracking" invisible="1"/>
                                    <field name="display_assign_serial" invisible="1"/>
                                    <field name="product_id" required="1" context="{'default_detailed_type': 'product'}" attrs="{'readonly': ['|', '&amp;', ('state', '!=', 'draft'), ('additional', '=', False), ('move_lines_count', '&gt;', 0)]}"/>
                                    <field name="description_picking" string="Description" optional="hide"/>
                                    <field name="date" optional="hide"/>
                                    <field name="date_deadline" optional="hide"/>
                                    <field name="is_initial_demand_editable" invisible="1"/>
                                    <field name="is_quantity_done_editable" invisible="1"/>
                                    <field name="product_packaging_id" groups="product.group_stock_packaging"/>
                                    <field name="product_uom_qty" string="Demand" attrs="{'column_invisible': [('parent.immediate_transfer', '=', True)], 'readonly': ['|', ('is_initial_demand_editable', '=', False), '&amp;', '&amp;', ('show_operations', '=', True), ('is_locked', '=', True), ('is_initial_demand_editable', '=', False)]}"/>
                                    <button type="object" name="action_product_forecast_report" title="Forecast Report" icon="fa-area-chart" 
                                        attrs="{'invisible': ['|', '&amp;', ('reserved_availability', '=', 0), ('forecast_availability', '&lt;=', 0), '|', ('parent.immediate_transfer', '=', True), '&amp;', ('parent.picking_type_code', '=', 'outgoing'), ('state', '!=', 'draft')]}"/>
                                    <button type="object" name="action_product_forecast_report" title="Forecast Report" icon="fa-area-chart text-danger" 
                                        attrs="{'invisible': ['|', '|', ('reserved_availability', '!=', 0), ('forecast_availability', '&gt;', 0), '|', ('parent.immediate_transfer', '=', True), '&amp;', ('parent.picking_type_code', '=', 'outgoing'), ('state', '!=', 'draft')]}"/>
                                    <field name="forecast_expected_date" invisible="1"/>
                                    <field name="forecast_availability" string="Reserved"
                                        attrs="{'column_invisible': ['|', '|', ('parent.state', 'in', ['draft', 'done']), ('parent.picking_type_code', '!=', 'outgoing'), ('parent.immediate_transfer', '=', True)]}" widget="forecast_widget"/>
                                    <field name="reserved_availability" string="Reserved"
                                        attrs="{'column_invisible': ['|', '|', ('parent.state', 'in', ['draft', 'done']), ('parent.picking_type_code', 'in', ['incoming', 'outgoing']), ('parent.immediate_transfer', '=', True)]}"/>
                                    <field name="product_qty" invisible="1" readonly="1"/>
                                    <field name="quantity_done" string="Done" attrs="{'readonly': [('product_id', '=', False)], 'column_invisible':[('parent.state', '=', 'draft'), ('parent.immediate_transfer', '=', False)]}"/>
                                    <field name="product_uom" attrs="{'readonly': [('state', '!=', 'draft'), ('additional', '=', False)]}" options="{'no_open': True, 'no_create': True}" string="Unit of Measure" groups="uom.group_uom"/>
                                    <field name="lot_ids" widget="many2many_tags"
                                        groups="stock.group_production_lot"
                                        attrs="{'invisible': ['|', ('show_details_visible', '=', False), ('has_tracking', '!=', 'serial')]}"
                                        optional="hide"
                                        options="{'create': [('parent.use_create_lots', '=', True)]}"
                                        context="{'default_company_id': company_id, 'default_product_id': product_id, 'active_picking_id': parent.id}"
                                        domain="[('product_id','=',product_id)]"
                                    />
                                    <button name="action_show_details" type="object" icon="fa-list" width="0.1" title="Details"
                                            attrs="{'invisible': [('show_details_visible', '=', False)]}"/>
                                    <button name="action_assign_serial" type="object"
                                            icon="fa-plus-square"
                                            width="0.1"
                                            role="img" title="Assign Serial Numbers"
                                            attrs="{'invisible': ['|', ('display_assign_serial', '=', False), ('show_operations', '=', False)]}"/>
                                </tree>
                                <form string="Stock Moves">
                                    <header>
                                        <field name="state" widget="statusbar"/>
                                    </header>
                                    <group>
                                        <field name="product_uom_category_id" invisible="1"/>
                                        <field name="additional" invisible="1"/>
                                        <field name="move_lines_count" invisible="1"/>
                                        <field name="company_id" invisible="1"/>
                                        <field name="product_id" required="1" attrs="{'readonly': ['|', '&amp;', ('state', '!=', 'draft'), ('additional', '=', False), ('move_lines_count', '&gt;', 0)]}"/>
                                        <field name="is_initial_demand_editable" invisible="1"/>
                                        <field name="is_quantity_done_editable" invisible="1"/>
                                        <field name="product_uom_qty" attrs="{'invisible': [('parent.immediate_transfer', '=', True)], 'readonly': [('is_initial_demand_editable', '=', False)]}"/>
                                        <field name="reserved_availability" string="Reserved" attrs="{'invisible': (['|','|', ('parent.state','=', 'done'), ('parent.picking_type_code', 'in', ['outgoing', 'incoming']), ('parent.immediate_transfer', '=', True)])}"/>
                                        <field name="product_qty" invisible="1" readonly="1"/>
                                        <field name="forecast_expected_date" invisible="1"/>
                                        <field name="forecast_availability" string="Reserved" attrs="{'invisible': ['|', ('parent.picking_type_code', '!=', 'outgoing'), ('parent.state','=', 'done')]}" widget="forecast_widget"/>
                                        <field name="quantity_done" string="Done" attrs="{'readonly': [('product_id', '=', False)]}"/>
                                        <field name="product_uom" attrs="{'readonly': [('state', '!=', 'draft'), ('id', '!=', False)]}" options="{'no_open': True, 'no_create': True}" string="Unit of Measure" groups="uom.group_uom"/>
                                        <field name="description_picking" string="Description"/>
                                    </group>
                                </form>
                            </field>
                            <field name="id" invisible="1"/>
                            <field name="package_level_ids" context="{'default_location_id': location_id, 'default_location_dest_id': location_dest_id, 'default_company_id': company_id}" attrs="{'readonly': [('state', '=', 'done')], 'invisible': ['|', ('picking_type_entire_packs', '=', False), ('show_operations', '=', True)]}" />
                            <button class="oe_highlight" name="action_put_in_pack" type="object" string="Put in Pack" attrs="{'invisible': [('state', 'in', ('draft', 'done', 'cancel'))]}" groups="stock.group_tracking_lot" data-hotkey="shift+g"/>
                        </page>
                        <page string="Additional Info" name="extra">
                            <group>
                                <group string="Other Information" name="other_infos">
                                    <field name="picking_type_code" invisible="1"/>
                                    <field name="move_type" attrs="{'invisible': [('picking_type_code', '=', 'incoming')]}"/>
                                    <field name="user_id" domain="[('share', '=', False)]"/>
                                    <field name="group_id" groups="base.group_no_one"/>
                                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}" force_save="1"/>
                                </group>
                            </group>
                        </page>
                        <page string="Note" name="note">
                            <field name="note" string="Note" placeholder="Add an internal note that will be printed on the Picking Operations sheet"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
                </form>
            </field>
        </record>

        <record id="view_picking_internal_search" model="ir.ui.view">
            <field name="name">stock.picking.internal.search</field>
            <field name="model">stock.picking</field>
            <field name="arch" type="xml">
                <search string="Picking Lists">
                    <field name="name" string="Transfer" filter_domain="['|', ('name', 'ilike', self), ('origin', 'ilike', self)]"/>
                    <field name="partner_id" filter_domain="[('partner_id', 'child_of', self)]"/>
                    <field name="origin"/>
                    <field name="product_id"/>
                    <field name="picking_type_id"/>
                    <field name="move_line_ids"
                        string="Package"
                        filter_domain="['|', ('move_line_ids.package_id.name', 'ilike', self), ('move_line_ids.result_package_id.name', 'ilike', self)]"
                        groups="stock.group_tracking_lot"/>
                    <filter name="to_do_transfers" string="To Do" domain="['&amp;',('user_id', 'in', [uid, False]),('state','not in',['done','cancel'])]"/>
                    <filter name="my_transfers" string="My Transfers" domain="[('user_id', '=', uid)]"/>
                    <filter string="Starred" name="starred" domain="[('priority', '=', '1')]"/>
                    <separator/>
                    <filter name="draft" string="Draft" domain="[('state', '=', 'draft')]" help="Draft Moves"/>
                    <filter name="waiting" string="Waiting" domain="[('state', 'in', ('confirmed', 'waiting'))]" help="Waiting Moves"/>
                    <filter name="available" string="Ready" domain="[('state', '=', 'assigned')]" help="Assigned Moves"/>
                    <filter name="done" string="Done" domain="[('state', '=', 'done')]" help="Pickings already processed"/>
                    <filter name="cancel" string="Cancelled" domain="[('state', '=', 'cancel')]" help="Cancelled Moves"/>
                    <separator/>
                    <filter name="late" string="Late" help="Deadline exceed or/and by the scheduled"
                        domain="[('state', 'in', ('assigned', 'waiting', 'confirmed')), '|', '|', ('has_deadline_issue', '=', True), ('date_deadline', '&lt;', current_date), ('scheduled_date', '&lt;', current_date)]"/> 
                    <filter string="Planning Issues" name="planning_issues" help="Transfers that are late on scheduled time or one of pickings will be late"
                        domain="['|', ('delay_alert_date', '!=', False), '&amp;', ('scheduled_date','&lt;', time.strftime('%Y-%m-%d %H:%M:%S')), ('state', 'in', ('assigned', 'waiting', 'confirmed'))]"/>
                    <separator/>
                    <filter name="backorder" string="Backorders" domain="[('backorder_id', '!=', False), ('state', 'in', ('assigned', 'waiting', 'confirmed'))]" help="Remaining parts of picking partially processed"/>
                    <separator/>
                    <filter invisible="1" string="Late Activities" name="activities_overdue"
                        domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                        help="Show all records which has next action date is before today"/>
                    <filter invisible="1" string="Today Activities" name="activities_today"
                        domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                        domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                    <separator/>
                    <filter string="Warnings" name="activities_exception"
                        domain="[('activity_exception_decoration', '!=', False)]"/>
                    <group expand="0" string="Group By">
                        <filter string="Status" name="status" domain="[]" context="{'group_by': 'state'}"/>
                        <filter string="Scheduled Date" name="expected_date" domain="[]" context="{'group_by': 'scheduled_date'}"/>
                        <filter string="Source Document" name="origin" domain="[]" context="{'group_by': 'origin'}"/>
                        <filter string="Operation Type" name="picking_type" domain="[]" context="{'group_by': 'picking_type_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="action_picking_tree_all" model="ir.actions.act_window">
            <field name="name">Transfers</field>
            <field name="res_model">stock.picking</field>
            <field name="type">ir.actions.act_window</field>
            <field name="view_mode">tree,kanban,form,calendar</field>
            <field name="domain"></field>
            <field name="context">{'contact_display': 'partner_address', 'default_company_id': allowed_company_ids[0]}</field>
            <field name="search_view_id" ref="view_picking_internal_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No transfer found. Let's create one!
                </p><p>
                    Transfers allow you to move products from one location to another.
                </p>
            </field>
        </record>

        <record id="action_validate_picking" model="ir.actions.server">
            <field name="name">Validate</field>
            <field name="model_id" ref="stock.model_stock_picking"/>
            <field name="binding_model_id" ref="stock.model_stock_picking"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">
            if records:
                res = records.button_validate()
                if isinstance(res, dict):
                    action = res
            </field>
        </record>

        <record id="action_unreserve_picking" model="ir.actions.server">
            <field name="name">Unreserve</field>
            <field name="model_id" ref="stock.model_stock_picking"/>
            <field name="binding_model_id" ref="stock.model_stock_picking"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">
            if records:
                records.do_unreserve()
            </field>
        </record>

        <record id="action_lead_mass_mail" model="ir.actions.act_window">
            <field name="name">Send email</field>
            <field name="res_model">mail.compose.message</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="context" eval="{
                'default_composition_mode': 'mass_mail',
                'default_use_template': False,
            }"/>
            <field name="binding_model_id" ref="stock.model_stock_picking"/>
            <field name="binding_view_types">list</field>
        </record>

        <menuitem id="all_picking" name="Transfers" parent="menu_stock_warehouse_mgmt" sequence="20" action="action_picking_tree_all" groups="stock.group_stock_manager,stock.group_stock_user"/>

        <record id="stock_picking_action_picking_type" model="ir.actions.act_window">
            <field name="name">All Transfers</field>
            <field name="res_model">stock.picking</field>
            <field name="type">ir.actions.act_window</field>
            <field name="view_mode">tree,kanban,form,calendar</field>
            <field name="domain"></field>
            <field name="context">{'contact_display': 'partner_address'}</field>
            <field name="search_view_id" ref="view_picking_internal_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No transfer found. Let's create one!
                </p><p>
                    Transfers allow you to move products from one location to another.
                </p>
            </field>
        </record>

        <record id="action_picking_tree_ready" model="ir.actions.act_window">
            <field name="name">To Do</field>
            <field name="res_model">stock.picking</field>
            <field name="type">ir.actions.act_window</field>
            <field name="view_mode">tree,kanban,form,calendar</field>
            <field name="domain"></field>
            <field name="context">{'contact_display': 'partner_address', 'search_default_available': 1}</field>
            <field name="search_view_id" ref="view_picking_internal_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No transfer found. Let's create one!
                </p><p>
                    Transfers allow you to move products from one location to another.
                </p>
            </field>
       </record>

        <record id="action_picking_tree_waiting" model="ir.actions.act_window">
            <field name="name">Waiting Transfers</field>
            <field name="res_model">stock.picking</field>
            <field name="type">ir.actions.act_window</field>
            <field name="view_mode">tree,kanban,form,calendar</field>
            <field name="domain"></field>
            <field name="context">{'contact_display': 'partner_address', 'search_default_waiting': 1}</field>
            <field name="search_view_id" ref="view_picking_internal_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No transfer found. Let's create one!
                </p><p>
                    Transfers allow you to move products from one location to another.
                </p>
            </field>
        </record>

        <record id="action_picking_tree_late" model="ir.actions.act_window">
            <field name="name">Late Transfers</field>
            <field name="res_model">stock.picking</field>
            <field name="type">ir.actions.act_window</field>
            <field name="view_mode">tree,kanban,form,calendar</field>
            <field name="domain"></field>
            <field name="context">{'contact_display': 'partner_address', 'search_default_planning_issues': 1}</field>
            <field name="search_view_id" ref="view_picking_internal_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No transfer found. Let's create one!
                </p><p>
                    Transfers allow you to move products from one location to another.
                </p>
            </field>
        </record>

        <record id="action_picking_tree_backorder" model="ir.actions.act_window">
            <field name="name">Backorders</field>
            <field name="res_model">stock.picking</field>
            <field name="type">ir.actions.act_window</field>
            <field name="view_mode">tree,kanban,form,calendar</field>
            <field name="domain"></field>
            <field name="context">{'contact_display': 'partner_address', 'search_default_backorder': 1}</field>
            <field name="search_view_id" ref="view_picking_internal_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No transfer found. Let's create one!
                </p>
                <p>
                    Transfers allow you to move products from one location to another.
                </p>
            </field>
        </record>

        <record id="action_get_picking_type_operations" model="ir.actions.act_window">
            <field name="name">Operations</field>
            <field name="res_model">stock.move.line</field>
            <field name="type">ir.actions.act_window</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('stock.view_move_line_tree_detailed')})]"/>
            <field name="domain">[('picking_type_id', '=', active_id), ('picking_id', '!=', False)]</field>
            <field name="context">{
                'search_default_todo': '1',
            }</field>
            <field name="search_view_id" ref="stock_move_line_view_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No operations found. Let's create a transfer !
                </p>
                <p>
                    Transfers allow you to move products from one location to another.
                </p>
            </field>
        </record>

        <record id="action_picking_form" model="ir.actions.act_window">
            <field name="name">New Transfer</field>
            <field name="res_model">stock.picking</field>
            <field name="type">ir.actions.act_window</field>
            <field name="view_mode">form</field>
            <field name="domain"></field>
            <field name="context">{
                    'search_default_picking_type_id': [active_id],
                    'default_picking_type_id': active_id,
                    'contact_display': 'partner_address',
            }
            </field>
            <field name="search_view_id" ref="view_picking_internal_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No transfer found. Let's create one!
                </p>
                <p>
                    Transfers allow you to move products from one location to another.
                </p>
            </field>
        </record>
        <record id="do_view_pickings" model="ir.actions.act_window">
            <field name="name">Transfers for Groups</field>
            <field name="res_model">stock.picking</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('group_id','=',active_id)]</field>
        </record>
</odoo>
