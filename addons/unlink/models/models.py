from contextlib import contextmanager
from odoo import api, models
import logging

_logger = logging.getLogger(__name__)
@contextmanager
def set_transaction_isolation(cr, level):
    cr.commit()
    cr.execute(f'SET TRANSACTION ISOLATION LEVEL {level}')
    try:
        yield
        cr.commit()
    except Exception as e:
        _logger.info(f'lỗi rollback {str(e)}')
        cr.rollback()
        raise
    finally:
        cr._cnx.autocommit = True

class IrAttachment(models.Model):
    _inherit = 'ir.attachment'

    def unlink(self):
        isolation_level = self.env['ir.config_parameter'].sudo().get_param('isolation_level')
        with set_transaction_isolation(self.env.cr, str(isolation_level)):
            return super(IrAttachment, self).unlink()

class CalendarAttendee(models.Model):
    _inherit = 'calendar.attendee'

    def unlink(self):
        isolation_level = self.env['ir.config_parameter'].sudo().get_param('isolation_level')
        with set_transaction_isolation(self.env.cr, str(isolation_level)):
            return super(CalendarAttendee, self).unlink()

class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'

    def unlink(self):
        isolation_level = self.env['ir.config_parameter'].sudo().get_param('isolation_level')
        with set_transaction_isolation(self.env.cr, str(isolation_level)):
            return super(AccountMoveLine, self).unlink()

# class AccountMove(models.Model):
#     _inherit = 'account.move'
#
#     def unlink(self):
#         isolation_level = self.env['ir.config_parameter'].sudo().get_param('isolation_level')
#         with set_transaction_isolation(self.env.cr, str(isolation_level)):
#             self.env.cr.execute("SHOW TRANSACTION ISOLATION LEVEL;")
#             level = self.env.cr.fetchone()
#             _logger.info(f"Current account.move Transaction Isolation Level: {level[0]}")
#             return super(AccountMove, self).unlink()


class AccountPartialReconcile(models.Model):
    _inherit = 'account.partial.reconcile'

    def unlink(self):
        isolation_level = self.env['ir.config_parameter'].sudo().get_param('isolation_level')
        with set_transaction_isolation(self.env.cr, str(isolation_level)):
            return super(AccountPartialReconcile, self).unlink()

class MailThread(models.AbstractModel):
    _inherit = 'mail.thread'

    def unlink(self):
        isolation_level = self.env['ir.config_parameter'].sudo().get_param('isolation_level')
        with set_transaction_isolation(self.env.cr, str(isolation_level)):
            return super(MailThread, self).unlink()
        
# class IrModel(models.Model):
#     _inherit = 'ir.model'
#
#     def unlink(self):
#         isolation_level = self.env['ir.config_parameter'].sudo().get_param('isolation_level')
#         with set_transaction_isolation(self.env.cr, str(isolation_level)):
#             return super(IrModel, self).unlink()