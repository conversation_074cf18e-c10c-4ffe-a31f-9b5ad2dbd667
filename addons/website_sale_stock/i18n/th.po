# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_stock
# 
# Translators:
# Khwunchai Jaen<PERSON>awang <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:57+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid ""
"<span attrs=\"{'invisible': [('show_availability', '=', "
"False)]}\">Units</span>"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__stock_notification_partner_ids
msgid "Back in stock Notifications"
msgstr "การแจ้งเตือนเมื่อสินค้ากลับมาในสต็อก"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Continue Selling"
msgstr "ทำการขายต่อ"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__allow_out_of_stock_order
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__allow_out_of_stock_order
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__allow_out_of_stock_order
msgid "Continue selling when out-of-stock"
msgstr "ขายต่อเมื่อหมดสต๊อก"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "Dear Customer,"
msgstr "เรียนลูกค้า,"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid ""
"Default availability mode set on newly created storable products. This can "
"be changed at the product level."
msgstr ""
"ตั้งค่าโหมดความพร้อมใช้งานเริ่มต้นในสินค้าที่สามารถจัดเก็บได้ที่สร้างขึ้นใหม่"
" ซึ่งสามารถเปลี่ยนแปลงได้ที่ระดับสินค้า"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Default visibility for custom messages."
msgstr "การมองเห็นเริ่มต้นสำหรับข้อความที่กำหนดเอง"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "Get notified when back in stock"
msgstr "รับการแจ้งเตือนเมื่อมีสินค้าในสต็อก"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "How to display products having low quantities (on hand - reserved)"
msgstr "วิธีแสดงสินค้าที่มีปริมาณน้อย (คงเหลือ - สำรองไว้)"

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/controllers/main.py:0
#, python-format
msgid "Invalid Email"
msgstr "อีเมลไม่ถูกต้อง"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "Invalid email"
msgstr "อีเมลไม่ถูกต้อง"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Inventory Defaults"
msgstr "ค่าเริ่มต้นของสินค้าคงคลัง"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "Only"
msgstr "เท่านั้น"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "Order Now"
msgstr "สั่งเลย"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "Out of Stock"
msgstr "หมดสต๊อก"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Out-of-Stock"
msgstr "Out-of-Stock"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__out_of_stock_message
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__out_of_stock_message
msgid "Out-of-Stock Message"
msgstr "ข้อความหมดสต๊อก"

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/product_product.py:0
#: model:ir.model,name:website_sale_stock.model_product_template
#, python-format
msgid "Product"
msgstr "สินค้า"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_product_product
msgid "Product Variant"
msgstr "ตัวแปรสินค้า"

#. module: website_sale_stock
#: model:ir.actions.server,name:website_sale_stock.ir_cron_send_availability_email_ir_actions_server
#: model:ir.cron,cron_name:website_sale_stock.ir_cron_send_availability_email
msgid "Product: send email regarding products availability"
msgstr "สินค้า: ส่งอีเมลเกี่ยวกับความพร้อมของผลิตภัณฑ์"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "Regards,"
msgstr "ขอแสดงความนับถือ,"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order
msgid "Sales Order"
msgstr "คำสั่งขาย"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "ไลน์คำสั่งขาย"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Show Available Qty"
msgstr "แสดงจำนวนที่มีอยู่"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__available_threshold
msgid "Show Threshold"
msgstr "แสดงเกณฑ์"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__show_availability
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__show_availability
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__show_availability
msgid "Show availability Qty"
msgstr "แสดงจำนวนที่มีอยู่"

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"Some products became unavailable and your cart has been updated. We're sorry"
" for the inconvenience."
msgstr ""
"สินค้าบางรายการไม่มีอยู่และรถเข็นของคุณได้รับการอัปเดตแล้ว "
"ขออภัยในความไม่สะดวก"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "The following product is now available."
msgstr "มีสินค้าดังต่อไปนี้"

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/product_product.py:0
#, python-format
msgid "The product '%(product_name)s' is now available"
msgstr "สินค้า '%(product_name)s' พร้อมแล้ว"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/website_sale_reorder.js:0
#, python-format
msgid "This product is out of stock."
msgstr "สินค้านี้หมดสต็อก"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_stock_picking
msgid "Transfer"
msgstr "โอน"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
msgid "Units"
msgstr "หน่วย"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__website_warehouse_id
#: model:ir.model.fields,field_description:website_sale_stock.field_website__warehouse_id
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Warehouse"
msgstr "โกดังสินค้า"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "We'll notify you once the product is back in stock."
msgstr "เราจะแจ้งให้คุณทราบเมื่อสินค้ากลับมาอยู่ในสต็อก"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_website
#: model:ir.model.fields,field_description:website_sale_stock.field_stock_picking__website_id
msgid "Website"
msgstr "เว็บไซต์"

#. module: website_sale_stock
#: model:ir.model.fields,help:website_sale_stock.field_stock_picking__website_id
msgid "Website where this order has been placed, for eCommerce orders."
msgstr "เว็บไซต์ที่ส่งคำสั่งซื้อนี้สำหรับคำสั่งซื้ออีคอมเมิร์ซ"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "You already added"
msgstr "คุณได้เพิ่มแล้ว"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "You already added all the available product in your cart."
msgstr "คุณได้เพิ่มสินค้าที่มีทั้งหมดในรถเข็นของคุณแล้ว"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/website_sale_reorder.js:0
#, python-format
msgid "You already have %s Units in your cart."
msgstr "คุณมี %s หน่วย ในตะกร้าสินค้าของคุณแล้ว"

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/sale_order_line.py:0
#, python-format
msgid ""
"You ask for %(desired_qty)s %(product_name)s but only %(new_qty)s is "
"available"
msgstr ""

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/sale_order.py:0
#, python-format
msgid "You ask for %(desired_qty)s products but only %(new_qty)s is available"
msgstr "คุณขอสินค้า %(desired_qty)s แต่มีสินค้าเพียง %(new_qty)s เท่านั้น"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/website_sale_reorder.js:0
#: code:addons/website_sale_stock/static/src/js/website_sale_reorder.js:0
#, python-format
msgid "You ask for %s Units but only %s are available."
msgstr "คุณขอ %s หน่วยแต่มีเพียง %s เท่านั้นที่พร้อมใช้งาน"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "in your cart."
msgstr "ในรถเข็น"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "left in stock."
msgstr "เหลือในสต๊อก"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "only if below"
msgstr "เฉพาะกรณีด้านล่างเท่านั้น"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "<EMAIL>"
msgstr "<EMAIL>"
