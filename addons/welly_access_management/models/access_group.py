from odoo import models, fields, api, Command
from odoo.exceptions import UserError


class AccessGroup(models.Model):
    _name = 'access.group'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Access Group'

    name = fields.Char(
        string='Name',
        required=True,
        tracking=True,
    )
    user_ids = fields.Many2many(
        'res.users',
        string='Users',
        related='group_id.users',
        readonly=False,
        help="Users belonging to the underlying group. Modifications here will affect the group's users.",
        tracking=True,
    )
    implied_ids = fields.Many2many(
        'res.groups',
        string='Inherits',
        related='group_id.implied_ids',
        readonly=False,
        domain = lambda self: [('category_id', '!=', self.env.ref('welly_access_management.module_category_role').id)],
        tracking=True
    )
    group_id = fields.Many2one(
        'res.groups',
        string='User Group',
        ondelete='cascade',
        domain=lambda self: f"[('category_id', '=', {self.env.ref('welly_access_management.module_category_role').id})]",
        tracking=True
    )
    access_management_ids = fields.Many2many(
        'access.management',
        string='Access Management',
        relation='access_management_access_group_rel',
        column1='access_group_id',
        column2='access_management_id',
        tracking=True
    )
    count_users = fields.Integer(
        string='Count Users',
        compute='_compute_count_users',
        store=True,
        tracking=True
    )
    count_access_management = fields.Integer(
        string='Count Access Management',
        compute='_compute_count_access_management'
    )
    # Active
    active = fields.Boolean(
        string='Active',
        default=True
    )
    def action_archive(self):
        res = super(AccessGroup, self).action_archive()
        for rec in self:
            if not rec.active:
                # remove users
                rec.group_id.users = [Command.set([])]
        return res
    
    # Access Rights
    access_right_ids = fields.Many2many(
        'ir.model.access',
        string='Access Rights',
        relation='access_right_access_group_rel',
        column1='access_group_id',
        column2='access_right_id',
        compute='_compute_access_right_ids'
    )

    @api.depends('group_id.implied_ids.model_access', 'group_id.model_access')
    def _compute_access_right_ids(self):
        for rec in self:
            rec.access_right_ids = [Command.set(rec.group_id.implied_ids.model_access.ids + rec.group_id.model_access.ids)]

    # Access Rules
    access_rule_ids = fields.Many2many(
        'ir.rule',
        string='Access Rules',
        relation='access_rule_access_group_rel',
        column1='access_group_id',
        column2='access_rule_id',
        compute='_compute_access_rule_ids'
    )
    @api.depends('group_id.implied_ids.rule_groups', 'group_id.rule_groups')
    def _compute_access_rule_ids(self):
        for rec in self:
            rec.access_rule_ids = [Command.set(rec.group_id.implied_ids.rule_groups.ids + rec.group_id.rule_groups.ids)]

    # Access Menus
    access_menu_ids = fields.Many2many(
        'ir.ui.menu',
        string='Access Menus',
        relation='access_menu_access_group_rel',
        column1='access_group_id',
        column2='access_menu_id',
        compute='_compute_access_menu_ids'
    )
    @api.depends('group_id.implied_ids.menu_access', 'group_id.menu_access')
    def _compute_access_menu_ids(self):
        for rec in self:
            rec.access_menu_ids = [Command.set(rec.group_id.implied_ids.menu_access.ids + rec.group_id.menu_access.ids)]

    # Access Views
    access_view_ids = fields.Many2many(
        'ir.ui.view',
        string='Access Views',
        relation='access_view_access_group_rel',
        column1='access_group_id',
        column2='access_view_id',
        compute='_compute_access_view_ids'
    )
    @api.depends('group_id.implied_ids.view_access', 'group_id.view_access')
    def _compute_access_view_ids(self):
        for rec in self:
            rec.access_view_ids = [Command.set(rec.group_id.implied_ids.view_access.ids + rec.group_id.view_access.ids)]

    _sql_constraints = [
        ('name_uniq', 'unique(name)', 'The name must be unique'),
        ('group_id_uniq', 'unique(group_id)', 'The group must be unique'),
    ]
    
    @api.model_create_multi
    def create(self, vals_list):
        # Tạo nhóm quyền nếu không tồn tại
        for vals in vals_list:
            vals['group_id'] = self.env['res.groups'].create({'name': vals['name'], 'category_id': self.env.ref('welly_access_management.module_category_role').id}).id
        
        return super(AccessGroup, self).create(vals_list)
    
    # Không được sửa User Group
    def write(self, vals):
        if 'group_id' in vals:
            raise UserError('Không thể sửa User Group.')
        return super(AccessGroup, self).write(vals)

    # Check group_id
    @api.constrains('group_id')
    def _check_group_id(self):
        for rec in self:
            if rec.group_id.category_id and rec.group_id.category_id.id != self.env.ref('welly_access_management.module_category_role').id:
                raise UserError('User Group không hợp lệ. Vui lòng chọn User Group hợp lệ.')
    
    @api.onchange('access_management_ids', 'group_id')
    def _compute_group_id_domain(self):
        return {
            'domain': {
                'group_id': [('id', 'not in', self.env['access.group'].search([]).group_id.ids)]
            }
}
    @api.constrains('user_ids')
    def _constrain_user_multiple_roles(self):
        if not self:
            return

        all_relevant_access_groups = self.env['access.group'].search([('group_id', '!=', False)])
        user_to_roles_map:dict[str, list[str]] = {}

        for acc_group in all_relevant_access_groups:
            for user in acc_group.user_ids: # acc_group.user_ids là trường related
                if user.id not in user_to_roles_map:
                    user_to_roles_map[user.id] = []
                user_to_roles_map[user.id].append(acc_group.name)
        
        error_messages = []
        for user_id, role_names in user_to_roles_map.items():
            if len(role_names) > 1:
                user_record = self.env['res.users'].browse(user_id)
                current_roles = ", ".join(role_names)
                error_messages.append(
                    f'• Người dùng "{user_record.name}" đang được gán cho {len(role_names)} vai trò: {current_roles}'
                )
        
        if error_messages:
            main_message = "⚠️ XUNG ĐỘT VAI TRÒ NGƯỜI DÙNG ⚠️\n\n"
            main_message += "Một người dùng chỉ được phép thuộc về MỘT vai trò duy nhất.\n\n"
            main_message += "Các xung đột được phát hiện:\n"
            main_message += "\n".join(error_messages)
            main_message += "\n\n📋 HƯỚNG DẪN KHẮC PHỤC:\n"
            main_message += "1. Truy cập menu 'Vai trò' để xem tất cả vai trò hiện có\n"
            main_message += "2. Tìm vai trò hiện tại của người dùng và loại bỏ họ ra khỏi vai trò đó\n"
            main_message += "3. Sau đó mới gán người dùng vào vai trò mới\n"
            main_message += "\n💡 LƯU Ý: Việc này giúp đảm bảo quyền truy cập rõ ràng và tránh xung đột phân quyền."
            
            raise UserError(main_message)

    @api.depends('user_ids')
    def _compute_count_users(self):
        for rec in self:
            rec.count_users = len(rec.user_ids)
    
    @api.depends('access_management_ids')
    def _compute_count_access_management(self):
        for rec in self:
            rec.count_access_management = len(rec.access_management_ids)
    
    def copy(self, default=None):
        default = dict(default or {})
        # Đổi tên trước khi copy
        default['name'] = f"{self.name} (Nhân bản)"
        return super(AccessGroup, self).copy(default)

    def unlink(self):
        for record in self:
            if record.count_users > 0:
                raise UserError("Không thể xóa nhóm quyền khi còn người dùng trong nhóm.")
            record.group_id.unlink()
        return super(AccessGroup, self).unlink()

    def action_view_users(self):
        self.ensure_one()
        return {
            'name': 'Users',
            'view_mode': 'tree,form',
            'res_model': 'res.users',
            'type': 'ir.actions.act_window',
            'domain': [('id', 'in', self.group_id.users.ids)],
        }

    def action_view_access_management(self):
        self.ensure_one()
        return {
            'name': 'Access Management',
            'view_mode': 'tree,form',
            'res_model': 'access.management',
            'type': 'ir.actions.act_window',
            'domain': [('id', 'in', self.access_management_ids.ids)],
        }
