# -*- coding: utf-8 -*-
from odoo import fields, models, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class ResUsers(models.Model):
    _inherit = 'res.users'

    @api.model
    def default_get(self, fields_list):
        """Override default_get để set default groups cho user mới"""
        defaults = super(ResUsers, self).default_get(fields_list)
        
        # Chỉ set default groups khi tạo user mới từ UI (không phải import hoặc system operations)
        if 'groups_id' in fields_list and self.env.context.get('default_user_template', True):
            # Set default group to only "Club Management/ Người dùng"
            default_group = self.env.ref('welly_base.group_welly_base_user')
            groups = [default_group.id] if default_group else []
            defaults['groups_id'] = [(6, 0, groups)]
        
        return defaults
