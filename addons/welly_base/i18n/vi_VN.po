# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* welly_base
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20231215\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-28 04:48+0000\n"
"PO-Revision-Date: 2023-12-28 04:48+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "(included)."
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid ""
". The journal entries need to be computed by Odoo before\n"
"                        being posted in your company's currency."
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid ""
". You might want to put a higher\n"
"                                number\n"
"                                here."
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.welly_crm_lead_view_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer phone number will also be "
"updated.\" attrs=\"{'invisible': [('partner_phone_update', '=', False)]}\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"Bằng cách lưu thay đổi này, số điện thoại của khách hàng cũng sẽ "
"được cập nhật. \" attrs=\"{'invisible': [('partner_phone_update', '=', "
"False)]}\"/>"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid ""
"<span>This entry has been generated through the Invoicing app,\n"
"                                        before installing Accounting. Its balance has been imported\n"
"                                        separately.\n"
"                                    </span>"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__access_warning
msgid "Access warning"
msgstr ""

#. module: welly_base
#: model:res.groups,name:welly_base.group_welly_account
msgid "Kế toán"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Account Entry"
msgstr ""

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__crm_quotation_partner__type__account_move
msgid "Account Move"
msgstr ""

#. module: welly_base
#: model:ir.actions.act_window,name:welly_base.action_account_move_reject_form
#: model:ir.model,name:welly_base.model_account_move_reject
msgid "Account Move Reject"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Account user approve"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Accounting"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__message_needaction
msgid "Action Needed"
msgstr "Hành động cần thiết"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__welly_contract__state__activated
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_filter
msgid "Activated"
msgstr "Đã kích hoạt"

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/welly_contract.py:0
#, python-format
msgid "Activated!"
msgstr "Đã kích hoạt"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_filter
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_form
msgid "Active"
msgstr "Đang hoạt động"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__activity_ids
msgid "Activities"
msgstr "Hoạt động"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Hoạt động ngoại lệ"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__activity_state
msgid "Activity State"
msgstr "Trạng thái hoạt động"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__activity_type_icon
msgid "Activity Type Icon"
msgstr "Biểu tượng loại hoạt động"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Add a line"
msgstr "Thêm dòng"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Add a note"
msgstr "Thêm ghi chú"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Add a section"
msgstr "Thêm phần"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Add an internal note..."
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__address
#: model:ir.model.fields,field_description:welly_base.field_account_move__address
#: model:ir.model.fields,field_description:welly_base.field_account_payment__address
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__address
msgid "Address"
msgstr "Địa chỉ"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__guardian_address
#: model:ir.model.fields,field_description:welly_base.field_account_move__guardian_address
#: model:ir.model.fields,field_description:welly_base.field_account_payment__guardian_address
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__guardian_address
msgid "Address"
msgstr "Địa chỉ người đại diện pháp luật"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__other_contact_address
#: model:ir.model.fields,field_description:welly_base.field_account_move__other_contact_address
#: model:ir.model.fields,field_description:welly_base.field_account_payment__other_contact_address
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__other_contact_address
msgid "Address"
msgstr "Địa chỉ người liên hệ"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_move__new_contract_id
msgid "New Contract"
msgstr "Hợp đồng của người được chuyển nhượng"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_move__old_contract_id
msgid "Old Contract"
msgstr "Hợp đồng dịch vụ cũ"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_move__service_price
msgid "Service Price"
msgstr "Giá dịch vụ"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_move__service_fee
msgid "Service Fee"
msgstr "Phí dịch vụ"


#. module: welly_base
#: model:res.groups,name:welly_base.group_admin_club
msgid "Admin Club"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_filter
msgid "Admin Confirm"
msgstr ""

#. module: welly_base
#: model:res.groups,name:welly_base.group_admin_online
msgid "Admin Online"
msgstr ""

#. module: welly_base
#: model:res.groups,name:welly_base.group_admin_sales
msgid "Admin Sales"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_payment_line__amount
msgid "Amount"
msgstr "Số tiền"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Approve"
msgstr "Phê duyệt"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__state__approved
msgid "Approved"
msgstr "Đã phê duyệt"

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/account_move.py:0
#: code:addons/welly_base/models/welly_contract.py:0
#, python-format
msgid "Approved successfully."
msgstr "Phê duyệt thành công"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_form
msgid "Are you sure you want to activate this contract ?"
msgstr "Bạn có muốn kích hoạt hợp đồng?"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__message_attachment_count
msgid "Attachment Count"
msgstr "Số lượng tệp đính kèm"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__birthdate
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__second_partner_birthdate
#: model:ir.model.fields,field_description:welly_base.field_account_move__birthdate
#: model:ir.model.fields,field_description:welly_base.field_account_move__second_partner_birthdate
#: model:ir.model.fields,field_description:welly_base.field_account_payment__birthdate
#: model:ir.model.fields,field_description:welly_base.field_account_payment__second_partner_birthdate
#: model:ir.model.fields,field_description:welly_base.field_crm_lead__birthdate
#: model:ir.model.fields,field_description:welly_base.field_res_partner__birthdate
#: model:ir.model.fields,field_description:welly_base.field_res_users__birthdate
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__birthdate
msgid "Birthdate"
msgstr "Ngày sinh"

#. module: welly_base
#: model:ir.actions.report,name:welly_base.welly_recept_report
#: model:ir.actions.report,name:welly_base.welly_recept_total_report
msgid "Biên lai thu tiền"
msgstr ""

#. module: welly_base
#: model:crm.stage,name:welly_base.stage_lead3
msgid "Booking"
msgstr "Đặt lịch hẹn"

#. module: welly_base
#: model:res.groups,name:welly_base.group_coo
msgid "COO"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__coo_name
msgid "COO Name"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_form
msgid "COO Sign"
msgstr "Ký"

#. module: welly_base
#: model:ir.model,name:welly_base.model_crm_stage
msgid "CRM Stages"
msgstr "Trạng thái CRM"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_utm_source__crm_team_id
#: model:ir.model.fields,field_description:welly_base.field_welly_service_type__crm_team_id
msgid "CRM Team"
msgstr "Đội ngũ kinh doanh"

#. module: welly_base
#: model:ir.model,name:welly_base.model_calendar_event
msgid "Calendar Event"
msgstr "Lịch sự kiện"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__crm_stage__stage_type__call
msgid "Call"
msgstr "Gọi Điện"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__utm_campaign_id
#: model:ir.model.fields,field_description:welly_base.field_account_move__utm_campaign_id
#: model:ir.model.fields,field_description:welly_base.field_account_payment__utm_campaign_id
msgid "Campaign"
msgstr "Chiến dịch được sử dụng"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__campaign_id
msgid "Campaign"
msgstr "Chiến dịch"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_crm_lead__can_edit
msgid "Can Edit"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_crm_lead__can_view_schedule_button
msgid "Can View Schedule Button"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.account_move_reject_view_form
#: model_terms:ir.ui.view,arch_db:welly_base.sale_order_reject_view_form
#: model_terms:ir.ui.view,arch_db:welly_base.sale_order_signature_view_form
#: model_terms:ir.ui.view,arch_db:welly_base.view_create_contract_wizard_form
msgid "Cancel"
msgstr "Hủy"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__state__cancel
#: model:ir.model.fields.selection,name:welly_base.selection__welly_contract__state__cancel
msgid "Cancelled"
msgstr "Đã hủy"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__payment_method__cash
msgid "Cash"
msgstr "Tiền mặt"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Cash Basis Entries"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Change Period"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_res_users__is_checkin
msgid "Checkin today"
msgstr "Điểm danh hôm nay"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__class_id
#: model:ir.ui.menu,name:welly_base.menu_welly_class
msgid "Class"
msgstr "Danh Sách Lớp"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_class__name
msgid "Class Name"
msgstr "Tên lớp"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__coach_id
msgid "Coach"
msgstr "Huấn luyện viên"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_crm_lead__code
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__partner_code
msgid "Code"
msgstr "Mã"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_utm_source__company_id
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__company_id
#: model:ir.model.fields,field_description:welly_base.field_welly_service_type__company_id
msgid "Company"
msgstr "Công ty"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__welly_contract__payment_state__completed
msgid "Completed"
msgstr "Hoàn thành"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__computed_domain
#: model:ir.model.fields,field_description:welly_base.field_account_move__computed_domain
#: model:ir.model.fields,field_description:welly_base.field_account_payment__computed_domain
msgid "Computed Domain"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_crm_quotation_partner_form_inherited
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_form
msgid "Confirm"
msgstr "Xác nhận"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__welly_contract__state__confirm_admin
msgid "Confirm Admin"
msgstr "Admin xác nhận"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__welly_contract__state__confirm_recep
msgid "Confirm Receptionist"
msgstr "Lễ tân xác nhận"

#. module: welly_base
#: model:crm.stage,name:welly_base.stage_lead7
msgid "Consider"
msgstr "Pending (Cân nhắc)"

#. module: welly_base
#: model:ir.model,name:welly_base.model_res_partner
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__other_contact_id
#: model:ir.model.fields,field_description:welly_base.field_account_move__other_contact_id
#: model:ir.model.fields,field_description:welly_base.field_account_payment__other_contact_id
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__other_contact_id
msgid "Contact"
msgstr "Người liên hệ"

#. module: welly_base
#: model:ir.model,name:welly_base.model_res_partner
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__guardian_id
#: model:ir.model.fields,field_description:welly_base.field_account_move__guardian_id
#: model:ir.model.fields,field_description:welly_base.field_account_payment__guardian_id
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__guardian_id
msgid "Contact"
msgstr "Người đại diện pháp luật"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__guardian_id_number
#: model:ir.model.fields,field_description:welly_base.field_account_move__guardian_id_number
#: model:ir.model.fields,field_description:welly_base.field_account_payment__guardian_id_number
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__guardian_id_number
msgid "Contact ID"
msgstr "CCCD/Hộ chiếu người đại diện pháp luật"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__other_contact_id_number
#: model:ir.model.fields,field_description:welly_base.field_account_move__other_contact_id_number
#: model:ir.model.fields,field_description:welly_base.field_account_payment__other_contact_id_number
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__other_contact_id_number
msgid "Contact ID"
msgstr "CCCD/Hộ chiếu người liên hệ"

#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__
#: model:ir.model.fields,field_description:welly_base.field_account_move__
#: model:ir.model.fields,field_description:welly_base.field_account_payment__
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__


#. module: welly_base
#: model:ir.actions.act_window,name:welly_base.action_contacts
#: model:ir.ui.menu,name:welly_base.menu_welly_contacts_list
#: model:ir.ui.menu,name:welly_base.welly_menu_contacts
msgid "Contacts"
msgstr "Khách hàng"

#. module: welly_base
#: model:ir.ui.menu,name:welly_base.menu_welly_contract_list
#: model_terms:ir.ui.view,arch_db:welly_base.welly_crm_lead_view_form
msgid "Contract"
msgstr "Hợp Đồng"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_crm_lead__contract_count
msgid "Contract Count"
msgstr "Hợp đồng"

#. module: welly_base
#: model:ir.model,name:welly_base.model_sale_order_signature
msgid "Contract Signature"
msgstr "Hủy hợp đồng"

#. module: welly_base
#: model:ir.ui.menu,name:welly_base.welly_menu_contract
msgid "Contracts"
msgstr "Hợp Đồng"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__coo_signature
msgid "Coo Electronic Signature"
msgstr "Chữ kí COO"

#. module: welly_base
#: model:crm.stage,name:welly_base.stage_lead1
#: model:ir.model.fields.selection,name:welly_base.selection__crm_stage__stage_type__create
msgid "Create"
msgstr "Khởi tạo"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_create_contract_wizard_form
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Create Contract"
msgstr "Tạo hợp đồng mới"

#. module: welly_base
#: model:ir.model,name:welly_base.model_create_pt_contract_wizard
msgid "Create PT Contract Wizard"
msgstr "Tạo hợp đồng PT"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__create_user_id
msgid "Create User"
msgstr ""

#. module: welly_base
#: model_terms:ir.actions.act_window,help:welly_base.action_contacts
#: model_terms:ir.actions.act_window,help:welly_base.action_contacts_user
msgid "Create a Contact in your address book"
msgstr "Tạo một liên hệ trong sổ địa chỉ"

#. module: welly_base
#: model_terms:ir.actions.act_window,help:welly_base.action_move_out_invoice_type_welly
msgid "Create a customer invoice"
msgstr "Tạo hóa đơn khách hàng"

#. module: welly_base
#: model_terms:ir.actions.act_window,help:welly_base.action_move_out_invoice_type_welly
msgid ""
"Create invoices, register payments and keep track of the discussions with your\n"
"                    customers."
msgstr ""

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/account_move.py:0
#, python-format
msgid "Create new contract"
msgstr "Tạo hợp đồng mới"

#. module: welly_base
#: model:ir.model,name:welly_base.model_crm_quotation_partner
msgid "Create new or use existing Customer on new Quotation"
msgstr "Tạo mới hoặc dùng một Khách hàng có sẵn cho Báo giá mới"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_move_reject__create_uid
#: model:ir.model.fields,field_description:welly_base.field_create_pt_contract_wizard__create_uid
#: model:ir.model.fields,field_description:welly_base.field_sale_order_reject__create_uid
#: model:ir.model.fields,field_description:welly_base.field_sale_order_signature__create_uid
#: model:ir.model.fields,field_description:welly_base.field_welly_class__create_uid
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__create_uid
#: model:ir.model.fields,field_description:welly_base.field_welly_exercise_form__create_uid
#: model:ir.model.fields,field_description:welly_base.field_welly_gift__create_uid
#: model:ir.model.fields,field_description:welly_base.field_welly_location__create_uid
#: model:ir.model.fields,field_description:welly_base.field_welly_payment_line__create_uid
#: model:ir.model.fields,field_description:welly_base.field_welly_registration_form__create_uid
#: model:ir.model.fields,field_description:welly_base.field_welly_service_type__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_move_reject__create_date
#: model:ir.model.fields,field_description:welly_base.field_create_pt_contract_wizard__create_date
#: model:ir.model.fields,field_description:welly_base.field_sale_order_reject__create_date
#: model:ir.model.fields,field_description:welly_base.field_sale_order_signature__create_date
#: model:ir.model.fields,field_description:welly_base.field_welly_class__create_date
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__create_date
#: model:ir.model.fields,field_description:welly_base.field_welly_exercise_form__create_date
#: model:ir.model.fields,field_description:welly_base.field_welly_gift__create_date
#: model:ir.model.fields,field_description:welly_base.field_welly_location__create_date
#: model:ir.model.fields,field_description:welly_base.field_welly_payment_line__create_date
#: model:ir.model.fields,field_description:welly_base.field_welly_registration_form__create_date
#: model:ir.model.fields,field_description:welly_base.field_welly_service_type__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__currency_id
#: model:ir.model.fields,field_description:welly_base.field_welly_payment_line__currency_id
msgid "Currency"
msgstr "Tiền tệ"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_crm_lead__partner_id
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__partner_id
msgid "Customer"
msgstr "Khách hàng"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__cus_signature
msgid "Customer Electronic Signature"
msgstr "Chữ ký khách hàng"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__cus_name
msgid "Customer Name"
msgstr "Tên khách hàng"

#. module: welly_base
#: model:ir.model.fields,help:welly_base.field_welly_contract__access_url
msgid "Customer Portal URL"
msgstr "URL cổng khách hàng"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Customer Reference"
msgstr "Tham chiếu khách hàng"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_form
msgid "Customer Sign"
msgstr "Ký"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Cut-Off"
msgstr "Cắt giảm"

#. module: welly_base
#: model:ir.ui.menu,name:welly_base.welly_meta_data
msgid "Data"
msgstr "Cấu hình"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_crm_lead__data_type
msgid "Data Type"
msgstr "Kiểu dữ liệu"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_payment_line__date
msgid "Date"
msgstr "Ngày thanh toán"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__date_end
#: model:ir.model.fields,field_description:welly_base.field_account_move__date_end
#: model:ir.model.fields,field_description:welly_base.field_account_payment__date_end
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__date_end
msgid "Date End"
msgstr "Ngày hết hạn"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__date_start
#: model:ir.model.fields,field_description:welly_base.field_account_move__date_start
#: model:ir.model.fields,field_description:welly_base.field_account_payment__date_start
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__date_start
msgid "Date Start"
msgstr "Ngày kích hoạt"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__payment_method__debit_credit_card
msgid "Debit/Credit Card"
msgstr "Thẻ"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__payment_type_welly__deposit
msgid "Deposit"
msgstr "Trả trước - sử dụng sau"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__payment_type_welly__installment
msgid "Installment"
msgstr "Trả góp"

#. module: welly_base
#. odoo-javascript
#: code:addons/welly_base/static/src/js/calendar_notification_service.js:0
#, python-format
msgid "Details"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Disc.%"
msgstr "% CK"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Discount Amount"
msgstr "Số lượng giảm giá"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Discount Date"
msgstr "Ngày giảm giá"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_move_reject__display_name
#: model:ir.model.fields,field_description:welly_base.field_create_pt_contract_wizard__display_name
#: model:ir.model.fields,field_description:welly_base.field_sale_order_reject__display_name
#: model:ir.model.fields,field_description:welly_base.field_sale_order_signature__display_name
#: model:ir.model.fields,field_description:welly_base.field_welly_class__display_name
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__display_name
#: model:ir.model.fields,field_description:welly_base.field_welly_exercise_form__display_name
#: model:ir.model.fields,field_description:welly_base.field_welly_gift__display_name
#: model:ir.model.fields,field_description:welly_base.field_welly_location__display_name
#: model:ir.model.fields,field_description:welly_base.field_welly_payment_line__display_name
#: model:ir.model.fields,field_description:welly_base.field_welly_registration_form__display_name
#: model:ir.model.fields,field_description:welly_base.field_welly_service_type__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__welly_contract__state__done
msgid "Done"
msgstr "Hoàn thành"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__state__draft
#: model:ir.model.fields.selection,name:welly_base.selection__welly_contract__state__draft
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_filter
msgid "Draft"
msgstr "Dự thảo"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_sale_order_signature__signature
msgid "Electronic Signature"
msgstr "Chữ ký"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__email
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__second_partner_email
#: model:ir.model.fields,field_description:welly_base.field_account_move__email
#: model:ir.model.fields,field_description:welly_base.field_account_move__second_partner_email
#: model:ir.model.fields,field_description:welly_base.field_account_payment__email
#: model:ir.model.fields,field_description:welly_base.field_account_payment__second_partner_email
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__email
msgid "Email"
msgstr ""

#. module: welly_base
#: model:ir.model,name:welly_base.model_calendar_alarm_manager
msgid "Event Alarm Manager"
msgstr "Quản lý cảnh báo sự kiện"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__exercise_form_id
#: model:ir.model.fields,field_description:welly_base.field_account_move__exercise_form_id
#: model:ir.model.fields,field_description:welly_base.field_account_payment__exercise_form_id
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__exercise_form_id
#: model:ir.ui.menu,name:welly_base.menu_welly_exercise_form
msgid "Exercise Form"
msgstr "Hình thức tập"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__crm_lead__gender__female
#: model:ir.model.fields.selection,name:welly_base.selection__res_partner__gender__female
msgid "Female"
msgstr "Nữ"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__message_follower_ids
msgid "Followers"
msgstr "Người theo dõi"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__message_partner_ids
msgid "Followers (Partners)"
msgstr "Người theo dõi (Đối tác)"

#. module: welly_base
#: model:ir.model.fields,help:welly_base.field_welly_contract__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font biểu tượng ví dụ: fa-tasks"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_exercise_form__name
msgid "Form Name"
msgstr "Tên hình thức"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_filter
msgid "Future Activities"
msgstr "Hoạt động tương lai"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__gender
#: model:ir.model.fields,field_description:welly_base.field_account_move__gender
#: model:ir.model.fields,field_description:welly_base.field_account_payment__gender
#: model:ir.model.fields,field_description:welly_base.field_crm_lead__gender
#: model:ir.model.fields,field_description:welly_base.field_res_partner__gender
#: model:ir.model.fields,field_description:welly_base.field_res_users__gender
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__gender
msgid "Gender"
msgstr "Giới tính"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__guardian_gender
#: model:ir.model.fields,field_description:welly_base.field_account_move__guardian_gender
#: model:ir.model.fields,field_description:welly_base.field_account_payment__guardian_gender
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__guardian_gender
msgid "Gender"
msgstr "Giới tính người đại diện pháp luật"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__other_contact_gender
#: model:ir.model.fields,field_description:welly_base.field_account_move__other_contact_gender
#: model:ir.model.fields,field_description:welly_base.field_account_payment__other_contact_gender
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__other_contact_gender
msgid "Gender"
msgstr "Giới tính người liên hệ"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__welly_gift_id
#: model:ir.model.fields,field_description:welly_base.field_account_move__welly_gift_id
#: model:ir.model.fields,field_description:welly_base.field_account_payment__welly_gift_id
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__welly_gift_id
#: model:ir.ui.menu,name:welly_base.menu_welly_gift
msgid "Gift"
msgstr "Danh sách quà tặng"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_gift__name
msgid "Gift Name"
msgstr "Tên quà tặng"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_form
msgid "Guardian"
msgstr "Người đại diện pháp luật"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__has_message
msgid "Has Message"
msgstr "Có tin nhắn"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_sale_order_signature__signer_name
msgid "Họ và tên"
msgstr ""

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/welly_contract.py:0
#, python-format
msgid "HỢp đồng: %s Đã bị hủy với lí do: %s. Vui lòng kiểm tra lại!"
msgstr ""

#. module: welly_base
#: model:ir.actions.report,name:welly_base.welly_contract_coach_report
msgid "Hợp đồng huấn luyện viên"
msgstr ""

#. module: welly_base
#: model:ir.actions.report,name:welly_base.welly_contract_member_report
msgid "Hợp đồng hội viên"
msgstr ""

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/welly_contract.py:0
#, python-format
msgid "Hợp đồng đã bị hủy"
msgstr ""

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/welly_contract.py:0
#, python-format
msgid "Hợp đồng đã được xác nhận không thể bị xóa."
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_form
msgid "I/ THÔNG TIN CHUNG"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_move_reject__id
#: model:ir.model.fields,field_description:welly_base.field_create_pt_contract_wizard__id
#: model:ir.model.fields,field_description:welly_base.field_sale_order_reject__id
#: model:ir.model.fields,field_description:welly_base.field_sale_order_signature__id
#: model:ir.model.fields,field_description:welly_base.field_welly_class__id
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__id
#: model:ir.model.fields,field_description:welly_base.field_welly_exercise_form__id
#: model:ir.model.fields,field_description:welly_base.field_welly_gift__id
#: model:ir.model.fields,field_description:welly_base.field_welly_location__id
#: model:ir.model.fields,field_description:welly_base.field_welly_payment_line__id
#: model:ir.model.fields,field_description:welly_base.field_welly_registration_form__id
#: model:ir.model.fields,field_description:welly_base.field_welly_service_type__id
msgid "ID"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_form
msgid "II/ THÔNG TIN SẢN PHẨM - THANH TOÁN"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__activity_exception_icon
msgid "Icon"
msgstr "Biểu tượng"

#. module: welly_base
#: model:ir.model.fields,help:welly_base.field_welly_contract__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Biểu tượng để chỉ ra một hoạt động ngoại lệ."

#. module: welly_base
#: model:ir.model.fields,help:welly_base.field_welly_contract__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Nếu chọn, bạn cần chú ý tới các tin nhắn mới."

#. module: welly_base
#: model:ir.model.fields,help:welly_base.field_welly_contract__message_has_error
#: model:ir.model.fields,help:welly_base.field_welly_contract__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Nếu chọn, một số tin nhắn sẽ có lỗi gửi."

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__payment_state__in_payment
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "In Payment"
msgstr "Trạng thái thanh toán"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "In order to validate this bill, you must"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "In order to validate this invoice, you must"
msgstr ""

#. module: welly_base
#: model:ir.actions.act_window,name:welly_base.action_contacts_user
#: model:ir.ui.menu,name:welly_base.menu_welly_internal_contact_list
msgid "Internal User"
msgstr "Người Dùng Nội Bộ"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__welly_invoice_id
#: model:ir.ui.menu,name:welly_base.menu_welly_invoices_list
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Invoice"
msgstr "Biên Lai"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_crm_lead__invoice_count
msgid "Invoice Count"
msgstr "Biên lai"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Invoice Lines"
msgstr "Chi tiết dịch vụ"

#. module: welly_base
#: model:ir.actions.act_window,name:welly_base.action_move_out_invoice_type_welly
#: model:ir.model.fields,field_description:welly_base.field_crm_lead__invoice_ids
#: model:ir.ui.menu,name:welly_base.welly_menu_invoices
#: model_terms:ir.ui.view,arch_db:welly_base.welly_crm_lead_view_form
msgid "Invoices"
msgstr "Biên lai"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__payment_state__invoicing_legacy
msgid "Invoicing App Legacy"
msgstr "Ứng dụng lập hóa đơn kế thừa"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_sale_order__is_from_crm_order
msgid "Is CRM Order"
msgstr "Có ticket"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__message_is_follower
msgid "Is Follower"
msgstr "Là người theo dõi"

#. module: welly_base
#: model:ir.model,name:welly_base.model_account_move
msgid "Journal Entry"
msgstr "Bút toán sổ nhật ký"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Journal Items"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_move_reject____last_update
#: model:ir.model.fields,field_description:welly_base.field_create_pt_contract_wizard____last_update
#: model:ir.model.fields,field_description:welly_base.field_sale_order_reject____last_update
#: model:ir.model.fields,field_description:welly_base.field_sale_order_signature____last_update
#: model:ir.model.fields,field_description:welly_base.field_welly_class____last_update
#: model:ir.model.fields,field_description:welly_base.field_welly_contract____last_update
#: model:ir.model.fields,field_description:welly_base.field_welly_exercise_form____last_update
#: model:ir.model.fields,field_description:welly_base.field_welly_gift____last_update
#: model:ir.model.fields,field_description:welly_base.field_welly_location____last_update
#: model:ir.model.fields,field_description:welly_base.field_welly_payment_line____last_update
#: model:ir.model.fields,field_description:welly_base.field_welly_registration_form____last_update
#: model:ir.model.fields,field_description:welly_base.field_welly_service_type____last_update
msgid "Last Modified on"
msgstr "Sửa lần cuối vào"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_move_reject__write_uid
#: model:ir.model.fields,field_description:welly_base.field_create_pt_contract_wizard__write_uid
#: model:ir.model.fields,field_description:welly_base.field_sale_order_reject__write_uid
#: model:ir.model.fields,field_description:welly_base.field_sale_order_signature__write_uid
#: model:ir.model.fields,field_description:welly_base.field_welly_class__write_uid
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__write_uid
#: model:ir.model.fields,field_description:welly_base.field_welly_exercise_form__write_uid
#: model:ir.model.fields,field_description:welly_base.field_welly_gift__write_uid
#: model:ir.model.fields,field_description:welly_base.field_welly_location__write_uid
#: model:ir.model.fields,field_description:welly_base.field_welly_payment_line__write_uid
#: model:ir.model.fields,field_description:welly_base.field_welly_registration_form__write_uid
#: model:ir.model.fields,field_description:welly_base.field_welly_service_type__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_move_reject__write_date
#: model:ir.model.fields,field_description:welly_base.field_create_pt_contract_wizard__write_date
#: model:ir.model.fields,field_description:welly_base.field_sale_order_reject__write_date
#: model:ir.model.fields,field_description:welly_base.field_sale_order_signature__write_date
#: model:ir.model.fields,field_description:welly_base.field_welly_class__write_date
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__write_date
#: model:ir.model.fields,field_description:welly_base.field_welly_exercise_form__write_date
#: model:ir.model.fields,field_description:welly_base.field_welly_gift__write_date
#: model:ir.model.fields,field_description:welly_base.field_welly_location__write_date
#: model:ir.model.fields,field_description:welly_base.field_welly_payment_line__write_date
#: model:ir.model.fields,field_description:welly_base.field_welly_registration_form__write_date
#: model:ir.model.fields,field_description:welly_base.field_welly_service_type__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_filter
msgid "Late Activities"
msgstr "Hoạt động chậm trễ"

#. module: welly_base
#: model:ir.model,name:welly_base.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Lead/Cơ hội"

#. module: welly_base
#: model:ir.model.fields,help:welly_base.field_crm_lead__partner_id
msgid ""
"Linked partner (optional). Usually created when converting the lead. You can"
" find a partner by its Name, TIN, Email or Internal Reference."
msgstr ""
"Đối tác được liên kết (tùy chọn). Thường được tạo khi chuyển đổi lead. Bạn "
"có thể tìm thấy đối tác theo tên, MST, email hoặc mã tham chiếu nội bộ. "

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__welly_location
#: model:ir.model.fields,field_description:welly_base.field_account_move__welly_location
#: model:ir.model.fields,field_description:welly_base.field_account_payment__welly_location
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__welly_location
#: model:ir.ui.menu,name:welly_base.menu_welly_location
msgid "Location"
msgstr "Địa điểm trung tâm"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_location__name
msgid "Location Name"
msgstr "Tên địa điểm"

#. module: welly_base
#: model:crm.stage,name:welly_base.stage_lead9
msgid "Lost"
msgstr "Thất Bại"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__valid_time_type__time
msgid "Lượt"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__message_main_attachment_id
msgid "Main Attachment"
msgstr "Tệp đính kèm chính"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__crm_lead__gender__male
#: model:ir.model.fields.selection,name:welly_base.selection__res_partner__gender__male
msgid "Male"
msgstr "Nam"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__medium_id
msgid "Medium"
msgstr "Phương tiện"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__service_type__member
msgid "Membership"
msgstr "Hội viên"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__sale_order_template__type__membership_card
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_form
msgid "Membership Card"
msgstr "Thẻ thành viên"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__message_has_error
msgid "Message Delivery error"
msgstr "Lỗi gửi tin nhắn"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__message_ids
msgid "Messages"
msgstr "Tin nhắn"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__valid_time_type__month
#: model:ir.model.fields.selection,name:welly_base.selection__welly_contract__valid_time_type__month
msgid "Months"
msgstr "Tháng"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Hạn chót hoạt động của tôi"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_exercise_form__code
msgid "Mã"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_res_partner__welly_code
#: model:ir.model.fields,field_description:welly_base.field_res_users__welly_code
msgid "Mã số"
msgstr ""

#. module: welly_base
#: model:ir.model.constraint,message:welly_base.constraint_welly_exercise_form_unique_code
msgid "Mã đã tồn tại!"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_payment__name
msgid "Name"
msgstr "Số chứng từ"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__name
msgid "Name"
msgstr "Mã hợp đồng"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__nationality_id
#: model:ir.model.fields,field_description:welly_base.field_account_move__nationality_id
#: model:ir.model.fields,field_description:welly_base.field_account_payment__nationality_id
#: model:ir.model.fields,field_description:welly_base.field_crm_lead__nationality_id
#: model:ir.model.fields,field_description:welly_base.field_res_partner__nationality_id
#: model:ir.model.fields,field_description:welly_base.field_res_users__nationality_id
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__nationality_id
msgid "Nationality"
msgstr "Quốc tịch"


#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__guardian_nationality_id
#: model:ir.model.fields,field_description:welly_base.field_account_move__guardian_nationality_id
#: model:ir.model.fields,field_description:welly_base.field_account_payment__guardian_nationality_id
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__guardian_nationality_id
msgid "Nationality"
msgstr "Quốc tịch người giám hộ"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__other_contact_nationality_id
#: model:ir.model.fields,field_description:welly_base.field_account_move__other_contact_nationality_id
#: model:ir.model.fields,field_description:welly_base.field_account_payment__other_contact_nationality_id
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__other_contact_nationality_id
msgid "Nationality"
msgstr "Quốc tịch người liên hệ"

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/crm.py:0
#: code:addons/welly_base/models/crm.py:0
#: code:addons/welly_base/models/crm.py:0
#: code:addons/welly_base/models/welly_contract.py:0
#: code:addons/welly_base/models/welly_contract.py:0
#: code:addons/welly_base/models/welly_contract.py:0
#: model:ir.model.fields.selection,name:welly_base.selection__crm_lead__data_type__new
#, python-format
msgid "New"
msgstr "Mới"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Sự kiện theo lịch cho hoạt động tiếp theo"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Hạn chót cho hoạt động tiếp theo"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__activity_summary
msgid "Next Activity Summary"
msgstr "Tóm tắt hoạt động tiếp theo"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__activity_type_id
msgid "Next Activity Type"
msgstr "Loại hoạt động tiếp theo"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_payment_register__next_payment_date
msgid "Next Payment Date"
msgstr "Ngày thanh toán tiếp theo"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_payment_line__effective_date
#: model:ir.model.fields,field_description:welly_base.field_account_payment_register__effective_date
msgid "Effective Date"
msgstr "Ngày kích hoạt"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_payment_line__expired_date
#: model:ir.model.fields,field_description:welly_base.field_account_payment_register__expired_date
msgid "Expired Date"
msgstr "Ngày hết hạn mới"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Người sử dụng dịch vụ"
msgstr "Người sử dụng dịch vụ"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__welly_contract__payment_state__none
msgid "None"
msgstr "Chưa có"

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/account_payment.py:0
#, python-format
msgid "None Factor"
msgstr ""

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__payment_state__not_paid
msgid "Not Paid"
msgstr "Chưa trả"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__message_needaction_counter
msgid "Number of Actions"
msgstr "Số lượng hành động"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__session_number
#: model:ir.model.fields,field_description:welly_base.field_account_move__session_number
#: model:ir.model.fields,field_description:welly_base.field_account_payment__session_number
#: model:ir.model.fields,field_description:welly_base.field_sale_order_template__session_number
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__session_number
msgid "Number of Session"
msgstr "Số buổi"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__message_has_error_counter
msgid "Number of errors"
msgstr "Số lỗi"

#. module: welly_base
#: model:ir.model.fields,help:welly_base.field_welly_contract__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Số tin nhắn cần xử lý"

#. module: welly_base
#: model:ir.model.fields,help:welly_base.field_welly_contract__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Số tin nhắn bị gửi lỗi"

#. module: welly_base
#. odoo-javascript
#: code:addons/welly_base/static/src/js/calendar_notification_service.js:0
#, python-format
msgid "OK"
msgstr ""

#. module: welly_base
#: model_terms:ir.actions.act_window,help:welly_base.action_contacts
#: model_terms:ir.actions.act_window,help:welly_base.action_contacts_user
msgid "Odoo helps you track all activities related to your contacts."
msgstr "Odoo giúp bạn theo dõi tất cả hoạt động liên quan tới các liên hệ."

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__opportunity_id
#: model:ir.model.fields,field_description:welly_base.field_account_move__opportunity_id
#: model:ir.model.fields,field_description:welly_base.field_account_payment__opportunity_id
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__opportunity_id
msgid "Opportunity"
msgstr "Cơ hội"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__res_partner__gender__other
msgid "Other"
msgstr "Khác"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_form
msgid "Other Contact"
msgstr "Người liên hệ"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Other Info"
msgstr "Người liên hệ"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__service_type__pt
#: model:res.groups,name:welly_base.group_pt
msgid "PT"
msgstr ""

#. module: welly_base
#: model:res.groups,name:welly_base.group_pt_manager
msgid "PT Manager"
msgstr "Quản lý PT"

#. module: welly_base
#: model:res.groups,name:welly_base.group_pilates
msgid "PT Pilates"
msgstr "Đội PT không sale"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__payment_state__paid
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Paid"
msgstr "Đã thanh toán"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__welly_contract__payment_state__partial
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Partial"
msgstr "Thanh toán một phần"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__payment_state__partial
msgid "Partially Paid"
msgstr "Đã trả 1 phần"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_create_pt_contract_wizard__partner_id
msgid "Partner"
msgstr "Người được tạo hợp đồng"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_create_pt_contract_wizard__welly_partner_1_id
msgid "Partner 1"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_create_pt_contract_wizard__welly_partner_2_id
msgid "Partner 2"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__partner_id_number
#: model:ir.model.fields,field_description:welly_base.field_account_move__partner_id_number
#: model:ir.model.fields,field_description:welly_base.field_account_payment__partner_id_number
#: model:ir.model.fields,field_description:welly_base.field_crm_lead__partner_id_number
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__partner_id_number
msgid "Partner ID"
msgstr "CCCD/Hộ chiếu"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_res_partner__partner_id_number
#: model:ir.model.fields,field_description:welly_base.field_res_users__partner_id_number
msgid "Partner ID Number"
msgstr "CCCD/Hộ chiếu"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__pay_amount
#: model:ir.model.fields,field_description:welly_base.field_account_move__pay_amount
#: model:ir.model.fields,field_description:welly_base.field_account_payment__pay_amount
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__pay_amount
msgid "Pay amount"
msgstr "Số tiền bằng số"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__pay_amount_to_text
#: model:ir.model.fields,field_description:welly_base.field_account_move__pay_amount_to_text
#: model:ir.model.fields,field_description:welly_base.field_account_payment__pay_amount_to_text
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__pay_amount_to_text
#: model:ir.model.fields,field_description:welly_base.field_welly_payment_line__pay_amount_to_text
msgid "Pay in Text"
msgstr "Số tiền bằng chữ"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_payment_line__payment_id
#: model:ir.ui.menu,name:welly_base.menu_welly_payment_list
msgid "Payment"
msgstr "Dòng thanh toán"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Payment Lines"
msgstr "Danh sách thanh toán"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__payment_method
#: model:ir.model.fields,field_description:welly_base.field_account_move__payment_method
#: model:ir.model.fields,field_description:welly_base.field_account_payment__payment_method
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__payment_method
msgid "Payment Method"
msgstr "Thanh toán bằng"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__payment_state
msgid "Payment State"
msgstr "Tình trạng thanh toán"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__payment_state
#: model:ir.model.fields,field_description:welly_base.field_account_move__payment_state
#: model:ir.model.fields,field_description:welly_base.field_account_payment__payment_state
msgid "Payment Status"
msgstr "Tình trạng thanh toán"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__payment_type_welly
#: model:ir.model.fields,field_description:welly_base.field_account_move__payment_type_welly
#: model:ir.model.fields,field_description:welly_base.field_account_payment__payment_type_welly
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__payment_type_welly
msgid "Payment Type"
msgstr "Loại thanh toán"

#. module: welly_base
#: model:ir.model,name:welly_base.model_account_payment
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__account_payment_ids
#: model:ir.model.fields,field_description:welly_base.field_account_move__account_payment_ids
#: model:ir.model.fields,field_description:welly_base.field_account_payment__account_payment_ids
msgid "Payments"
msgstr "Danh sách thanh toán"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__pdf_file
#: model:ir.model.fields,field_description:welly_base.field_account_move__pdf_file
#: model:ir.model.fields,field_description:welly_base.field_account_payment__pdf_file
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__pdf_file
msgid "Pdf File"
msgstr ""

#. module: welly_base
#: model:crm.stage,name:welly_base.stage_lead4
msgid "Pending"
msgstr "Pending (Chưa thăm quan)"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__phone
#: model:ir.model.fields,field_description:welly_base.field_account_payment__phone
msgid "Phone"
msgstr "Điện thoại"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.welly_account_payment_form
#: model_terms:ir.ui.view,arch_db:welly_base.welly_account_payment_tree
msgid "Phương Thức Thanh Toán"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_payment_line__journal_id
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_account_payment_register_form_inherit_payment
msgid "Phương thức thanh toán"
msgstr ""

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/welly_contract.py:0
#, python-format
msgid ""
"Please provide an coo electronic signature before confirming the order."
msgstr "Vui lòng cung cấp chữ ký trước khi xác nhận"

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/welly_contract.py:0
#, python-format
msgid ""
"Please provide an customer electronic signature before confirming the order."
msgstr "Vui lòng cung cấp chữ ký khách hàng trước khi xác nhận đơn hàng"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__access_url
msgid "Portal Access URL"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Post"
msgstr "Vào sổ"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__state__posted
msgid "Posted"
msgstr "Đã vào sổ"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Preview"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Preview invoice"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Price"
msgstr "Giá"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Print"
msgstr "In biên lai"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Print Total Receipt"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_crm_lead__pt_team_id_related
msgid "Pt Team Compute"
msgstr ""

#. module: welly_base
#: model:ir.ui.menu,name:welly_base.menu_welly_quotations_list
#: model:ir.ui.menu,name:welly_base.welly_menu_quotations
msgid "Quotation"
msgstr "Báo Giá"

#. module: welly_base
#: model:ir.model,name:welly_base.model_sale_order_template
msgid "Quotation Template"
msgstr "Mẫu báo giá"

#. module: welly_base
#: model:ir.ui.menu,name:welly_base.welly_sale_order_template_menu
msgid "Quotation Templates"
msgstr "Mẫu báo giá"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_res_users__turn_list_present
msgid "Ready in turn list"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__reject_reason
#: model:ir.model.fields,field_description:welly_base.field_account_move__reject_reason
#: model:ir.model.fields,field_description:welly_base.field_account_move_reject__reject_reason
#: model:ir.model.fields,field_description:welly_base.field_account_payment__reject_reason
#: model:ir.model.fields,field_description:welly_base.field_sale_order_reject__reject_reason
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__reject_reason
msgid "Reason"
msgstr "Lí do"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__name
#: model:ir.model.fields,field_description:welly_base.field_account_move__name
msgid "Receipt Code"
msgstr "Mã biên lai"

#. module: welly_base
#: model:ir.actions.act_window,name:welly_base.account_action_recept_new
msgid "Recept"
msgstr "Biên lai"

#. module: welly_base
#: model:res.groups,name:welly_base.group_receptionist
msgid "Receptionist"
msgstr "Lễ tân"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Receptionist submit"
msgstr "Xác nhận"

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/welly_contract.py:0
#: model:ir.model.constraint,message:welly_base.constraint_welly_contract_unique_name
#, python-format
msgid "Record must be unique!"
msgstr ""

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/sale_order.py:0
#: model:ir.model,name:welly_base.model_account_payment_register
#: model_terms:ir.ui.view,arch_db:welly_base.inherit_sale_order_form_view
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
#, python-format
msgid "Register Payment"
msgstr "Ghi nhận thanh toán"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__registration_form_id
#: model:ir.model.fields,field_description:welly_base.field_account_move__registration_form_id
#: model:ir.model.fields,field_description:welly_base.field_account_payment__registration_form_id
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__registration_form_id
#: model:ir.ui.menu,name:welly_base.menu_welly_registration_form
msgid "Registration Form"
msgstr "Hình thức đăng Kí"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_registration_form__name
msgid "Registration Form Name"
msgstr "Hình thức đăng kí"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__welly_contract__state__reject
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_form
msgid "Reject"
msgstr "Hủy bỏ"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_form
msgid "Reject Reason"
msgstr "Lí do hủy bỏ"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__guardian_relationship
#: model:ir.model.fields,field_description:welly_base.field_account_move__guardian_relationship
#: model:ir.model.fields,field_description:welly_base.field_account_payment__guardian_relationship
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__guardian_relationship
msgid "Relationship"
msgstr "Quan hệ với hội viên"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__remitter
#: model:ir.model.fields,field_description:welly_base.field_account_move__remitter
#: model:ir.model.fields,field_description:welly_base.field_account_payment__remitter
msgid "Remitter"
msgstr "Người chuyển tiền"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__remitter_id_number
#: model:ir.model.fields,field_description:welly_base.field_account_move__remitter_id_number
#: model:ir.model.fields,field_description:welly_base.field_account_payment__remitter_id_number
msgid "Remitter ID"
msgstr "CCCD/Hộ chiếu"

#. module: welly_base
#: model:ir.model,name:welly_base.model_py3o_report
msgid "Report Py30"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Reset to Draft"
msgstr "Chuyển về là nháp"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__activity_user_id
msgid "Responsible User"
msgstr "Người phụ trách"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__payment_state__reversed
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Reversed"
msgstr "Đảo ngược"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Lỗi gửi SMS"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__crm_quotation_partner__type__sale_order
msgid "Sale Order"
msgstr "Đơn bán hàng"

#. module: welly_base
#: model:ir.actions.act_window,name:welly_base.action_sale_order_reject_form
#: model:ir.model,name:welly_base.model_sale_order_reject
msgid "Sale Order Reject"
msgstr "Hủy hợp đồng"

#. module: welly_base
#: model:ir.actions.act_window,name:welly_base.action_sale_order_signature_form
msgid "Sale Order Signature"
msgstr "Chữ ký"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__sale_order_template_id
#: model:ir.model.fields,field_description:welly_base.field_account_move__sale_order_template_id
#: model:ir.model.fields,field_description:welly_base.field_account_payment__sale_order_template_id
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__sale_order_template_id
msgid "Sale Order Template"
msgstr "Gói dịch vụ"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__service_type
#: model:ir.model.fields,field_description:welly_base.field_account_move__service_type
msgid "Loại dịch vụ"
msgstr "Loại dịch vụ"

#. module: welly_base
#: model:res.groups,name:welly_base.group_sales
msgid "Sales"
msgstr "Bán hàng"

#. module: welly_base
#: model:ir.model,name:welly_base.model_sale_order
msgid "Sales Order"
msgstr "Đơn bán hàng"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_crm_lead__user_id
msgid "Salesperson"
msgstr "Nhân viên kinh doanh"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__crm_lead__data_type__scan
msgid "Scan"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_filter
msgid "Search Contract"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__access_token
msgid "Security Token"
msgstr "Mã bảo mật"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Send & Print"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_form
msgid "Send Request"
msgstr "Gửi đề nghị"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_form
msgid "Send request to admin"
msgstr "Đề xuất lên admin"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_move__sequence
#: model:ir.model.fields,field_description:welly_base.field_account_payment__sequence
msgid "Sequence"
msgstr "Trình tự"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_crm_lead__service_id
msgid "Service"
msgstr "Dịch vụ"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_service_type__service_code
msgid "Service Code"
msgstr "Mã dịch vụ"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_service_type__name
msgid "Service Name"
msgstr "Tên dịch vụ"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_form
msgid "Service Pack"
msgstr "Gói dịch vụ"


#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__sale_order_template__type__service_pack
msgid "Service pack"
msgstr "Gói dịch vụ"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_form
msgid "Set to Quotation"
msgstr "Đặt về là nháp"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_filter
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__welly_contract__state__sign_coo
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_filter
msgid "Sign COO"
msgstr "COO ký"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__welly_contract__state__sign_customer
msgid "Sign Customer"
msgstr "Khách hàng ký"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_res_partner__user_signature
#: model:ir.model.fields,field_description:welly_base.field_res_users__user_signature
msgid "Signature"
msgstr "Chữ ký"

#. module: welly_base
#. odoo-javascript
#: code:addons/welly_base/static/src/js/calendar_notification_service.js:0
#, python-format
msgid "Snooze"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__source_id
#: model:ir.ui.menu,name:welly_base.menu_welly_source
msgid "Source"
msgstr "Nguồn"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Source Document"
msgstr "Tài liệu nguồn"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_crm_lead__stage_type
#: model:ir.model.fields,field_description:welly_base.field_crm_stage__stage_type
msgid "Stage Type"
msgstr "Loại trạng thái"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__state
#: model:ir.model.fields,field_description:welly_base.field_account_move__state
#: model:ir.model.fields,field_description:welly_base.field_account_payment__state
#: model:ir.model.fields,field_description:welly_base.field_welly_payment_line__state
msgid "Status"
msgstr "Trạng thái bút toán"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__state
msgid "Status"
msgstr "Trạng thái hợp đồng"

#. module: welly_base
#: model:ir.model.fields,help:welly_base.field_welly_contract__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Trạng thái dựa trên hoạt động\n"
"Quá hạn: Hạn chót hạn đã qua\n"
"Hôm nay: Hôm nay là ngày phải thực hiện\n"
"Kế hoạch: Cần thực hiện trong tương lai."

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.account_move_reject_view_form
#: model_terms:ir.ui.view,arch_db:welly_base.sale_order_reject_view_form
#: model_terms:ir.ui.view,arch_db:welly_base.sale_order_signature_view_form
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Submit"
msgstr "Gửi"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Subtotal"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_crm_lead__invoice_amount_total
msgid "Sum of Invoices"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_crm_lead__user_pt_id
msgid "Support PT"
msgstr "PT hỗ trợ"

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/wizard/account_payment_register.py:0
#, python-format
msgid ""
"Số tiền vừa thanh toán: %(paid)s\n"
"Tổng số tiền còn phải thanh toán thanh toán: %(to_pay)s"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Tax Grids"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Taxes Applied"
msgstr ""

#. module: welly_base
#: model:crm.stage,name:welly_base.stage_lead2
msgid "Telesale"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Terms and Conditions"
msgstr "Điều khoản và điều kiện"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid ""
"The\n"
"                                current highest number is"
msgstr ""

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/welly_contract.py:0
#, python-format
msgid "The contract named: %s has been activated!"
msgstr "Hợp đồng %s đã được kích hoạt"

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/welly_contract.py:0
#, python-format
msgid "The contract named: %s need to active!"
msgstr "Hợp đồng %s cần được kích hoạt"

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/welly_contract.py:0
#, python-format
msgid "The contract named: %s need to approve now. Please check it!"
msgstr "Hợp đồng mã số: %s cần được phê duyệt."

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/welly_contract.py:0
#, python-format
msgid "The contract named: %s need to confirm now. Please check it!"
msgstr "Hợp đồng mã số: %s cần được xác nhận."

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/welly_contract.py:0
#, python-format
msgid ""
"The contract named: %s needs to collect customer signatures now. Please "
"check it!"
msgstr "Hợp đồng mã số: %s cần thu thập chữ kí khách hàng."

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/welly_contract.py:0
#, python-format
msgid "The contract needs to collect customer signatures!"
msgstr "Hợp đồng cần thu thập chữ kí khách hàng!"

#. module: welly_base
#: model:ir.model.constraint,message:welly_base.constraint_res_partner_unique_mobile
msgid "The mobile must be unique"
msgstr "Số điện thoại phải là duy nhất"

#. module: welly_base
#: model:ir.model.fields,help:welly_base.field_welly_payment_line__currency_id
msgid "The payment's currency."
msgstr ""

#. module: welly_base
#: model:ir.model.constraint,message:welly_base.constraint_res_partner_unique_phone
msgid "The phone must be unique"
msgstr "Số điện thoại phải là duy nhất"

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/crm.py:0
#, python-format
msgid "The phone number is not registered"
msgstr "Số điện thoại chưa được đăng kí !!!"

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/wizard/account_payment_register.py:0
#, python-format
msgid "The receipt named: %s needs approve!"
msgstr "Biên lai %s cần được phê duyệt."

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/account_move.py:0
#, python-format
msgid "The receipt named: %s needs to create new contract!"
msgstr "Tạo hợp đồng mới cho biên lai %s"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid ""
"The recurrence\n"
"                            will end on"
msgstr ""

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/welly_contract.py:0
#, python-format
msgid "There is a contract that need to active!"
msgstr "Hợp đồng cần kích hoạt."

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/welly_contract.py:0
#, python-format
msgid "There is a contract that need to approve!"
msgstr "Hợp đồng cần được phê duyệt."

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/welly_contract.py:0
#, python-format
msgid "There is a contract that need to confirm!"
msgstr "Hợp đồng cần xác nhận."

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/wizard/account_payment_register.py:0
#, python-format
msgid "There is a receipt that needs approve"
msgstr "Biên lai cần được phê duyệt"

#. module: welly_base
#: model:ir.model.fields,help:welly_base.field_welly_contract__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"Đây là tên giúp bạn theo dõi các chiến dịch marketing khác nhau: ví dụ "
"Fall_Drive, Christmas_Special"

#. module: welly_base
#: model:ir.model.fields,help:welly_base.field_welly_contract__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""
"Đây là phương thức truyền đạt, ví dụ như bưu thiếp, email hoặc banner quảng "
"cáo"

#. module: welly_base
#: model:ir.model.fields,help:welly_base.field_welly_contract__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"Đây là nguồn của liên kết, ví dụ: công cụ tìm kiếm, tên miền khác hoặc tên "
"của danh sách email"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid ""
"This move is configured to be posted automatically at the accounting date:"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.welly_crm_lead_view_form
msgid ""
"This phone number is blacklisted for SMS Marketing. Click to unblacklist."
msgstr ""
"Số điện thoại này được đưa vào danh sách hạn chế SMS Marketing. Bấm để loại "
"khỏi danh sách hạn chế."

#. module: welly_base
#: model:crm.stage,name:welly_base.stage_lead8
msgid "Thành Công"
msgstr ""

#. module: welly_base
#: model:calendar.alarm,name:welly_base.alarm_notif_default
msgid "Thông báo trước 1 giờ"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_filter
msgid "Today Activities"
msgstr "Hoạt động hôm nay"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Total"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Total Credit"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Total Debit"
msgstr ""

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__payment_method__transfer
msgid "Transfer"
msgstr "Chuyển khoản"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_crm_quotation_partner__type
#: model:ir.model.fields,field_description:welly_base.field_sale_order_template__type
msgid "Type"
msgstr "Loại"

#. module: welly_base
#: model:ir.model.fields,help:welly_base.field_welly_contract__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Loại hoạt động ngoại lệ trên tập dữ liệu."

#. module: welly_base
#: model:ir.model,name:welly_base.model_utm_source
msgid "UTM Source"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,help:welly_base.field_crm_lead__invoice_amount_total
msgid "Untaxed Total of Posted Invoice"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "UoM"
msgstr "Đơn vị tính"

#. module: welly_base
#: model:ir.model,name:welly_base.model_res_users
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__user_id
#: model:ir.model.fields,field_description:welly_base.field_account_move__user_id
#: model:ir.model.fields,field_description:welly_base.field_account_payment__user_id
msgid "User"
msgstr "Người dùng"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__valid_time
#: model:ir.model.fields,field_description:welly_base.field_account_move__valid_time
#: model:ir.model.fields,field_description:welly_base.field_account_payment__valid_time
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__valid_time
msgid "Valid Time"
msgstr "Thời hạn"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_bank_statement_line__valid_time_type
#: model:ir.model.fields,field_description:welly_base.field_account_move__valid_time_type
#: model:ir.model.fields,field_description:welly_base.field_account_payment__valid_time_type
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__valid_time_type
msgid "Valid Time Type"
msgstr "Đơn vị tính thời hạn"

#. module: welly_base
#: model:res.groups,name:welly_base.group_view_calendar
msgid "View calendar"
msgstr ""

#. module: welly_base
#: model:crm.stage,name:welly_base.stage_lead5
msgid "Visit"
msgstr "Thăm quan CLB"

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__welly_contract__state__waiting_active
msgid "Waiting Active"
msgstr "Chờ kích hoạt"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "Warning: this bill might be a duplicate of"
msgstr ""

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_contract__website_message_ids
msgid "Website Messages"
msgstr "Thông báo trên trang web"

#. module: welly_base
#: model:ir.model.fields,help:welly_base.field_welly_contract__website_message_ids
msgid "Website communication history"
msgstr "Lịch sử trao đổi qua trang web"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_welly_payment_line__welly_account_move_id
msgid "Welly Account Move"
msgstr ""

#. module: welly_base
#: model:ir.actions.act_window,name:welly_base.welly_class_action
msgid "Welly Classes"
msgstr ""

#. module: welly_base
#: model:ir.actions.act_window,name:welly_base.action_view_welly_contract_tree_welly
#: model:ir.model,name:welly_base.model_welly_contract
#: model_terms:ir.ui.view,arch_db:welly_base.view_welly_contract_form
msgid "Welly Contract"
msgstr "Hợp đồng"

#. module: welly_base
#: model:ir.actions.server,name:welly_base.ir_cron_welly_contract_activation_ir_actions_server
#: model:ir.cron,cron_name:welly_base.ir_cron_welly_contract_activation
msgid "Welly Contract Activation"
msgstr ""

#. module: welly_base
#: model:ir.actions.server,name:welly_base.ir_cron_welly_contract_complete_ir_actions_server
#: model:ir.cron,cron_name:welly_base.ir_cron_welly_contract_complete
msgid "Welly Contract Complete"
msgstr ""

#. module: welly_base
#: model:ir.actions.act_window,name:welly_base.welly_exercise_form_action
msgid "Welly Exercise Form"
msgstr ""

#. module: welly_base
#: model:ir.ui.menu,name:welly_base.welly_menu_root
msgid "Welly Fitness"
msgstr "Bán Hàng"

#. module: welly_base
#: model:ir.actions.act_window,name:welly_base.welly_gift_action
msgid "Welly Gifts"
msgstr "Quà tặng"

#. module: welly_base
#: model:ir.actions.act_window,name:welly_base.welly_location_action
msgid "Welly Locations"
msgstr "Địa điểm"

#. module: welly_base
#: model:ir.module.category,name:welly_base.module_category_welly_base
msgid "Club Management"
msgstr "Quản lý CLB"

#. module: welly_base
#: model:ir.module.category,name:welly_base.module_category_welly_base_pt
msgid "Welly PT"
msgstr "PT"

#. module: welly_base
#: model:ir.module.category,name:welly_base.module_category_welly_base_sale
msgid "Welly Sale"
msgstr "Bán hàng"

#. module: welly_base
#: model:ir.actions.act_window,name:welly_base.welly_service_type_action
msgid "Welly Service Types"
msgstr "Loại hình dịch vụ"

#. module: welly_base
#: model:res.groups,name:welly_base.group_welly_base_user
msgid "Welly User"
msgstr "Người dùng"

#. module: welly_base
#: model:ir.actions.act_window,name:welly_base.welly_registration_form_action
msgid "Welly registration Form"
msgstr "Hình thức đăng kí"

#. module: welly_base
#: model:ir.actions.act_window,name:welly_base.welly_free_service_form_action
msgid "Dịch vụ miễn phí"
msgstr "Dịch vụ miễn phí"

#. module: welly_base
#: model:ir.module.category,description:welly_base.module_category_welly_base
msgid "Welly security"
msgstr "Nhóm Welly"

#. module: welly_base
#: model:ir.module.category,description:welly_base.module_category_welly_base_pt
msgid "Welly security PT"
msgstr ""

#. module: welly_base
#: model:ir.module.category,description:welly_base.module_category_welly_base_sale
msgid "Welly security sale"
msgstr ""

#. module: welly_base
#: model:ir.model.fields.selection,name:welly_base.selection__account_move__valid_time_type__year
#: model:ir.model.fields.selection,name:welly_base.selection__welly_contract__valid_time_type__year
msgid "Years"
msgstr "Năm"

#. module: welly_base
#. odoo-python
#: code:addons/welly_base/models/crm.py:0
#, python-format
msgid "You do not have access right to change stage sequence."
msgstr "Bạn không có quyền truy cập để thay đổi trình tự giai đoạn"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "You have"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid ""
"activate the currency of\n"
"                        the bill"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid ""
"activate the currency of\n"
"                        the invoice"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid ""
"auto-posting enabled. Next\n"
"                        accounting date:"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid ""
"for this customer. You can\n"
"                        allocate them to mark this credit note as paid."
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid ""
"for this customer. You can\n"
"                        allocate them to mark this invoice as paid."
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid ""
"for this vendor. You can allocate\n"
"                        them to mark this bill as paid."
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid ""
"for this vendor. You can allocate\n"
"                        them to mark this credit note as paid."
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid "one of those bills"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid ""
"outstanding\n"
"                                credits"
msgstr ""

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.view_move_form
msgid ""
"outstanding\n"
"                                debits"
msgstr ""

#. module: welly_base
#: model:ir.model,name:welly_base.model_welly_class
msgid "welly.class"
msgstr "Lớp"

#. module: welly_base
#: model:ir.model,name:welly_base.model_welly_exercise_form
msgid "welly.exercise.form"
msgstr ""

#. module: welly_base
#: model:ir.model,name:welly_base.model_welly_gift
msgid "welly.gift"
msgstr "Quà tặng"

#. module: welly_base
#: model:ir.model,name:welly_base.model_welly_location
msgid "welly.location"
msgstr "Địa điểm"

#. module: welly_base
#: model:ir.model,name:welly_base.model_welly_payment_line
msgid "welly.payment.line"
msgstr ""

#. module: welly_base
#: model:ir.model,name:welly_base.model_welly_registration_form
msgid "welly.registration.form"
msgstr "Hình thức đăng kí"

#. module: welly_base
#: model:ir.model,name:welly_base.model_welly_service_type
msgid "welly.service.type"
msgstr "Loại hình dịch vụ"

#. module: calendar
#: model:ir.ui.menu,name:calendar.mail_menu_calendar
msgid "Calendar"
msgstr "Đặt Lịch"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance
msgid "Invoicing"
msgstr "Kế Toán"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__activity_ids
#: model:ir.model.fields,field_description:account.field_account_journal__activity_ids
#: model:ir.model.fields,field_description:account.field_account_move__activity_ids
#: model:ir.model.fields,field_description:account.field_account_payment__activity_ids
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__activity_ids
#: model:ir.model.fields,field_description:account.field_res_partner_bank__activity_ids
msgid "Activities"
msgstr "Công việc"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_opportunity_report_menu_lead
msgid "Leads"
msgstr "Báo cáo cơ hội"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_activity_report_menu
msgid "Activities"
msgstr "Báo cáo công việc"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_my_activities
#: model:ir.ui.menu,name:crm.crm_lead_menu_my_activities
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
msgid "My Activities"
msgstr "Công việc của tôi"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_activity_report_action
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_ids
#: model:ir.ui.menu,name:crm.crm_activity_report_menu
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Activities"
msgstr "Báo cáo công việc"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__mail_activity_type_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Activity Type"
msgstr "Cấu hình công việc"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_menu_config_activity_types
msgid "Activity Types"
msgstr "Cấu hình công việc"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_type_icon
msgid "Activity Type Icon"
msgstr "Biểu tượng công việc"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_config
msgid "Sales Teams"
msgstr "Đội ngũ kinh doanh"

#. module: crm
#: model:ir.actions.act_window,name:crm.act_crm_opportunity_calendar_event_new
#: model:ir.model.fields,field_description:crm.field_crm_lead__calendar_event_ids
msgid "Meetings"
msgstr "Cuộc hẹn"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('calendar_event_count', '&lt;', 2)]}\"> Meetings</span>\n"
"                                    <span class=\"o_stat_text\" attrs=\"{'invisible': [('calendar_event_count', '&gt;', 1)]}\"> Meeting</span>"
msgstr ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('calendar_event_count', '&lt;', 2)]}\"> Cuộc họp</span>\n"
"                                    <span class=\"o_stat_text\" attrs=\"{'invisible': [('calendar_event_count', '&gt;', 1)]}\"> Cuộc hẹn</span>"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__priority
msgid "Priority"
msgstr "Khả năng Close Deal / Độ ưu tiên"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Priority:"
msgstr "Khả năng Close Deal / Độ ưu tiên:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_deadline
msgid "Expected Closing"
msgstr "Ngày chốt dự kiến"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead__date_deadline
msgid "Expected Closing"
msgstr "Ngày chốt dự kiến"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Tag"
msgstr "Tag"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__tag_ids
#: model:ir.ui.menu,name:crm.menu_crm_lead_categ
msgid "Tags"
msgstr "Thẻ"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form
msgid "Employee(s)"
msgstr "Nhân sự"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#: model:ir.actions.act_window,name:crm.crm_lead_action_pipeline
#: model:ir.model.fields,field_description:crm.field_crm_team__use_opportunities
#: model:ir.ui.menu,name:crm.crm_opportunity_report_menu
#: model:ir.ui.menu,name:crm.menu_crm_config_lead
#, python-format
msgid "Pipeline"
msgstr "Cơ hội"

#. module: hr
#: model:ir.actions.act_window,name:hr.act_employee_from_department
#: model:ir.actions.act_window,name:hr.hr_employee_action_from_user
#: model:ir.actions.act_window,name:hr.hr_employee_public_action
#: model:ir.actions.act_window,name:hr.open_view_employee_list
#: model:ir.actions.act_window,name:hr.open_view_employee_list_my
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__employee_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__employee_ids
#: model:ir.model.fields,field_description:hr.field_res_partner__employee_ids
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_tree
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_view_activity
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
#: model_terms:ir.ui.view,arch_db:hr.view_partner_tree2
msgid "Employees"
msgstr "Nhân sự"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_upselling
#: model:ir.ui.menu,name:sale.menu_sale_order_upselling
msgid "Orders to Upsell"
msgstr "Báo cáo phân tích bán hàng"

#. module: sale
#: model:ir.ui.menu,name:sale.res_partner_menu
msgid "Customers"
msgstr "Hội viên"

#. module: account
#: model:ir.actions.act_window,name:account.res_partner_action_customer
msgid "Customers"
msgstr "Hội viên"

#. module: contacts
#: model:ir.actions.act_window,name:contacts.action_contacts
msgid "Contacts"
msgstr "Khách hàng"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_report_crm_lead_salesteam
#: model:ir.actions.act_window,name:crm.crm_opportunity_report_action_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_graph_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_pivot_lead
msgid "Leads Analysis"
msgstr "Báo cáo cơ hội"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.crm_team_action_config
#: model:ir.actions.act_window,name:sales_team.crm_team_action_sales
#: model:ir.model.fields,field_description:sales_team.field_res_users__crm_team_ids
msgid "Sales Teams"
msgstr "Đội ngũ kinh doanh"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Salesteams Search"
msgstr "Tìm kiếm đội ngũ kinh doanh"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.mail_activity_type_action_config_sales
msgid "Activity Types"
msgstr "Cấu hình công việc"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: account
#. odoo-python
#: code:addons/account/wizard/account_automatic_entry_wizard.py:0
#: model:ir.model.fields,field_description:account.field_account_analytic_line__partner_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__partner_id
#: model:ir.model.fields,field_description:account.field_account_move__partner_id
#: model:ir.model.fields,field_description:account.field_account_move_line__partner_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_partner_mapping__partner_id
#: model_terms:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_various_payment_tree
#, python-format
msgid "Partner"
msgstr "Khách hàng"

#. module: account
#. odoo-python
#: code:addons/account/wizard/account_payment_register.py:0
#: model:ir.actions.act_window,name:account.action_account_payments
#: model:ir.actions.act_window,name:account.action_account_payments_payable
#: model:ir.model,name:account.model_account_payment
#: model:ir.model.fields,field_description:account.field_account_move__payment_ids
#: model:ir.model.fields,field_description:account.field_account_payment__payment_ids
#: model:ir.ui.menu,name:account.menu_action_account_payments_payable
#: model:ir.ui.menu,name:account.menu_action_account_payments_receivable
#: model:ir.ui.menu,name:account.root_payment_menu
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#, python-format
msgid "Payments"
msgstr "Danh sách chi tiết thanh toán"


#. module: mail
#. odoo-python
#: code:addons/mail/wizard/base_partner_merge_automatic_wizard.py:0
#, python-format
msgid "Merged with the following partners:"
msgstr "Được hợp nhất với đối tác sau:"

#. module: welly_base
#: model_terms:ir.ui.view,arch_db:welly_base.account_move_search_archive_view_inherit
#: model_terms:ir.ui.view,arch_db:welly_base.account_move_search_archive_view_inherit
msgid "Archived"
msgstr "Đã lưu trữ"

#. module: welly_base
#. odoo-javascript
#: code:addons/welly_base/static/src/js/list_controller.js:0
#: code:addons/welly_base/static/src/js/list_controller.js:0
#, python-format
msgid "Archive receipts will result in cancellation of related payments. Are you sure you want to do this?"
msgstr "Hành động lưu trữ biên lai sẽ dẫn đến hủy các thanh toán liên quan. Bạn chắc chắn muốn làm điều này?"


#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_employee_user
msgid "Employees"
msgstr "Nhân viên"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_employee_payroll
#: model:ir.ui.menu,name:hr.menu_hr_root
msgid "Employees"
msgstr "Nhân Viên"

#. module: base
#: model:ir.model,name:base.model_res_partner
#: model:ir.model.fields,field_description:base.field_res_partner__child_ids
#: model:ir.model.fields,field_description:base.field_res_users__child_ids
#: model:ir.model.fields.selection,name:base.selection__res_partner__type__contact
#: model_terms:ir.ui.view,arch_db:base.view_company_form
#: model_terms:ir.ui.view,arch_db:base.view_partner_simple_form
msgid "Contact"
msgstr "Khách Hàng"

#. module: contacts
#: model:ir.actions.act_window,name:contacts.action_contacts
#: model:ir.ui.menu,name:contacts.menu_contacts
#: model:ir.ui.menu,name:contacts.res_partner_menu_contacts
msgid "Contacts"
msgstr "Khách Hàng"

#. module: sale
#: model:ir.ui.menu,name:sale.product_menu_catalog
msgid "Products"
msgstr "Báo giá"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_template_action
msgid "Products"
msgstr "Bán lẻ"

#. module: crm
#: model:ir.ui.menu,name:crm.menu_crm_opportunities
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "My Pipeline"
msgstr "Cơ hội của tôi"

#. module: sale
#: model:ir.ui.menu,name:sale.sale_menu_root
msgid "Sales"
msgstr "Báo giá"

#. module: base
#: model:ir.model.fields,field_description:base.field_res_company__name
#: model:ir.model.fields,field_description:base.field_res_partner__company_name
#: model:ir.model.fields,field_description:base.field_res_users__company_name
msgid "Company Name"
msgstr "Tên CLB"

#. module: base
#: model:ir.model.fields,field_description:base.field_res_partner__commercial_company_name
#: model:ir.model.fields,field_description:base.field_res_users__commercial_company_name
msgid "Company Name Entity"
msgstr "Thực thể Tên CLB"

#. module: base
#: model_terms:ir.ui.view,arch_db:base.view_partner_form
#: model_terms:ir.ui.view,arch_db:base.view_partner_simple_form
msgid "Company Name..."
msgstr "Tên CLB..."

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "The value of the payment amount must be positive."
msgstr "Giá trị của số tiền thanh toán phải là số dương."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__amount_total_signed
#: model:ir.model.fields,field_description:account.field_account_move__amount_total_signed
#: model:ir.model.fields,field_description:account.field_account_payment__amount_total_signed
msgid "Total Signed"
msgstr "Tổng tiền thanh toán"

#. module: welly_base
#: model:ir.model.fields,field_description:welly_base.field_account_payment__source_id
msgid "Source"
msgstr "Nguồn thanh toán"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__source_id
msgid "Source"
msgstr "Nguồn thanh toán"
