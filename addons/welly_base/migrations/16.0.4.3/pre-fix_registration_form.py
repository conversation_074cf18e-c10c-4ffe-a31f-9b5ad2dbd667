import logging

_logger = logging.getLogger(__name__)


def migrate(cr, version):
    if version == '16.0.4.0':
        _create_welly_registration_form_reserve(cr)


def _create_welly_registration_form_reserve(cr):
    cr.execute("""
        SELECT id FROM welly_registration_form 
        WHERE name ILIKE %s OR name ILIKE %s OR name ILIKE %s 
        LIMIT 1
    """, ('%bảo lưu%', '%bao luu%', '%reserve%'))
    welly_registration_form_reserve_res_id = cr.fetchone()

    if not welly_registration_form_reserve_res_id:
        cr.execute("""
            INSERT INTO welly_registration_form (name, company_id)
            VALUES (%s, %s)
            RETURNING id
        """, ('Bảo Lưu', 1))

        record_id = cr.fetchone()[0]
        cr.execute("""
            INSERT INTO ir_model_data (name, module, model, noupdate, res_id)
            VALUES (%s, %s, %s, %s, %s)
        """, ('welly_registration_form_reserve', 'welly_base', 'welly.registration.form', True,
              record_id))
        _logger.info('Create welly_registration_form_reserve for welly_base module')
        return

    welly_registration_form_reserve_res_id = welly_registration_form_reserve_res_id[0]

    cr.execute("""
        SELECT id FROM ir_model_data 
        WHERE model = %s AND module = %s AND name = %s
    """, ('welly.registration.form', 'welly_base', 'welly_registration_form_reserve'))
    welly_registration_form_reserve = cr.fetchone()

    if not welly_registration_form_reserve:
        cr.execute("""
            INSERT INTO ir_model_data (name, module, model, noupdate, res_id)
            VALUES (%s, %s, %s, %s, %s)
        """, ('welly_registration_form_reserve', 'welly_base', 'welly.registration.form', True,
              welly_registration_form_reserve_res_id))
        _logger.info('Create welly_registration_form_reserve for welly_base module')
