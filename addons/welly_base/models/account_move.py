from datetime import datetime, timedelta

from odoo import api, fields, models, _
from collections import defaultdict
from odoo.exceptions import UserError
from ..fields import selection
from dateutil.relativedelta import relativedelta
from odoo.tools import (
    formatLang,
)


READONLY_FIELD_STATES = {
    state: [('readonly', True)]
    for state in {'sale', 'done', 'cancel'}
}

PAYMENT_STATE_SELECTION = [
    ('not_paid', 'Not Paid'),
    ('in_payment', 'In Payment'),
    ('paid', 'Paid'),
    ('partial', 'Partially Paid'),
    ('reversed', 'Reversed'),
    ('invoicing_legacy', 'Invoicing App Legacy'),
]

SERVICE_FEE_CODE = 'PROD_SERVICE_FEE'

try:
    from num2words import num2words
except ImportError:
    num2words = None


class AccountMove(models.Model):
    _inherit = 'account.move'

    @api.model
    def default_get(self, fields):
        res = super(AccountMove, self).default_get(fields)
        user = self.env.user
        res.update({
            'represent_id': user.partner_id.id,
        })
        return res

    name = fields.Char('Receipt Code', default='/',
                       required=True, readonly=True)
    user_id = fields.Many2one(
        'res.users', default=lambda self: self.env.user, required=True)

    # Thông tin CRM
    opportunity_id = fields.Many2one(
        string='Opportunity',
        comodel_name='crm.lead',
        default=lambda self: self.sale_order_id.opportunity_id,
    )
    
    service_crm_team_id = fields.Many2one('crm.team', string='Phân loại đội ngũ theo Dịch Vụ', store=True, related='opportunity_id.service_crm_team_id')
    source_crm_team_id = fields.Many2one('crm.team', string='Phân loại đội ngũ theo Nguồn', store=True, related='opportunity_id.source_crm_team_id')

    # Trạng thái theo thanh toán. Gốc odoo
    state = fields.Selection(
        selection=[
            ('draft', 'Draft'),
            ('posted', 'Posted'),
            ('approved', 'Approved'),
            ('cancel', 'Cancelled'),
        ],
        string='Trạng thái bút toán',
        required=True,
        readonly=True,
        copy=False,
        tracking=True,
        default='draft',
    )
    # Trạng thái biên lai - custome
    approval_state = fields.Selection(
        selection=[
            ('draft', 'Dự thảo'),
            ('posted', 'Đã vào sổ'),
            ('request_cancel', 'Yêu cầu hủy'),
            ('approved', 'Đã phê duyệt'),
            ('cancel', 'Đã hủy'),
        ],
        string='Trạng thái phê duyệt',
        required=True,
        readonly=True,
        copy=False,
        tracking=True,
        default='draft',
    )
    
    # Trường này được tạo ra để call _compute_state_trigger_cancel khi huỷ biên lai ở bất cứ đâu
    # Trường này sẽ được store để đảm bảo có bất cứ biên lai nào huỷ cũng sẽ cập nhật lại trạng thái của thanh toán
    # Tránh tính toán lại nhiều lần thì không được thêm vào view trường này
    state_trigger_cancel = fields.Boolean(string='State Trigger Cancel', compute='_compute_state_trigger_cancel', store=True)
    @api.depends('state')
    def _compute_state_trigger_cancel(self):
        for record in self:
            record.state_trigger_cancel = record.state == 'cancel'
            if record.state_trigger_cancel:
                record.approval_state = 'cancel'
                # Hủy tất cả các thanh toán liên quan
                for payment in record.account_payment_ids:
                    payment.payment_id.state = 'cancel'
                
                for line in record.invoice_line_ids:
                    if line.payment_id:
                        line.payment_id.state = 'cancel'
                        # Hủy tất cả các phần thanh toán liên quan dến dòng hoá đơn
                        partial = self.env['account.partial.reconcile'].search([('credit_move_id', '=', line.id)], limit=1)
                        partial.debit_move_id.move_id.payment_id.state = 'cancel'
                        partial.credit_move_id.move_id.payment_id.state = 'cancel'
    
    # Thông tin phòng tập
    # trường many2one này vẫn cần giữ vì còn dữ liệu cũ cần merge sang
    welly_location = fields.Many2one(string='Location',
                                     comodel_name='welly.location')
    def _default_welly_location_many2_many(self):
        """Lấy giá trị mặc định cho welly_location_many2_many"""
        default_locations = self.env['welly.location'].search([('is_default', '=', True)])
        return default_locations if default_locations else False
    welly_location_many2_many = fields.Many2many(string='Địa điểm phòng tập',
                                                 comodel_name='welly.location', relation='welly_account_move_location_rel',
                                                 column1='welly_account_move_id', column2='welly_location_id',
                                                 default=lambda self: self._default_welly_location_many2_many())
    service_type = fields.Selection(
        string=selection.ServiceType._string,
        selection=selection.ServiceType._selection,
        compute='_compute_service_type',
        store=True,
        readonly=True
    )
    
    @api.depends('sale_order_id')
    def _compute_service_type(self):
        for record in self:
            if record.is_clone:
                break
            for line in record.invoice_line_ids:
                if line.product_id.welly_service_type:
                    # Mapping dữ liệu từ product sang account move
                    record.service_type = line.product_id.welly_service_type
                    record.valid_time = line.product_id.welly_service_duration
                    record.valid_time_type = line.product_id.welly_service_duration_type
                    # Cần break để chỉ lấy giá trị đầu tiên[Sản phẩm của welly luôn là sản phẩm đầu tiên]
                    break


    # người tạo biên lai, đứng tên sử dụng hợp đồng
    partner_name_print = fields.Char(
        string="Tên khách hàng", compute='_compute_partner_info', store=True)
    # thông tin cccd của khách hàng
    partner_id_number = fields.Char(
        string="Partner ID", compute='_compute_partner_info', store=True)
    identification_card_front = fields.Binary(related='partner_id.identification_card_front')
    identification_card_back = fields.Binary(related='partner_id.identification_card_back')
    gender = fields.Selection(
        string=selection.Gender._string,
        selection=selection.Gender._selection, compute='_compute_partner_info',
        default=selection.Gender.MALE,
        store=True
    )
    address = fields.Char(string="Address", compute='_compute_partner_info', store=True)

    birthdate = fields.Date(
        string='Birthdate', compute='_compute_partner_info', store=True
    )
    nationality_id = fields.Many2one(
        'res.country', string="Nationality", compute='_compute_partner_info', store=True)
    phone = fields.Char(string="Số điện thoại", compute='_compute_partner_info', store=True)
    email = fields.Char(string="Email", compute='_compute_partner_info', store=True)

    # người sử dụng dịch vụ thêm
    partner_account_move_ids = fields.One2many(
        'welly.partner.account.move',
        'account_move_id',
        string='Người đi kèm')
    @api.onchange('partner_account_move_ids')
    def _valid_can_add_partner_account_move(self):
        b = len(self.partner_account_move_ids.partner_id) <= self.family_member_qty and self.is_family_service or not self.is_family_service
        if not b:
            raise UserError('Số lượng thành viên vượt quá số lượng cho phép!')

    @api.onchange('partner_id')
    def _change_partner_id_add_partner_account_move_ids(self):
        if self.partner_id:
            virtual_record = self.env['welly.partner.account.move'].new({
                'partner_id': self.partner_id.id,
                'account_move_id': self.id,
            })
            self.partner_account_move_ids = [(6, 0, [virtual_record.id])]

    # Thời gian sử dụng dich vụ
    date_start = fields.Date(
        string='Date Start'
    )
    date_end = fields.Date(
        string='Date End'
    )

    # tính ngày hết hạn = ngày kích hoạt + thời hạn + quà tặng
    @api.onchange('date_start')
    def _onchange_date_start(self):
        if self.date_start:
            date_end = self.convert_valid_time_type_to_day(self.date_start, self.valid_time, self.valid_time_type)
            if self.welly_gift_id:
                for gift in self.welly_gift_id:
                    date_end = self.convert_valid_time_type_to_day(date_end, gift.valid_time, gift.valid_time_type)
            self.date_end = date_end

    @api.onchange('date_end')
    def _onchange_date_end(self):
        if self.id.origin and not self.date_start:
            raise UserError(_("Bạn cần phải nhập ngày bắt đầu trước."))

    def convert_valid_time_type_to_day(self, date_start, value, unit):
        # Convert string to date object
        date_start_obj = fields.Date.from_string(date_start)

        if unit == 'day':
            # Add days to date_start
            date_end = date_start_obj + relativedelta(days=value)
        elif unit == 'month':
            # Add months to date_start
            date_end = date_start_obj + relativedelta(months=value)
        elif unit == 'year':
            # Add years to date_start
            date_end = date_start_obj + relativedelta(years=value)
        else:
            # Return the start date if no valid unit is found
            date_end = date_start_obj

        # Return the computed date as a string in the format YYYY-MM-DD
        return fields.Date.to_string(date_end)

    represent_id = fields.Many2one('res.partner', string='Người đại diện', domain=lambda self: self._compute_represent_id_domain())

    @api.model
    def _compute_represent_id_domain(self):
        company_id = self.env.company.id
        # Tìm tất cả người dùng thuộc nhóm quyền pt và pt_manager
        users = self.env['res.users'].search([
            ('company_id', '=', company_id)
        ]).mapped(
            'partner_id').ids
        # Trả về domain với danh sách user_ids
        return [('id', 'in', users)]

    valid_time = fields.Integer(string='Valid Time', default=1)
    valid_time_type = fields.Selection(selection=selection.DurationType._selection,
                                       store=True)
    # Hình thức đăng kí
    def _default_registration_form_id(self):
        """Lấy giá trị mặc định cho registration_form_id"""
        default_registration_form = self.env['welly.registration.form'].search([('is_default', '=', True)], limit=1)
        return default_registration_form.id if default_registration_form else False
    registration_form_id = fields.Many2one(
        'welly.registration.form', string='Hình thức đăng ký',
        default=lambda self: self._default_registration_form_id()
    )
    registration_form_name_print = fields.Char(
        string="Tên hình thức đăng ký", compute='_compute_registration_form_info', store=True)
    
    # Thông tin hợp đồng cũ và mới khi hình thức đăng ký là Chuyển Nhượng
    new_contract_id = fields.Many2one('welly.contract', string='New Contract')
    old_contract_id = fields.Many2one('welly.contract', string='Old Contract')
    
    # Thông tin giá và phí dịch vụ khi renew hoặc chuyển nhượng hợp đồng
    service_price = fields.Monetary(string='Giá dịch vụ', readonly=True)
    service_fee = fields.Monetary(string='Phí')
    service_fee_to_text = fields.Char(
        string="Giá dịch vụ bằng chữ", compute="_compute_service_fee_to_text", store=True)
    service_fee_currency_id = fields.Many2one(
        'res.currency', string='Loại tiền tệ giá dịch vụ', default=lambda self: self.env.company.currency_id)
    
    # Thông tin gói gia đình
    is_family_service = fields.Boolean(string='Gói gia đình', readonly=True)
    family_member_qty = fields.Integer(string='Số lượng thành viên', readonly=True)

    # Thông tin thanh toán
    pay_amount = fields.Monetary(
        string='Tổng tiền bằng số', compute="compute_pay_amount"
    )
    pay_amount_to_text = fields.Char(
        string="Tổng tiền bằng chữ", compute="_compute_text_amount", store=True)
    payment_method = fields.Selection(
        string='Hình thức thanh toán',
        selection=[('cash', 'Cash'), ('transfer', 'Transfer'),
                   ('debit_credit_card', 'Debit/Credit Card')],
        required=True, default='cash'
    )
    payment_type_welly = fields.Selection(
        string='Loại thanh toán',
        selection=[('deposit', 'Deposit'), ('installment', 'Installment')], default='deposit'
    )

    # Thông tin hợp đồng
    sale_order_template_id = fields.Many2one(
        comodel_name='sale.order.template',
        related='sale_order_id.sale_order_template_id',
        string="Gói dịch vụ/Mẫu báo giá"
    )
    sale_order_template_name_print = fields.Char(
        string="Tên gói dịch vụ", compute='_compute_sale_order_template_info', store=True)

    exercise_form_id = fields.Many2one(
        'welly.exercise.form', string='Exercise Form', store=True, compute='_compute_product_info')
    sale_order_id = fields.Many2one(
        'sale.order',
        string='Đơn bán hàng',
        default=lambda self: self._context.get('active_id') if self._context.get('active_model') == 'sale.order' else False,
    )
    
    # Thông tin dịch vụ
    welly_service_ids = fields.Many2many(comodel_name='welly.service.type', string='Dịch vụ sử dụng', store=True, compute='_compute_product_info')
    
    @api.depends('sale_order_id')
    def _compute_product_info(self):
        for record in self:
            if record.is_clone:
                break
            if record.sale_order_template_id:
                # Lấy thông tin dịch vụ từ template sản phẩm(Lấy sản phẩm đầu tiên là sản phẩm chính)
                for line in record.sale_order_template_id.sale_order_template_line_ids:
                    record.exercise_form_id = line.product_id.exercise_form_id
                    record.welly_service_ids = line.product_id.welly_service_ids
                    break
    
    exercise_form_name_print = fields.Char(
        string="Tên hình thức tập luyện", related="exercise_form_id.name", store=True)

    session_number = fields.Integer('Number of Session' , store=True)

    # Thông người liên hệ
    other_contact_id = fields.Many2one('res.partner', string="Người liên hệ",
                                       )
    other_contact_name_print = fields.Char(
        string="Tên người liên hệ", compute='_compute_other_contact_info', store=True)
    other_contact_id_number = fields.Char(
        string="CCCD người liên hệ", compute='_compute_other_contact_info', store=True)
    other_contact_gender = fields.Selection(
        string="Giới tính người liên hệ",
        selection=selection.Gender._selection, compute='_compute_other_contact_info', store=True)
    other_contact_address = fields.Char(
        string="Địa chỉ người liên hệ", compute='_compute_other_contact_info', store=True)
    other_contact_nationality_id = fields.Many2one('res.country', string="Quốc tịch người liên hệ",
                                                   compute='_compute_other_contact_info', store=True)
    other_contact_phone = fields.Char(
        string="SĐT người liên hệ", compute='_compute_other_contact_info', store=True)

    # Thông tin người giám hộ
    guardian_id = fields.Many2one('res.partner', string="Người giám hộ")
    guardian_name_print = fields.Char(
        string="Tên người giám hộ", compute='_compute_guardian_contact_info', store=True)
    guardian_id_number = fields.Char(
        string="CCCD người giám hộ", compute='_compute_guardian_contact_info', store=True)
    guardian_relationship = fields.Char(string="Relationship")
    guardian_gender = fields.Selection(
        string="Giới tính người giám hộ",
        selection=selection.Gender._selection, default=selection.Gender.MALE, compute='_compute_guardian_contact_info',
        store=True)
    guardian_address = fields.Char(
        string="Địa chỉ người giám hộ", compute='_compute_guardian_contact_info', store=True)
    guardian_nationality_id = fields.Many2one('res.country', string="Quốc tịch người giám hộ",
                                              compute='_compute_guardian_contact_info', store=True)
    guardian_phone = fields.Char(string="SĐT người giám hộ", compute='_compute_guardian_contact_info', store=True)

    # Thông tin chiến dịch
    utm_campaign_id = fields.Many2one(
        'utm.campaign', 'Chiến dịch được sử dụng', index=True, ondelete='set null')
    utm_campaign_name_print = fields.Char(
        string="Tên chiến dịch được sử dụng", compute='_compute_campaign_info', store=True)

    # Thông tin quà tặng
    welly_gift_id = fields.Many2many(string='Gift', comodel_name='welly.gift', relation='welly_account_move_gift_rel',
                                     column1='welly_account_move_id', column2='welly_gift_id')
    welly_gift_name_print = fields.Char(string='Tên quà tặng', compute='_compute_gift_info', store=True)

    # File đính kèm
    # pdf_file = fields.Binary(string='Pdf File', attachment=True)

    reject_reason = fields.Text(string='Reason')
    sequence = fields.Integer(string='Sequence', default=10)

    # Thông tin thanh toán
    account_payment_ids = fields.One2many('welly.payment.line', 'welly_account_move_id', string='Thông tin thanh toán')
    payment_state = fields.Selection(
        selection=PAYMENT_STATE_SELECTION,
        string="Payment Status",
        compute='_compute_payment_state', store=True, readonly=True,
        copy=False,
        tracking=True,
    )
    
    ###########################################################################
    user_company_ids = fields.Many2many('res.company', related='opportunity_id.user_company_ids', store=False,
                                        readonly=True)
    pt_team_id_related = fields.Many2one('crm.team', string='PT Team', related='opportunity_id.team_id', store=False,
                                         readonly=True)
    coach_id = fields.Many2one(
        'res.users',
        string='Huấn luyện viên',
        domain=lambda self: self._compute_coach_domain()
    )
    

    @api.model
    def _compute_coach_domain(self):
        # lấy biến môi trường id của department_pt
        company_id = self.env.company.id
        group_welly_account = self.env.ref('welly_base.group_pt')
        group_pt_manager = self.env.ref('welly_base.group_pt_manager')

        # Tìm tất cả người dùng thuộc nhóm quyền pt và pt_manager
        users = self.env['res.users'].search([
            '|',
            ('groups_id', 'in', group_welly_account.id),
            ('groups_id', 'in', group_pt_manager.id),
            ('company_id', '=', company_id)
        ])
        # Trả về domain với danh sách user_ids
        return [('id', 'in', users.ids)]


    presenter = fields.Char(string='Người giới thiệu')

    presenter_partner = fields.Many2one('res.partner', string='Người giới thiệu', tracking=True)
    @api.constrains('presenter_partner')
    def _validate_presenter_partner_change_role(self):
        for rec in self:
            if rec.approval_state != 'draft' and not self.env.user.has_group('welly_base.group_welly_account') and not self.env.user.has_group('welly_base.group_admin_club'):
                raise UserError('Bạn không có quyền thay đổi người giới thiệu khi biên lai không là Nháp!')

    # Thông tin nội bộ
    marketing_staff_id = fields.Many2many(
        'res.users',
        relation='welly_account_move_marketing_staff_rel',
        default = lambda self: self._set_default_marketing_staff_id()
    )
    def _set_default_marketing_staff_id(self):
        sale_oder_id = self._context.get("active_id") if self._context.get("active_model")=="sale.order" else False
        ids = self.env['sale.order'].browse(sale_oder_id).opportunity_id.user_id.ids
        data = [(6, 0, ids)] if ids else False
        return data
    
    pt_staff_id = fields.Many2many(
        'res.users',
        relation='welly_account_move_pt_staff_rel',
        default = lambda self: self._set_default_pt_staff_id()
    )
    def _set_default_pt_staff_id(self):
        sale_oder_id = self._context.get("active_id") if self._context.get("active_model")=="sale.order" else False
        ids = self.env['sale.order'].browse(sale_oder_id).opportunity_id.user_pt_id.ids
        data = [(6, 0, ids)] if ids else False
        return data
    
    #############################################################################
    
    # Thêm các trường mới CHỈ DÙNG ĐỂ HIỂN THỊ tổng giá gốc và tổng chiết khấu của hóa đơn
    # KHÔNG ĐƯỢC DÙNG CÁC TRƯỜNG NÀY ĐỂ TÍNH TOÁN
    # Giá gốc
    total_original_price = fields.Monetary(string="Tổng giá gốc", compute="_compute_total_price_discount", store=True)
    total_original_price_text = fields.Char(string="Giá gốc bằng chữ", compute="_compute_total_price_discount", store=True)
    # Chiết khấu
    total_discount = fields.Float(string="Giảm giá. %", compute="_compute_total_price_discount", store=True)
    total_discount_amount = fields.Monetary(string="Tổng giá được giảm", compute="_compute_total_price_discount", store=True)
    total_discount_amount_text = fields.Char(string="Giảm giá bằng chữ", compute="_compute_total_price_discount", store=True)
    
    @api.depends('sale_order_id', 'registration_form_id', 'invoice_line_ids', 'invoice_line_ids.price_unit', 'invoice_line_ids.discount', 'invoice_line_ids.quantity', 'invoice_line_ids.tax_ids')
    def _compute_total_price_discount(self):
        for record in self:
            total_original = 0.0
            total_discount_amount = 0.0
            # Duyệt qua mỗi dòng hóa đơn để tính tổng giá gốc và tổng chiết khấu
            for line in record.invoice_line_ids:
                if line.price_subtotal >= 0:
                    price_original = line.price_unit * line.quantity
                    # Tính giá trước chiết khấu cho mỗi dòng
                    # Kiểm tra không phải sản phẩm phí dịch vụ và sản phẩm khuyên mãi
                    rewards = self.env['loyalty.reward'].sudo().search(
                        ['|', ('reward_product_id', '=', line.product_id.id), ('discount_line_product_id', '=', line.product_id.id), ('active', '=', True)], limit=1
                    )
                    if line.product_id.default_code != SERVICE_FEE_CODE and not rewards:
                        total_original += price_original
                    # Tính số tiền được giảm giá dựa trên tỷ lệ phần trăm chiết khấu
                    total_discount_amount += price_original * line.discount / 100
                else:
                    # Với những line có price_subtotal < 0 thì line đó sẽ được tính là giảm giá
                    total_discount_amount -= line.price_subtotal
            # Cập nhật giá trị cho các trường tổng mới
            record.total_original_price = total_original
            record.total_discount_amount = total_discount_amount
            record.total_discount = (total_discount_amount / total_original) if total_original > 0 else 0
            # Cập nhật giá trị cho các trường tổng mới ở dạng chữ
            record.total_original_price_text = record._num2words(record.total_original_price, 'vi_VN') + ' ' + record.currency_id.name
            record.total_discount_amount_text = record._num2words(record.total_discount_amount, 'vi_VN') + ' ' + record.currency_id.name
            record.compute_pay_amount()
    
    ##############################################################################
    # Sản phẩm chính
    main_product_id = fields.Many2one('product.product', string='Sản phẩm chính', compute='_compute_main_product_id', store=True)
    @api.depends('invoice_line_ids')
    def _compute_main_product_id(self):
        for rec in self:
            rec.main_product_id = rec.invoice_line_ids[0].product_id if rec.invoice_line_ids else False

    # trường đánh dấu là biên lai được clone từ các chức năng nhân bản, tạo từ CNHĐ, Bảo Lưu HĐ
    is_clone = fields.Boolean("Biên lai Clone", default=False)
    
    # Nguồn
    source_id = fields.Many2one(tracking=True)
    @api.constrains('source_id')
    def _validate_source_id_change_role(self):
        for rec in self:
            if rec.state != 'draft' and not self.env.user.has_group('welly_base.group_welly_account') or rec.state == 'cancel':
                raise UserError('Bạn không có quyền thay đổi nguồn!')
    
    # Thông tin nhân viên kinh doanh và PT hỗ trợ mapping với crm.lead
    crm_user_id = fields.Many2one('res.users', string='Nhân viên kinh doanh trên ticket', related='opportunity_id.user_id', store=True)
    crm_pt_user_id = fields.Many2one('res.users', string='PT hỗ trợ trên ticket', related='opportunity_id.user_pt_id', store=True)
    # Hàm chuyển số thành chữ
    def _num2words(self, number, lang):
        if num2words is None:
            return ""
        try:
            return num2words(number, lang=lang).title()
        except NotImplementedError:
            return num2words(number, lang='en').title()

    # Số tiền đã thanh toán
    amount_paid = fields.Monetary(string='Số tiền đã trả', compute='_compute_amount_paid', store=True)

    @api.depends('account_payment_ids', 'account_payment_ids.amount', 'account_payment_ids.payment_state')
    def _compute_amount_paid(self):
        for account_move in self:
            # Lấy tổng số tiền của welly.payment.line có state = posted gán cho account_move.amount_paid
            account_move.amount_paid = sum(account_move.account_payment_ids.filtered(lambda x: x.payment_state == "posted").mapped('amount'))

    @api.depends('registration_form_id')
    def _compute_registration_form_info(self):
        for rec in self:
            rec.registration_form_name_print = rec.registration_form_id.name
            product_id = self.env['product.product'].search([('default_code', '=', SERVICE_FEE_CODE)])
            if rec.registration_form_id and rec.registration_form_id.id != self.env.ref('welly_base.welly_registration_form_new').id:
                # Kiểm tra xem có sản phẩm dịch vụ phí trong invoice_line_ids không, nếu không thì thêm vào
                if not rec.invoice_line_ids.filtered(lambda x: x.product_id.id == product_id.id):
                    invoice_line = self.env['account.move.line'].new({
                        'product_id': product_id.id,
                        'name': product_id.name,
                        'quantity': 1,
                        'price_unit': rec.service_fee,
                        'tax_ids': False,
                    })
                    rec.invoice_line_ids += invoice_line
            else:
                # Xoá sản phẩm dịch vụ phí trong invoice_line_ids nếu có khi chọn hình thức đăng ký là Mới
                rec.invoice_line_ids = [(2, line.id) for line in rec.invoice_line_ids if line.product_id.id == product_id.id]
        
    @api.depends('welly_gift_id')
    def _compute_gift_info(self):
        for rec in self:
            gifts = [gift.name for gift in rec.welly_gift_id]
            rec.welly_gift_name_print = ', '.join(gifts)

    @api.depends('sale_order_id')
    def _compute_sale_order_template_info(self):
        for rec in self:
            rec.sale_order_template_name_print = rec.sale_order_template_id.name

    @api.depends('utm_campaign_id')
    def _compute_campaign_info(self):
        for rec in self:
            rec.utm_campaign_name_print = rec.utm_campaign_id.name

    @api.depends('pay_amount')
    def _compute_text_amount(self):
        for rec in self:
            if rec.currency_id:
                rec.pay_amount_to_text = rec._num2words(rec.pay_amount, 'vi_VN') + ' ' + rec.currency_id.name
            else:
                rec.pay_amount_to_text = ''

    @api.depends('partner_id',
                    'partner_id.street',
                    'partner_id.street2',
                    'partner_id.city',
                    'partner_id.name',
                    'partner_id.partner_id_number',
                    'partner_id.gender',
                    'partner_id.birthdate',
                    'partner_id.nationality_id',
                    'partner_id.phone',
                    'partner_id.email',)
    def _compute_partner_info(self):
        for rec in self:
            if rec.state == 'draft':
                partner = rec.partner_id
                street = rec.partner_id.street
                street2 = rec.partner_id.street2
                city = rec.partner_id.city
                non_empty_values = [value for value in [
                    street, street2, city] if value]
                full_address = ', '.join(
                    non_empty_values) if non_empty_values else ''
                rec.address = full_address
                rec.partner_name_print = partner.name
                rec.partner_id_number = partner.partner_id_number
                rec.gender = partner.gender
                rec.birthdate = partner.birthdate
                rec.nationality_id = partner.nationality_id
                rec.phone = partner.phone
                rec.email = partner.email

    # tính tổng số tiền cần thanh toán, và giá dịch vụ khi báo giá có thuế
    @api.depends('amount_residual')
    def compute_pay_amount(self):
        for rec in self:
            if rec.tax_totals:
                rec.service_price = int(sum([x.price_total if x.product_id.default_code != SERVICE_FEE_CODE else 0 for x in rec.invoice_line_ids]))
                rec.pay_amount = int(rec.tax_totals['amount_total'])
            else:
                rec.pay_amount = 0

    # tính tổng số tiền cần thanh toán khi khai báo phí
    @api.onchange('service_fee')
    def _onchange_service_fee(self):
        for move in self:
            for line in move.invoice_line_ids:
                if line.product_id.default_code == SERVICE_FEE_CODE:
                    line.price_unit = move.service_fee
                    line.quantity = 1
    
    @api.onchange('invoice_line_ids')
    def _onchange_service_fee_by_invoice_line_ids(self):
        # Kiểm tra, update giá dịch vụ khi thay đổi invoice_line_ids
        for move in self:
            service_fee_line = move.invoice_line_ids.filtered(lambda x: x.product_id.default_code == SERVICE_FEE_CODE)
            if service_fee_line:
                move.service_fee = service_fee_line.price_unit
            elif move.registration_form_id and move.registration_form_id.id != self.env.ref('welly_base.welly_registration_form_new').id and not move.is_clone:
                raise UserError('Không thể xoá phí dịch vụ!')
            else:
                move.service_fee = 0
    
    # thay đổi số tiền cần thanh toán và giá dịch vụ khi nhập báo giá
    @api.onchange('sale_order_id', 'registration_form_id')
    def _onchange_sale_order_id(self):
        for move in self:
            sale_order = move.sale_order_id
            # update sale_order.order_line to invoice_line_ids by product_id key
            for line in sale_order.order_line:
                invoice_line = move.invoice_line_ids.filtered(
                    lambda x: x.product_id.id == line.product_id.id)
                if invoice_line:
                    invoice_line.quantity = line.product_uom_qty
                    invoice_line.price_unit = line.price_unit
                    invoice_line.discount = line.discount
                    invoice_line.tax_ids = [(6, 0, line.tax_id.ids)]
                    invoice_line.name = line.name
                else:
                    invoice_line = move._prepare_invoice_line_values_welly(line)
                    move.invoice_line_ids += self.env['account.move.line'].new(invoice_line)
            move.service_price = int(sum([x.price_total if x.product_id.default_code != SERVICE_FEE_CODE else 0 for x in move.invoice_line_ids]))
            
            # Reset lại phí dịch vụ và tính lại tổng số tiền cần thanh toán
            if move.registration_form_id:
                move.service_fee = 0
                move.pay_amount = move.service_price + move.service_fee

    
    # convert sale.order.line sang account.move.line data
    def _prepare_invoice_line_values_welly(self, line):
        self.ensure_one()
        return {
            'display_type': line.display_type,
            'name': line.name,
            'tax_ids': [(6, 0, line.tax_id.ids)],
            'discount': line.discount,
            'product_id': line.product_id.id,
            'quantity': line.product_uom_qty,
            'price_unit': line.price_unit,
        }

    # Action button "Ký"
    def button_sign(self):
        for rec in self:
            if rec.state == 'posted' and rec.approval_state in ('posted', 'request_cancel'):
                rec.approval_state = 'approved'
                # không gửi thông báo, tạo công việc khi đây là lệnh import
                if not self.env.context.get('is_import'):
                    rec.action_feedback()
                    sale_user_id = rec.opportunity_id.user_id
                    summary = _('Create new contract')
                    content = _('The receipt named: %s needs to create new contract!') % (
                        rec.name)
                    if sale_user_id:
                        rec.action_create_mail_activity(
                            sale_user_id, summary=summary, content=content)
                    return {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'type': 'success',
                            'message': _("Approved successfully."),
                            'next': {'type': 'ir.actions.act_window_close'},
                        }}

    # Action button "Xác Nhận"
    def button_confirm(self):
        for rec in self:
            # check required fields
            missing_fields = []
            if len(self.welly_location_many2_many) < 1:
                missing_fields.append('Địa điểm')
            if self.service_type == selection.ServiceType.MEMBER and not self.sale_order_template_id:
                missing_fields.append('Thẻ thành viên')
            if self.service_type == selection.ServiceType.PT and not self.sale_order_template_id:
                missing_fields.append('Gói dịch vụ')
            if not self.valid_time:
                missing_fields.append('Thời hạn')
            if not self.date_start:
                missing_fields.append('Ngày kích hoạt')
            # if not self.date_end:
            #     missing_fields.append('Ngày hết hạn')
            if not self.registration_form_id:
                missing_fields.append('Hình thức đăng kí')
            if not self.payment_type_welly:
                missing_fields.append('Loại thanh toán')
            if len(self.partner_account_move_ids) < 1:
                missing_fields.append('Danh sách Người sử dụng dịch vụ')
            if missing_fields:
                missing_fields_str = ", ".join(missing_fields)
                raise UserError(
                    _(f"Điền vào các trường sau trước khi xác nhận: {missing_fields_str}"))
            if rec.state == 'draft' and rec.approval_state == 'draft':
                rec.approval_state = 'posted'
                if rec.pay_amount == 0:
                    rec.action_post()

    # Action button "Hủy"
    def action_reject(self):
        for rec in self:
            if (rec.state == 'posted' and rec.approval_state == 'posted') or self.user_has_groups('welly_base.group_welly_account'):
                # chuyển sang nháp - đây là action gốc của odoo: thực hiện chuyển remove_move_reconcile các thanh toán
                rec.button_draft()
                # Cập nhật trạng thái hủy
                rec.state = 'cancel'
                # Gửi thông báo về việc huỷ biên lai cho người dùng liên quan
                rec.action_feedback()
                rec.message_post(
                    body=f"Biên lai bị hủy bởi lí do: {self.reject_reason}")
                
                if rec.opportunity_id.user_id:
                    summary = _('Receipt Have Been Rejected')
                    content = _('The receipt named: %s have been canceled with reason: %s.') % (
                        rec.name, rec.reject_reason)
                    rec.action_create_mail_activity(
                        rec.opportunity_id.user_id, summary=summary, content=content)

    # Action button tạo "Yêu cầu hủy"
    def action_request_cancel(self):
        for rec in self:
            rec.approval_state = 'request_cancel'
            rec.message_post(
                body=f"Tạo yêu cầu hủy với lý do: {self.reject_reason}")
            partners = self.env['res.users'].search(
                [('groups_id', 'in', [self.env.ref('welly_base.group_welly_account').id])]).mapped('partner_id').filtered(lambda p: p != self.env.user.partner_id)
            vals = {'partners': partners,
                    'title': 'Yêu cầu hủy biên lai'}
            rec.notify_user(vals)

    # Gửi thông báo cho người dùng
    def action_create_mail_activity(self, user_id, summary, content):
        self.ensure_one()
        activity_type = self.env['mail.activity.type'].search(
            [('name', 'ilike', 'email')])[0]
        self.env['mail.activity'].create({
            'activity_type_id': activity_type and activity_type.id,
            'summary': summary or activity_type.summary,
            'automated': True,
            'note': content or activity_type.default_note,
            'res_model_id': self.env.ref('welly_base.model_account_move').id,
            'res_id': self.id,
            'user_id': user_id.id,
        })

    # Tạo mới hợp đồng
    def create_new_contract(self):
        for rec in self:
            if self.env.user.has_group('welly_base.group_sales') and not self.env.user.has_group(
                    'welly_base.group_admin_sales'):
                if self.opportunity_id.user_id.id != self.env.user.id:
                    raise UserError(
                        _('User does not have permission to create this contract.'))
            exist_contract = self.env['welly.contract'].search(
                [('welly_invoice_id', '=', rec.id),
                 ('state', 'not in', ['reject', 'cancel'])
                 ])
            # rule: Với loại thanh toán trả góp chỉ được phép tạo duy nhất một hợp đồng với tất cả người sử dụng dịch vụ
            exclude_ids = []
            full_partner_account_move_ids = []
            if rec.payment_type_welly == 'installment':
                if exist_contract:
                    raise UserError(
                        _(f'Với loại thanh toán trả góp chỉ được phép tạo duy nhất một hợp đồng. Mã HĐ đã được tạo là: {exist_contract.name}'))
                else:
                    full_partner_account_move_ids = rec.partner_account_move_ids.ids
            else:
                exclude_ids = list(set([partner_id for contract in exist_contract for partner_id in
                                        contract.partner_account_ids.mapped('id')]))
            # rule: Trong 1 biên lai, 1 KH chỉ được phép tạo 1 Hợp Đồng mới
            filtered_ids = rec.partner_account_move_ids.filtered(
                lambda move: move.partner_id.id not in exclude_ids and move.account_move_id.id == rec.id).ids
            # rule: phải có ít nhất một người sử dụng dịch vụ cho một hđ mới
            if len(filtered_ids) < 1:
                raise UserError(_('Không tìm thấy dữ liệu Người sử dụng dịch vụ'))
            if rec.service_type in [selection.ServiceType.MEMBER, selection.ServiceType.TURN_CARD]:
                # hợp đồng member sẽ tạo hết người sử dụng dịch vụ
                if exist_contract:
                    raise UserError("Biên lai đã tạo hợp đồng!")
                else:
                    self.action_feedback()
                    view_id = self.env.ref(
                        'welly_base.view_welly_contract_form').id
                    return {
                        'type': 'ir.actions.act_window',
                        'name': 'Create New Contract',
                        'res_model': 'welly.contract',
                        'view_mode': 'form',
                        'view_id': view_id,
                        'target': 'current',
                        'context': {
                            'default_welly_invoice_id': self.id,
                            'default_service_type': self.service_type,
                            'default_welly_location_many2_many': self.welly_location_many2_many.ids,
                            'default_sale_order_template_id': self.sale_order_template_id.id,
                            'default_other_contact_id': self.other_contact_id.id,
                            'default_guardian_id': self.guardian_id.id,
                            'default_guardian_relationship': self.guardian_relationship,
                            'default_date_start': self.date_start,
                            'default_date_end': self.date_end,
                            'default_exercise_form_id': self.exercise_form_id.id,
                            'default_welly_gift_id': self.welly_gift_id.ids,
                            'default_create_user_id': self.env.user.id,
                            'default_partner_account_move_ids': [(6, 0, filtered_ids)],
                            'form_view_initial_mode': 'edit'}}
            else:
                self.action_feedback()
                if len(filtered_ids) == 1:
                    total_session_number = self.session_number
                    available_session_number = total_session_number
                    date_start = self.date_start
                    date_end = self.date_end
                    is_installment_activated = False
                    partner_account_move = self.env['welly.partner.account.move'].browse(filtered_ids[0])
                    default_partner_id = self.partner_id.id
                    welly_partner_account_move_ids = [partner_account_move.id]

                    # Kiểm tra đã có hợp đồng PT nào chưa cho biên lai này
                    # * Có rồi:
                    # - Số buổi còn lại = 0,
                    # * Chưa:
                    # - Số buổi còn lại = số buổi trên biên lai với loại không phải trả góp
                    # - Số buổi còn lại = tổng số buổi trên payment_line với loại trả góp

                    if self.payment_type_welly == 'installment':
                        is_installment_activated = True
                        welly_payment_lines = self.env['welly.payment.line'].search([
                            ('welly_account_move_id', '=', self.id),
                            ('payment_state', '=', 'posted')
                        ])
                        welly_payment_lines = sorted(welly_payment_lines, key=lambda x: (x.date, x.id))
                        available_session_number = sum(x.allow_session_number for x in welly_payment_lines)
                        if self.payment_state == 'partial' and welly_payment_lines:
                            date_start = welly_payment_lines[0].effective_date
                            date_end = welly_payment_lines[-1].expired_date

                    if exist_contract:
                        available_session_number = 0

                    view_id = self.env.ref('welly_base.view_welly_contract_form').id
                    return {
                        'type': 'ir.actions.act_window',
                        'name': 'Create New Contract',
                        'res_model': 'welly.contract',
                        'view_mode': 'form',
                        'view_id': view_id,
                        'target': 'current',
                        'context': {
                            'default_welly_invoice_id': self.id,
                            'default_partner_id': default_partner_id,
                            'default_partner_account_move_ids': [(6, 0, welly_partner_account_move_ids)],
                            'default_service_type': self.service_type,
                            'default_welly_location_many2_many': self.welly_location_many2_many.ids,
                            'default_sale_order_template_id': self.sale_order_template_id.id,
                            'default_other_contact_id': self.other_contact_id.id,
                            'default_guardian_id': self.guardian_id.id,
                            'default_guardian_relationship': self.guardian_relationship,
                            'default_date_start': date_start,
                            'default_date_end': date_end,
                            'default_session_number': total_session_number,
                            'default_available_session_number': available_session_number if available_session_number else None,
                            'default_is_installment_activated': is_installment_activated,
                            'default_create_user_id': self.env.user.id,
                            'default_exercise_form_id': self.exercise_form_id.id,
                            'default_welly_gift_id': [(6, 0, self.welly_gift_id.ids)],
                            'form_view_initial_mode': 'edit'
                        }
                    }
                else:
                    # Nếu còn nhiều hơn 1 người chưa tạo hợp đồng, mở wizard để chọn người tạo hợp đồng PT
                    return {
                        'name': 'Create PT Contract',
                        'view_mode': 'form',
                        'res_model': 'create.pt.contract.wizard',
                        'type': 'ir.actions.act_window',
                        'target': 'new',
                        'context': {
                            'default_partner_account_move_ids_domain': [(6, 0, filtered_ids)],
                            'default_partner_account_move_ids': [(6, 0, [])],
                            'has_exist_contract': bool(exist_contract)
                        }
                    }

    # Validate hạn biên lai
    @api.constrains('date_start', 'date_end')
    def _check_dates(self):
        for record in self:
            if record.date_start and record.date_end and record.date_start > record.date_end:
                raise UserError(
                    "Ngày kết thúc phải lớn hơn hoặc bằng ngày bắt đầu!")


    # Overriding lại hàm create để tạo giá trị mặc định cho partner_account_move_ids
    @api.model_create_multi
    def create(self, vals_list):
        account_move_id = None
        for vals in vals_list:
            # Kiểm tra có phải hoá đơn bán hàng không. Nếu không thì sẽ return giá trị mặc định
            if vals.get('move_type') == "out_invoice":
                if vals.get('name', _("/")) == _("/"):
                    seq_date = fields.Datetime.context_timestamp(
                        self, fields.Datetime.to_datetime(vals['date_created'])
                    ) if 'date_order' in vals else None
                    vals['name'] = self.env['ir.sequence'].next_by_code(
                        'account.move', sequence_date=seq_date) or _("None Factor")

                # tạo giá trị mặc định partner cho partner_account_move_ids
                if vals.get('partner_account_move_ids'):
                    for cmd in vals.get('partner_account_move_ids'):
                        if cmd[0] == 4:
                            partner_id = self.env['welly.partner.account.move'].browse(cmd[1]).partner_id.id
                            if partner_id == vals['partner_id']:
                                account_move_id = partner_id
        
        # Call super
        rs = super().create(vals_list)
        # Kiểm tra hoá đơn được tạo là hoá đơn bán hàng
        for record in rs:
            if record.move_type == "out_invoice" and not account_move_id and record.partner_id:
                self.env['welly.partner.account.move'].create({
                    'account_move_id': record.id,
                    'partner_id': record.partner_id.id
                })
            # sửa session_number = 0 đối với service_type là member hoặc khác
            if record.service_type in ['member', 'other']:
                record.session_number = 0
        return rs

    @api.depends('other_contact_id')
    def _compute_other_contact_info(self):
        """ compute the new values when partner_id has changed """
        for receipt in self:
            partner = receipt.other_contact_id
            street = receipt.other_contact_id.street
            street2 = receipt.other_contact_id.street2
            city = receipt.other_contact_id.city
            non_empty_values = [value for value in [
                street, street2, city] if value]
            full_address = ', '.join(
                non_empty_values) if non_empty_values else ''
            receipt.other_contact_address = full_address
            receipt.other_contact_name_print = partner.name
            receipt.other_contact_id_number = partner.partner_id_number
            receipt.other_contact_gender = partner.gender
            receipt.other_contact_nationality_id = partner.nationality_id
            receipt.other_contact_phone = partner.phone

    @api.depends('guardian_id')
    def _compute_guardian_contact_info(self):
        """ compute the new values when partner_id has changed """
        for receipt in self:
            if receipt.state == 'draft':
                partner = receipt.guardian_id
                street = receipt.guardian_id.street
                street2 = receipt.guardian_id.street2
                city = receipt.guardian_id.city
                non_empty_values = [value for value in [
                    street, street2, city] if value]
                full_address = ', '.join(
                    non_empty_values) if non_empty_values else ''
                receipt.guardian_address = full_address
                receipt.guardian_name_print = partner.name
                receipt.guardian_id_number = partner.partner_id_number
                receipt.guardian_gender = partner.gender
                receipt.guardian_nationality_id = partner.nationality_id
                receipt.guardian_phone = partner.phone

    # Action gửi feedback
    def action_feedback(self):
        activity_type = self.env['mail.activity.type'].search(
            [('name', 'ilike', 'email')])[0]
        activity = self.env['mail.activity'].search([
            ('activity_type_id', '=', activity_type.id),
            ('res_id', '=', self.id),
            ('res_model_id', '=', self.env['ir.model']._get_id('account.move'))
        ])
        for act in activity:
            if act.user_id == self.env.user and self.state != 'cancel' and self.approval_state != 'cancel':
                act.action_feedback(feedback=_('Hoàn thành!!!'))
            else:
                act.unlink()

    @api.depends('amount_residual', 'move_type', 'state', 'company_id')
    def _compute_payment_state(self):
        stored_ids = tuple(self.ids)
        if stored_ids:
            self.env['account.partial.reconcile'].flush_model()
            self.env['account.payment'].flush_model(['is_matched'])

            queries = []
            for source_field, counterpart_field in (('debit', 'credit'), ('credit', 'debit')):
                queries.append(f'''
                        SELECT
                            source_line.id AS source_line_id,
                            source_line.move_id AS source_move_id,
                            account.account_type AS source_line_account_type,
                            ARRAY_AGG(counterpart_move.move_type) AS counterpart_move_types,
                            COALESCE(BOOL_AND(COALESCE(pay.is_matched, FALSE))
                                FILTER (WHERE counterpart_move.payment_id IS NOT NULL), TRUE) AS all_payments_matched,
                            BOOL_OR(COALESCE(BOOL(pay.id), FALSE)) as has_payment,
                            BOOL_OR(COALESCE(BOOL(counterpart_move.statement_line_id), FALSE)) as has_st_line
                        FROM account_partial_reconcile part
                        JOIN account_move_line source_line ON source_line.id = part.{source_field}_move_id
                        JOIN account_account account ON account.id = source_line.account_id
                        JOIN account_move_line counterpart_line ON counterpart_line.id = part.{counterpart_field}_move_id
                        JOIN account_move counterpart_move ON counterpart_move.id = counterpart_line.move_id
                        LEFT JOIN account_payment pay ON pay.id = counterpart_move.payment_id
                        WHERE source_line.move_id IN %s AND counterpart_line.move_id != source_line.move_id
                        GROUP BY source_line_id, source_move_id, source_line_account_type
                    ''')

            self._cr.execute(' UNION ALL '.join(
                queries), [stored_ids, stored_ids])

            payment_data = defaultdict(lambda: [])
            for row in self._cr.dictfetchall():
                payment_data[row['source_move_id']].append(row)
        else:
            payment_data = {}

        for invoice in self:
            if invoice.payment_state == 'invoicing_legacy':
                # invoicing_legacy state is set via SQL when setting setting field
                # invoicing_switch_threshold (defined in account_accountant).
                # The only way of going out of this state is through this setting,
                # so we don't recompute it here.
                continue

            currencies = invoice._get_lines_onchange_currency().currency_id
            currency = currencies if len(
                currencies) == 1 else invoice.company_id.currency_id
            reconciliation_vals = payment_data.get(invoice.id, [])
            payment_state_matters = invoice.is_invoice(True)

            # Restrict on 'receivable'/'payable' lines for invoices/expense entries.
            if payment_state_matters:
                reconciliation_vals = [x for x in reconciliation_vals if
                                       x['source_line_account_type'] in ('asset_receivable', 'liability_payable')]

            new_pmt_state = 'not_paid'
            if invoice.state in ('posted'):

                # Posted invoice/expense entry.
                if payment_state_matters:

                    if currency.is_zero(invoice.amount_residual):
                        if any(x['has_payment'] or x['has_st_line'] for x in reconciliation_vals):

                            # Check if the invoice/expense entry is fully paid or 'in_payment'.
                            if all(x['all_payments_matched'] for x in reconciliation_vals):
                                new_pmt_state = 'paid'
                            else:
                                new_pmt_state = invoice._get_invoice_in_payment_state()

                        else:
                            new_pmt_state = 'paid'
                            reverse_move_types = set()
                            for x in reconciliation_vals:
                                for move_type in x['counterpart_move_types']:
                                    reverse_move_types.add(move_type)

                            in_reverse = (invoice.move_type in ('in_invoice', 'in_receipt')
                                          and (reverse_move_types == {'in_refund'} or reverse_move_types == {
                                        'in_refund', 'entry'}))
                            out_reverse = (invoice.move_type in ('out_invoice', 'out_receipt')
                                           and (reverse_move_types == {'out_refund'} or reverse_move_types == {
                                        'out_refund', 'entry'}))
                            misc_reverse = (invoice.move_type in ('entry', 'out_refund', 'in_refund')
                                            and reverse_move_types == {'entry'})
                            if in_reverse or out_reverse or misc_reverse:
                                new_pmt_state = 'reversed'

                    elif reconciliation_vals:
                        new_pmt_state = 'partial'
            # Gửi thông báo cho người dùng nếu biên lai được thanh toán và là hoá đơn bán hàng
            if new_pmt_state == 'paid' and invoice.move_type == 'out_invoice':
                partners = self.env['res.users'].search(
                    [('groups_id', 'in', [self.env.ref('welly_base.group_welly_account').id])]).mapped('partner_id')
                vals = {'partners': partners,
                        'title': 'Biên lai cần phê duyệt'}
                invoice.notify_user(vals)
            invoice.payment_state = new_pmt_state

    # Hiển thị bảng in cho biên nhận
    def open_welly_recept_total_report(self):
        report = self.env.ref('welly_base.welly_recept_total_report')
        return {
            'type': 'ir.actions.report',
            'report_name': report.report_name,
            'report_type': report.report_type,
            'py3o_filetype': report.py3o_filetype,
        }

    # Hiển thị thông báo cho người dùng
    def notify_user(self, vals):
        notifications = []
        if 'partners' in vals:
            for p in vals['partners']:
                action = self.env.ref('welly_base.action_move_out_invoice_type_welly')
                bus_message = {
                    "company_id": self.env.company.id,
                    
                    "type": vals['type'] if 'type' in vals else 'default',
                    'message': '%s',
                    'links': [{
                        'label': f'Biên lai {self.name}',
                        'url': f'#action={action.id}&id={self.id}&view_type=form&model=account.move'
                    }],
                    "title": vals['title'],
                    "sticky": True,
                }
                notifications.append((
                    p, 'web.notify', [bus_message]
                ))
        self.env['bus.bus']._sendmany(notifications)

    # Compute text for service fee
    def _compute_service_fee_to_text(self):
        for rec in self:
            if rec.currency_id:
                rec.service_fee_to_text = rec._num2words(
                    rec.service_fee, 'vi_VN') + ' ' + rec.currency_id.name
            else:
                rec.service_fee_to_text = ''
    
    # Override để huỷ reconcile khi huỷ biên lai
    def js_remove_outstanding_partial(self, partial_id):
        self.ensure_one()
        partial = self.env['account.partial.reconcile'].browse(partial_id)
        partial.debit_move_id.payment_id.state = 'cancel'
        partial.credit_move_id.payment_id.state = 'cancel'
        return partial.unlink()

    @api.model
    def _get_last_month_domain(self):
        # Xác định ngày đầu tiên và ngày cuối cùng của tháng trước
        today = datetime.today()
        first_day_of_last_month = (today.replace(day=1) - relativedelta(months=1)).replace(day=1)
        last_day_of_last_month = today.replace(day=1) - timedelta(days=1)

        # Trả về domain để lọc các bản ghi trong khoảng thời gian này
        return [('&'),
                ('invoice_date', '>=', first_day_of_last_month.strftime('%Y-%m-%d')),
                ('invoice_date', '<=', last_day_of_last_month.strftime('%Y-%m-%d'))]


    # override hàm search để thêm bộ lọc tùy chỉnh
    @api.model
    def search(self, domain, offset=0, limit=None, order=None, count=False):
        # Kiểm tra nếu domain chứa 'last_month'
        for index, arg in enumerate(domain):
            if isinstance(arg, (list, tuple)) and len(arg) == 3 and arg[0] == 'invoice_date' and arg[2] == 'last_month':
                # Thay thế với domain tháng trước
                domain[index:index + 1] = self._get_last_month_domain()
                break
        return super(AccountMove, self).search(domain, offset, limit, order, count)


    @api.model
    def read_group(self, domain, fields, groupby, offset=0, limit=None, orderby=False, lazy=True):
        # Kiểm tra nếu domain chứa 'last_month'
        for index, arg in enumerate(domain):
            if isinstance(arg, (list, tuple)) and len(arg) == 3 and arg[0] == 'invoice_date' and arg[2] == 'last_month':
                # Thay thế với domain tháng trước
                domain[index:index + 1] = self._get_last_month_domain()
                break

        return super(AccountMove, self).read_group(domain, fields, groupby, offset, limit, orderby, lazy)

    def action_view_partner(self):
        """Mở form xem thông tin chi tiết của khách hàng"""
        self.ensure_one()
        return {
            'name': 'Thông tin khách hàng',
            'type': 'ir.actions.act_window',
            'res_model': 'res.partner',
            'view_mode': 'form',
            'res_id': self.partner_id.id,
            'target': 'current',
            'flags': {'mode': 'readonly'},
            'context': {'create': False, 'edit': False}
        }

    active = fields.Boolean(default=True)

    def action_archive(self):
        user = self.env.user
        # check phân quyền được sử dụng
        if not user.has_group('base.group_system'):
            raise UserError(_("Bạn không có quyền thao tác."))
        for rec in self:
            if rec.active:
                # tìm thông tin hợp đồng của biên lai.
                # chỉ được quyền lưu trữ "biên lai" khi hợp đồng sinh từ biên lại có trạng thái = Hủy/ Đóng/Hoàn thành (Hoặc không có hợp đồng)
                exist_contract = self.env['welly.contract'].search(
                    [('welly_invoice_id', '=', rec.id),
                     ('state', 'not in', ['closed', 'done', 'cancel', 'reject'])
                     ])
                if exist_contract:
                    raise UserError(_(f'Chỉ được Lưu trữ biên lai có Hợp đồng trạng thái (Hủy/Đóng/ Hoàn thành) hoặc Chưa tạo hợp đồng. '
                                      f'Biên lai: {rec.name} đã tồn tại Hợp đồng không đủ điều kiện để lưu trữ.'))
                rec.action_reject()
                # cập nhật log activities
                self.env['mail.message'].create({
                    'model': 'account.move',
                    'res_id': rec.id,
                    'message_type': 'comment',
                    'body': 'Hóa đơn đã được Lưu trữ',
                })
        res = super(AccountMove, self).action_archive()
        return res

    def action_unarchive(self):
        user = self.env.user
        # check phân quyền được sử dụng
        if not user.has_group('base.group_system'):
            raise UserError(_("Bạn không có quyền thao tác."))
        for rec in self:
            if not rec.active:
                rec.approval_state = 'draft'
                rec.state = 'draft'
                # cập nhật log activities
                self.env['mail.message'].create({
                    'model': 'account.move',
                    'res_id': rec.id,
                    'message_type': 'comment',
                    'body': 'Hóa đơn đã được Bỏ lưu trữ',
                })
        res = super(AccountMove, self).action_unarchive()
        return res

    def action_account_move_reject(self):
        """Mở form xem hủy biên lai"""
        self.ensure_one()
        exist_contract = self.env['welly.contract'].search(
            [('welly_invoice_id', '=', self.id),
             ('state', 'not in', ['reject', 'cancel'])
             ])
        if exist_contract:
            contract_names = ', '.join(exist_contract.mapped('name'))
            raise UserError(
                f"Biên lai đã tồn tại hợp đồng: {contract_names}. Vui lòng hủy hợp đồng trước khi hủy biên lai!")
        return {
            'name': 'Nhập lý do hủy biên lai',
            'type': 'ir.actions.act_window',
            'res_model': 'account.move.reject',
            'view_mode': 'form',
            'target': 'new',  # Mở popup
            'context': {
                'default_move_id': self.id,
                'active_id': self.id,
                'active_ids': self.id
            }
        }

    invoice_date_due = fields.Date(
        string='Ngày phải trả',
        compute='_compute_invoice_date_due'
    )
    # #4002 override hàm tính ngày phải trả
    @api.depends('needed_terms')
    def _compute_invoice_date_due(self):
        today = fields.Date.context_today(self)
        for move in self:
            move.invoice_date_due = move.needed_terms and max(
                (k['date_maturity'] for k in move.needed_terms.keys() if k),
                default=False,
            ) or move.invoice_date_due or today
            if move.state == 'cancel' or move.approval_state == 'cancel':
                move.invoice_date_due = None
            if move.payment_state == 'partial':
                # Lấy ngày hết hạn của lần thanh toán có trường date lớn nhất
                welly_payment_lines = self.env['welly.payment.line'].search([
                    ('welly_account_move_id', '=', move.id),
                    ('payment_state', '=', 'posted'),
                    ('next_payment_date', '!=', False)
                ])
                if len(welly_payment_lines) > 0:
                    # Lấy ghi nhận có ngày thanh toán tiếp theo lớn nhất trong welly_payment_lines
                    welly_payment_line_last = max(welly_payment_lines, key=lambda x: (x.next_payment_date, x.id))
                    move.invoice_date_due = welly_payment_line_last.next_payment_date
