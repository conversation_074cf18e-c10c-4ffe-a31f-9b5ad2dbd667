import pytz

from odoo import api, fields, models, _
from datetime import timedelta, datetime, date
from odoo.exceptions import UserError, ValidationError
from odoo.addons.sale.models.sale_order import READONLY_FIELD_STATES
from odoo.addons.welly_base.fields import selection
from dateutil.relativedelta import relativedelta

try:
    from num2words import num2words
except ImportError:
    num2words = None

PAYMENT_STATE_SELECTION = [
    ('not_paid', 'Not Paid'),
    ('in_payment', 'In Payment'),
    ('paid', 'Paid'),
    ('partial', 'Partially Paid'),
    ('reversed', 'Reversed'),
    ('invoicing_legacy', 'Invoicing App Legacy'),
]


class WellyContract(models.Model):
    _name = 'welly.contract'
    _description = 'Welly Contract'
    _inherit = ['portal.mixin', 'mail.thread', 'mail.activity.mixin']
    _order = 'date_start desc, id desc'

    company_id = fields.Many2one(
        comodel_name='res.company',
        required=True, index=True,
        default=lambda self: self.env.company)
    name = fields.Char(string='Mã hợp đồng', default='New', readonly=True, required=True)
    create_user_id = fields.Many2one('res.users', string='Người tạo')
    welly_invoice_id = fields.Many2one(string='Hóa đơn',
                                       comodel_name='account.move')

    # Thông tin phòng tập
    # trường many2one này vẫn cần giữ vì còn dữ liệu cũ cần merge sang
    welly_location = fields.Many2one(string='Location',
                                     comodel_name='welly.location', )
    welly_location_many2_many = fields.Many2many(string='Địa điểm phòng tập',
                                     comodel_name='welly.location', relation='welly_contract_location_rel',
                                     column1='welly_contract_id', column2='welly_location_id')

    reject_reason = fields.Text(string='Reason')
    service_type = fields.Selection(
        string=selection.ServiceType._string,
        selection=selection.ServiceType._selection, related='welly_invoice_id.service_type',
        store=True
    )
    state = fields.Selection(
        selection=[
            ('draft', 'Dự thảo'),
            ('confirm_admin', 'Admin xác nhận'),
            ('sign_coo', 'Sign COO'),
            ('sign_customer', 'Khách hàng ký'),
            ('confirm_recep', 'Lễ tân xác nhận'),
            ('waiting_active', 'Chờ kích hoạt'),
            ('activated', 'Kích hoạt'),
            ('done', 'Hoàn thành'),
            ('reject', 'Từ chối'),
            ('cancel', 'Hủy'),
            ('closed', 'Đóng'),
        ],
        string='Trạng thái hợp đồng',
        required=True,
        readonly=True,
        copy=False,
        tracking=True,
        default='draft',
    )
    is_installment_activated = fields.Boolean(string="Hợp đồng thanh toán trả góp", store=True, default=False)

    def _compute_is_installment_activated(self):
        for record in self:
            if record.state not in ['confirm_admin', 'sign_coo', 'sign_customer',
                                    'confirm_recep', 'waiting_active', 'activated']:
                record.is_installment_activated = False

    # người đứng tên hợp đồng
    partner_id = fields.Many2one(
        comodel_name='res.partner',
        string="Khách hàng",
        related='welly_invoice_id.partner_id',
        store = True
    )
    partner_name_print = fields.Char(
        string="Khách hàng đứng tên", compute='_compute_partner_info', store=True)
    partner_code = fields.Char(string='Code', compute='_compute_partner_info', store=True)
    # thông tin cccd của khách hàng
    partner_id_number = fields.Char(string="Partner ID", compute='_compute_partner_info', store=True)
    identification_card_front = fields.Binary(related='partner_id.identification_card_front')
    identification_card_back = fields.Binary(related='partner_id.identification_card_back')
    gender = fields.Selection(
        string=selection.Gender._string,
        selection=selection.Gender._selection, compute='_compute_partner_info',
        store=True
    )
    address = fields.Char(string="Địa chỉ khách hàng", compute='_compute_partner_info', store=True)

    birthdate = fields.Date(
        string='Birthdate', compute='_compute_partner_info', store=True
    )
    nationality_id = fields.Many2one('res.country', string="Nationality", compute='_compute_partner_info', store=True)
    phone = fields.Char(string="Số điện thoại", compute='_compute_partner_info', store=True)
    email = fields.Char(string="Email", compute='_compute_partner_info', store=True)
    # người sử dụng dịch vụ
    partner_account_move_ids = fields.One2many(
        'welly.partner.account.move',
        'welly_contract_id',
        string='Người đi kèm',
        required=True)
    # Validate số lượng thành viên không vượt quá số lượng cho phép đối với gói gia đình
    @api.constrains('partner_account_move_ids')
    def _valid_can_add_partner_account_move(self):
        # Kiểm tra role của user có phải là COO, Admin Club, Admin hệ thống không
        user = self.env.user
        if not user.has_group('welly_base.group_coo') and not user.has_group('welly_base.group_admin_club') and not user.has_group('base.group_system'):
            raise UserError(_("Bạn không có quyền chỉnh sửa thông tin Người sử dụng dịch vụ."))
        
        b = len(self.partner_account_move_ids.partner_id) <= self.family_member_qty and self.is_family_service or not self.is_family_service
        if not b:
            raise UserError('Số lượng thành viên vượt quá số lượng cho phép!')
    ########################################
    
    # Trường này dùng để lưu thông tin mapping khách hàng với hợp đồng có thể lấy thông tin hợp đồng của khách hàng
    partner_account_ids = fields.Many2many(
        'res.partner', 'welly_contract_res_partner_rel',
        compute='_compute_partner_account_ids',
        string='Người sử dụng dịch vụ', store=True)
    @api.depends('partner_account_move_ids')
    def _compute_partner_account_ids(self):
        for record in self:
            record.partner_account_ids = record.partner_account_move_ids.mapped('partner_id')
    
    #########################################
    date_start = fields.Date(string='Date Start', required=True, copy=False)
    date_end = fields.Date(string='Date End', copy=False)

    # tính ngày hết hạn = ngày kích hoạt + thời hạn + quà tặng
    @api.onchange('date_start')
    def _onchange_date_start(self):
        # Nếu là HĐ trả góp
        if self.welly_invoice_id.payment_type_welly == 'installment':
            # Chưa thanh toán đủ
            if self.welly_invoice_id.payment_state == 'partial':
                # Lấy ngày hết hạn của lần thanh toán có trường date lớn nhất
                welly_payment_lines = self.env['welly.payment.line'].search([
                    ('welly_account_move_id', '=', self.welly_invoice_id.id),
                    ('payment_state', '=', 'posted')
                ])
                if len(welly_payment_lines) > 0:
                    # Lấy ghi nhận có ngày thanh toán lớn nhất trong welly_payment_lines
                    welly_payment_line_last = max(welly_payment_lines, key=lambda x: (x.date, x.id))
                    self.date_end = welly_payment_line_last.expired_date
            # Đã thanh toán đủ
            if self.welly_invoice_id.payment_state == 'posted':
                # Lấy ngày hết hạn và ngày kích hoạt theo account.move
                self.date_end = self.welly_invoice_id.date_end
        # Không phải hợp đồng trả góp
        else:
            if self.date_start:
                date_end = self.convert_valid_time_type_to_day(self.date_start, self.valid_time, self.valid_time_type)
                if self.welly_gift_id:
                    for gift in self.welly_gift_id:
                        date_end = self.convert_valid_time_type_to_day(date_end, gift.valid_time, gift.valid_time_type)
                self.date_end = date_end

    @api.onchange('date_end')
    def _onchange_date_end(self):
        if not self.date_start:
            raise UserError(_("Bạn cần phải nhập ngày bắt đầu trước."))

    def convert_valid_time_type_to_day(self, date_start, value, unit):
        # Convert string to date object
        date_start_obj = fields.Date.from_string(date_start)

        if unit == 'day':
            # Add days to date_start
            date_end = date_start_obj + relativedelta(days=value)
        elif unit == 'month':
            # Add months to date_start
            date_end = date_start_obj + relativedelta(months=value)
        elif unit == 'year':
            # Add years to date_start
            date_end = date_start_obj + relativedelta(years=value)
        else:
            # Return the start date if no valid unit is found
            date_end = date_start_obj

        # Return the computed date as a string in the format YYYY-MM-DD
        return fields.Date.to_string(date_end)

    valid_time = fields.Integer(string='Valid Time', related='welly_invoice_id.valid_time')
    valid_time_type = fields.Selection(
        selection=selection.DurationType._selection,
        related='welly_invoice_id.valid_time_type',
    )

    # Hình thức đăng kí
    registration_form_id = fields.Many2one('welly.registration.form', string='Registration Form',
                                           related='welly_invoice_id.registration_form_id')
    registration_form_name_print = fields.Char(
        string="Tên hình thức đăng kí", compute='_compute_registration_form_info', store=True)
    # mã hđ mới/cũ
    new_contract_id = fields.Many2one(comodel_name='welly.contract', string='New Contract',
                                      related='welly_invoice_id.new_contract_id', store=False)
    old_contract_id = fields.Many2one(comodel_name='welly.contract', string='Old Contract',
                                      related='welly_invoice_id.old_contract_id', store=False)
    service_price = fields.Monetary(string='Giá dịch vụ', related='welly_invoice_id.service_price')
    service_fee = fields.Monetary(string='Phí', related='welly_invoice_id.service_fee')
    service_fee_to_text = fields.Char(
        string="Service Fee in Text", related='welly_invoice_id.service_fee_to_text', store=False)
    service_fee_currency_id = fields.Many2one(
        'res.currency', string='Service Fee Currency', related='welly_invoice_id.service_fee_currency_id')
    # tổng tiền
    pay_amount = fields.Monetary(
        string='Tổng tiền bằng số', related='welly_invoice_id.pay_amount', store=True
    )
    currency_id = fields.Many2one(
        'res.currency', string='Currency',
        related='company_id.currency_id', readonly=True)
    pay_amount_to_text = fields.Char(string="Tổng tiền bằng chữ", compute="_compute_pay_amount_to_text", store=True)
    payment_method = fields.Selection(
        string='Payment Method',
        selection=[('cash', 'Cash'), ('transfer', 'Transfer'), ('debit_credit_card', 'Debit/Credit Card')],
        related='welly_invoice_id.payment_method'
    )
    payment_type_welly = fields.Selection(
        string='Payment Type',
        selection=[('deposit', 'Deposit'), ('installment', 'Installment')],
        related='welly_invoice_id.payment_type_welly'
    )
    sale_order_template_id = fields.Many2one(
        comodel_name='sale.order.template',
        store=True, readonly=True,
        states=READONLY_FIELD_STATES
    )
    sale_order_template_name_print = fields.Char(
        string="Tên gói dịch vụ", compute='_compute_sale_order_template_info', store=True)
    other_contact_id = fields.Many2one('res.partner', string="Contact")
    other_contact_name_print = fields.Char(
        string="Liên hệ", compute='_compute_other_contact', store=True)
    other_contact_id_number = fields.Char(string="Contact ID", compute='_compute_other_contact', store=True)

    other_contact_gender = fields.Selection(
        string=selection.Gender._string,
        default=selection.Gender.MALE,
        selection=selection.Gender._selection, compute='_compute_other_contact', store=True
    )
    other_contact_address = fields.Char(string="Address", compute='_compute_other_contact', store=True)
    other_contact_nationality_id = fields.Many2one('res.country', string="Nationality",
                                                   compute='_compute_other_contact', store=True)
    other_contact_phone = fields.Char(string="SĐT người liên hệ", compute='_compute_other_contact', store=True)
    guardian_id = fields.Many2one('res.partner', string="Contact")
    guardian_name_print = fields.Char(
        string="Liên hệ", compute='_compute_guardian_info', store=True)
    guardian_id_number = fields.Char(string="Contact ID", compute='_compute_guardian_info', store=True)
    guardian_relationship = fields.Char(string="Relationship")
    guardian_gender = fields.Selection(
        string=selection.Gender._string,
        selection=selection.Gender._selection, default=selection.Gender.MALE, compute='_compute_guardian_info', store=True
    )
    guardian_address = fields.Char(string="Address", compute='_compute_guardian_info', store=True)
    guardian_nationality_id = fields.Many2one('res.country', string="Nationality",
                                              compute='_compute_guardian_info', store=True)
    guardian_phone = fields.Char(string="SĐT giám hộ", compute='_compute_guardian_info', store=True)
    welly_gift_id = fields.Many2many(string='Danh sách quà tặng', comodel_name='welly.gift', relation='welly_contract_welly_gift_rel',
                                     column1='welly_contract_id', column2='welly_gift_id')
    welly_gift_name_print = fields.Char(string='Quà tặng', compute='_compute_gift_info', store=True)
    payment_state = fields.Selection(
        string='Payment State',
        selection=PAYMENT_STATE_SELECTION, related='welly_invoice_id.payment_state'
    )

    opportunity_id = fields.Many2one(
        string='Opportunity',
        comodel_name='crm.lead', related='welly_invoice_id.opportunity_id'
    )
    coo_signature = fields.Binary(string="Coo Electronic Signature")
    cus_signature = fields.Binary(string="Customer Electronic Signature")
    coach_id = fields.Many2one('res.users', string='Huấn luyện viên', related='welly_invoice_id.coach_id', store=True,
                               readonly=False)
    coach_name_print = fields.Char(string='Tên huấn luyện viên',
                                   compute='_compute_coach_info', store=True)
    class_id = fields.Many2one('welly.class', string='Class')
    exercise_form_id = fields.Many2one('welly.exercise.form', string='Exercise Form')
    exercise_form_name_print = fields.Char(
        string="Tên hình thức tập luyện", compute='_compute_exercise_form_info', store=True)
    session_number = fields.Integer('Number of Session', related='welly_invoice_id.session_number')
    # pdf_file = fields.Binary(string='Pdf File', attachment=True)
    campaign_id = fields.Many2one('utm.campaign', 'Campaign', index=True, ondelete='set null',
                                  related='welly_invoice_id.utm_campaign_id')
    campaign_name_print = fields.Char(
        string="Tên chiến dịch", compute='_compute_campaign_info', store=True)
    coo_name = fields.Char(string='COO Name')
    cus_name = fields.Char(string='Chữ ký khách hàng')
    # số buổi tập theo hợp đồng còn lại
    available_session_number = fields.Integer('Buổi còn lại', default=0)
    # số buổi tập miễn phí còn lại
    free_session_number = fields.Integer('Buổi tập PT khuyến mãi', store=True, default=0)

    @api.onchange('welly_gift_id')
    def _onchange_welly_gift_id(self):
        if self.welly_gift_id:
            session_day = sum(self.welly_gift_id.mapped('session_number'))
            self.free_session_number = session_day

    total_free_session_number = fields.Integer('Tổng số buổi free',
                                               compute='_compute_total_free_session_number',
                                               default=0, invisible=1,
                                               store=True,
                                               readonly=True)

    # Số buổi tích lượt khuyến mãi
    turn_card_free_number = fields.Integer('Số lượt khuyến mãi', compute='_compute_total_free_session_number', store=True, default=0)
    total_turn_card_free_number = fields.Integer('Tổng số lượt khuyến mãi',
                                               compute='_compute_total_free_session_number',
                                               default=0, invisible=1,
                                               store=True,
                                               readonly=True)

    @api.depends('welly_gift_id')
    def _compute_total_free_session_number(self):
        for rec in self:
            if rec.welly_gift_id:
                session_day = 0
                turn_card_free = 0
                if rec.welly_gift_id:
                    session_day = sum(rec.welly_gift_id.mapped('session_number'))
                    turn_card_free = sum(rec.welly_gift_id.mapped('turn_card_free_number'))
                rec.total_free_session_number = session_day
                rec.total_turn_card_free_number = turn_card_free
                rec.turn_card_free_number = turn_card_free
    # công nợ
    remaining_amount = fields.Monetary(
        string='Công nợ', related='welly_invoice_id.amount_residual_signed'
    )
    # Thời gian checkin
    welly_service_checkin_time_from = fields.Char(string="Thời gian bắt đầu checkin", related="welly_invoice_id.main_product_id.welly_service_checkin_time_from", store=True)
    welly_service_checkin_time_to = fields.Char(string="Thời gian kết thúc checkin", related="welly_invoice_id.main_product_id.welly_service_checkin_time_to", store=True)

    # Thời gian checkin float, dùng để so sánh
    checkin_time_from = fields.Float(compute="_compute_checkin_time_float", store=True)
    checkin_time_to = fields.Float(compute="_compute_checkin_time_float", store=True)

    @api.depends('welly_service_checkin_time_from', 'welly_service_checkin_time_to')
    def _compute_checkin_time_float(self):
        for record in self:
            record.checkin_time_from = self._convert_time_to_float(record.welly_service_checkin_time_from)
            record.checkin_time_to = self._convert_time_to_float(record.welly_service_checkin_time_to)

    def _convert_time_to_float(self, time_str):
        """ Chuyển HH:MM thành float (ví dụ: 14:30 → 14.5) """
        if time_str:
            hour, minute = map(int, time_str.split(':'))
            return hour + (minute / 60.0)
        return 0.0

    # Thông tin nội bộ
    membership_code = fields.Char(string='Mã HĐ membership')
    presenter = fields.Char(string='Người giới thiệu', related='welly_invoice_id.presenter', readonly=True)
    presenter_partner = fields.Many2one(string='Người giới thiệu', related='welly_invoice_id.presenter_partner', readonly=True)
    marketing_staff_id = fields.Many2many(
        'res.users', string='NV kinh doanh',
        relation='welly_contract_marketing_staff_rel',
        compute='_compute_marketing_staff_id',
        store=True, readonly=True)

    @api.depends('welly_invoice_id.marketing_staff_id')
    def _compute_marketing_staff_id(self):
        for record in self:
            record.marketing_staff_id = record.welly_invoice_id.marketing_staff_id

    pt_staff_id = fields.Many2many(
        'res.users', string='PT hỗ trợ',
        relation='welly_contract_pt_staff_rel',
        compute='_compute_pt_staff_id',
        store=True, readonly=True)

    @api.depends('welly_invoice_id.pt_staff_id')
    def _compute_pt_staff_id(self):
        for record in self:
            record.pt_staff_id = record.welly_invoice_id.pt_staff_id

    care_staff_id = fields.Many2many(
        'res.users', string='NV phụ trách HĐ',
        relation='welly_contract_care_staff_rel',
        compute='_compute_care_staff_id',
        store=True, readonly=True, tracking=True)

    @api.depends('marketing_staff_id')
    def _compute_care_staff_id(self):
        for record in self:
            if record.marketing_staff_id:
                record.care_staff_id = record.marketing_staff_id

    # Thông tin dịch vụ
    welly_service_ids = fields.Many2many(comodel_name='welly.service.type', string='Dịch vụ', related="welly_invoice_id.welly_service_ids")

    # Thông tin gói gia đình mapping từ invoice
    is_family_service = fields.Boolean(string='Gói gia đình', related='welly_invoice_id.is_family_service')
    family_member_qty = fields.Integer(string='Số lượng thành viên', related='welly_invoice_id.family_member_qty')
    # nguồn theo biên lai
    utm_source_id = fields.Many2one('utm.source', 'Nguồn', store=True, readonly=True,
                                    related='welly_invoice_id.source_id')

    _sql_constraints = [
        ('unique_name', 'unique(name)', _('Record must be unique!')),
    ]
    
    def button_send_request(self):
        for rec in self:
            if rec.state == 'draft':
                rec.state = 'confirm_admin'
                group = self.env.ref('welly_base.group_admin_club', raise_if_not_found=False)
                if group:
                    users = self.env['res.users'].search([('groups_id', 'in', group.id)])
                    if users:
                        for user in users:
                            summary = _('There is a contract that need to confirm!')
                            content = _('The contract named: %s need to confirm now. Please check it!') % (
                                rec.name)
                            rec.action_create_mail_activity(user, summary=summary, content=content)

    def button_confirm_admin(self):
        for rec in self:
            if rec.state == 'confirm_admin':
                rec.action_feedback()
                rec.state = 'sign_coo'
                group = self.env.ref('welly_base.group_coo', raise_if_not_found=False)
                if group:
                    users = self.env['res.users'].search([('groups_id', 'in', group.id)])
                    if users:
                        for user in users:
                            summary = _('There is a contract that need to approve!')
                            content = _('The contract named: %s need to approve now. Please check it!') % (
                                rec.name)
                            rec.action_create_mail_activity(user, summary=summary, content=content)
                        partners = users.mapped('partner_id')
                        vals = {'partners': partners,
                                'title': 'Hợp đồng cần phê duyệt'}
                        rec.notify_user(vals)

    def button_sign_coo(self):
        for rec in self:
            if not self.coo_signature:
                if self.env.user.user_signature:
                    self.coo_signature = self.env.user.user_signature
                # có thể ký điện tử hoặc in ký giấy
                # else:
                #     raise UserError(_("Please provide an coo electronic signature before confirming the order."))
            if rec.state == 'sign_coo':
                rec.action_feedback()
                rec.update({'state': 'sign_customer', 'coo_name': self.env.user.name})

    def button_sign_customer(self):
        for rec in self:
            # có thể ký điện tử hoặc in ký giấy
            # if not self.cus_signature:
            #     raise UserError(_("Please provide an customer electronic signature before confirming the order."))
            if rec.state == 'sign_customer':
                rec.state = 'confirm_recep'

    def button_confirm_to_active(self):
        for rec in self:
            if rec.state == 'confirm_recep':
                rec.state = 'waiting_active'
                rec.action_feedback()

    def button_recep_reject_to_sign_customer(self):
        for rec in self:
            if rec.state == 'confirm_recep':
                rec.state = 'sign_customer'

    def button_active(self):
        for rec in self:
            if rec.state in ['waiting_active', 'sign_customer', 'confirm_recep']:
                # Nếu là hợp đồng trả góp
                if rec.payment_type_welly == 'installment':
                    # Chưa thanh toán đủ
                    if rec.payment_state == 'partial':
                        end = fields.Date.today() - rec.date_start
                        date_end = rec.date_end + relativedelta(days=end.days)
                    # Đã thanh toán đủ
                    else:
                        date_end = self.convert_valid_time_type_to_day(fields.Date.today(),
                                                                       rec.valid_time,
                                                                       rec.valid_time_type)
                        if rec.welly_gift_id:
                            for gift in rec.welly_gift_id:
                                date_end = rec.convert_valid_time_type_to_day(date_end,
                                                                              gift.valid_time,
                                                                              gift.valid_time_type)
                else:
                    # nếu hđ có ngày kích hoạt và kich hoạt thủ công:
                    # Ngày Hết Hạn Mới = Ngày Hết Hạn Cũ + Khoảng thời gian của (Current Date - Ngày Kích Hoạt Mới)
                    if rec.date_end:
                        end = fields.Date.today() - rec.date_start
                        date_end = rec.date_end + relativedelta(days=end.days)
                    # nếu hđ không có ngày kích hoạt và kich hoạt thủ công:
                    # Ngày Hết Hạn Mới = current date + thời hạn + quà tặng (khoảng thời gian)
                    else:
                        date_end = self.convert_valid_time_type_to_day(fields.Date.today(),
                                                                       rec.valid_time,
                                                                       rec.valid_time_type)
                        if rec.welly_gift_id:
                            for gift in rec.welly_gift_id:
                                date_end = rec.convert_valid_time_type_to_day(date_end,
                                                                              gift.valid_time,
                                                                              gift.valid_time_type)
                rec.write({'state': 'activated', 'date_start': fields.Date.today(), 'date_end': date_end})
                rec.action_feedback()
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'type': 'success',
                        'message': _("Approved successfully."),
                        'next': {'type': 'ir.actions.act_window_close'},
                    }}

    def action_reject(self):
        """
        Action reject the document request content by approver.
        """
        for rec in self:
            if rec.state == 'confirm_admin':
                rec.state = 'reject'
                rec.action_feedback()
                self.message_post(body=f"Hợp đồng bị hủy bởi lí do:: {self.reject_reason}")
                if rec.opportunity_id.user_id:
                    summary = _('Hợp đồng đã bị hủy')
                    content = _('HỢp đồng: %s Đã bị hủy với lí do: %s. Vui lòng kiểm tra lại!') % (
                        rec.name, rec.reject_reason)
                    rec.action_create_mail_activity(rec.opportunity_id.user_id, summary=summary, content=content)

    @api.model
    def action_reject_contract(self):
        # check quyền
        user = self.env.user
        # chỉ có quyền COO, ADMIN Club, Admin hệ thống mới được sử dụng
        if not user.has_group('welly_base.group_coo') and not user.has_group(
                'welly_base.group_admin_club') and not user.has_group('base.group_system'):
            raise UserError(_("Bạn không có quyền thao tác."))
        # direct đến wizard
        view_id = self.env.ref('welly_base.reject_form').id
        return {
            'name': 'Hủy Hoàn Toàn HĐ',
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'reject.contract',
            'view_id': view_id,
            'type': 'ir.actions.act_window',
            'target': 'new',
            'context': {},
        }
        pass

    @api.model
    def action_change_session_contract(self):
        if self.state != 'activated':
            raise UserError(_("Chỉ dùng khi hợp đồng đã được kích hoạt."))
        # check quyền
        user = self.env.user
        # chỉ có quyền COO, ADMIN Club, Admin hệ thống mới được sử dụng
        if not user.has_group('welly_base.group_coo') and not user.has_group(
                'welly_base.group_admin_club') and not user.has_group('base.group_system') and not user.has_group('welly_base.group_welly_account'):
            raise UserError(_("Bạn không có quyền thao tác."))
        # direct đến wizard
        view_id = self.env.ref('welly_base.change_session_form').id

        return {
            'name': 'Cộng/Trừ buổi',
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'change.contract.session',
            'view_id': view_id,
            'type': 'ir.actions.act_window',
            'target': 'new',
            'context': {
                'default_service_type': self.service_type,
                'default_available_session_number': self.available_session_number,
                'default_free_session_number': self.free_session_number,
                'default_free_turn_card_number': self.turn_card_free_number,
                'default_free_session_number_initial': self.total_free_session_number,
                'default_free_turn_card_number_initial': self.total_turn_card_free_number,
                'default_session_number_initial': self.session_number,
            }
        }
        pass

    def change_date_start_end_button(self):
        # direct đến wizard
        view_id = self.env.ref('welly_base.change_date_start_end_form').id
        return {
            'name': 'Thay Đổi Ngày Hết Hạn',
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'change.contract.date',
            'view_id': view_id,
            'type': 'ir.actions.act_window',
            'target': 'new',
            'context': {
                'default_date_start': self.date_start,
                'default_old_date_start': self.date_start,
                'default_date_end': self.date_end,
                'default_old_date_end': self.date_end,
            }
        }


    def action_create_mail_activity(self, user_id, summary, content):
        self.ensure_one()
        activity_type = self.env['mail.activity.type'].search([('name', 'ilike', 'email')])[0]
        res_model_id = self.env['ir.model']._get_id('welly.contract')
        msg = {
            'activity_type_id': activity_type and activity_type.id,
            'summary': summary or activity_type.summary,
            'automated': True,
            'note': content or activity_type.default_note,
            'res_model_id': res_model_id,
            'res_id': self.id,
            'user_id': user_id.id,
        }
        self.env['mail.activity'].create(msg)

    @api.model
    def action_submit_signature(self):
        order = self.browse(self._context.get('active_id'))
        if order:
            order.write({'signature': self.signature})
        return {'type': 'ir.actions.act_window_close'}

    def action_cancel(self):
        self.state = "cancel"

    def action_draft(self):
        self.state = "draft"

    @api.constrains('session_number', 'available_session_number')
    def _check_session_number(self):
        for record in self:
            if record.available_session_number and record.session_number and record.available_session_number > record.session_number:
                raise ValidationError("Số buổi còn hiệu lực không được lớn hơn số buổi")

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if vals.get('name', _("New")) == _("New"):
                seq_date = fields.Datetime.context_timestamp(
                    self, fields.Datetime.to_datetime(vals['date_created'])
                ) if 'date_order' in vals else None
                vals['name'] = self.env['ir.sequence'].next_by_code(
                    'welly.contract', sequence_date=seq_date) or _("New")
                account_move = self.env['account.move'].browse(vals['welly_invoice_id'])
                # Thêm giá trị mặc định cho available_session_number
                if 'available_session_number' not in vals:
                    if account_move.service_type != 'member':
                        vals['available_session_number'] = account_move.session_number
                if 'free_session_number' not in vals:
                    gift = 0
                    for welly_gift in account_move.welly_gift_id:
                        gift += welly_gift.session_number
                    vals['free_session_number'] = gift

                # tạo giá trị cho partner_account_ids
                if vals.get('partner_account_move_ids'):
                    partner_ids = []
                    for cmd in vals.get('partner_account_move_ids'):
                        if cmd[0] == 4:
                            partner_id = self.env['welly.partner.account.move'].browse(cmd[1]).partner_id.id
                            partner_ids.append(partner_id)
                    vals['partner_account_ids'] = [(6, 0, partner_ids)]
                account_move = self.env['account.move'].browse(vals['welly_invoice_id'])
                # Nếu là hợp đồng trả góp
                if account_move.payment_type_welly == 'installment':
                    welly_payment_lines = self.env['welly.payment.line'].search([
                        ('welly_account_move_id', '=', vals['welly_invoice_id']),
                        ('payment_state', '=', 'posted')
                    ])
                    # Sắp xếp tăng dần theo ngày thanh toán và id tăng dần
                    welly_payment_lines = sorted(welly_payment_lines, key=lambda x: (x.date, x.id))
                    vals['available_session_number'] = sum(payment_line.allow_session_number for payment_line in welly_payment_lines)
                    # Chưa payfull thì date_start theo payment line đầu, date_end theo payment line cuối
                    if account_move.payment_state == 'partial':
                        vals['date_start'] = welly_payment_lines[0].effective_date
                        vals['date_end'] = welly_payment_lines[-1].expired_date
                    # Đã payfull thì date_start và date_end theo account move
                    elif account_move.payment_state == 'paid':
                        vals['date_start'] = account_move.date_start
                        vals['date_end'] = account_move.date_end
        res = super().create(vals_list)
        move_id = res.welly_invoice_id
        action = self.env.ref('welly_base.action_move_out_invoice_type_welly')
        res.message_post(body=f"Hợp đồng được tạo từ biên lai: <a href='/web#id={move_id.id}&action={action.id}&view_type=form&model=account.move'>{move_id.name}</a>")
        move_id.message_post(body=f"Tạo mới hợp đồng: <a href='/web#id={res.id}&view_type=form&model=welly.contract'>{res.name}</a>")
        return res

    def write(self, vals):
        if 'welly_gift_id' in vals:
            gift_ids = self.env['welly.gift'].browse(vals['welly_gift_id'][0][2])
            vals['free_session_number'] = sum(gift_ids.mapped('session_number'))
        res = super(WellyContract, self).write(vals)
        if 'state' in vals:
            self._compute_is_installment_activated()
        return res

    def action_feedback(self):
        activity_type = self.env['mail.activity.type'].search([('name', 'ilike', 'email')])[0]
        activity = self.env['mail.activity'].search([
            ('activity_type_id', '=', activity_type.id),
            ('res_id', '=', self.id),
            ('res_model_id', '=', self.env['ir.model']._get_id('welly.contract'))
        ])
        for act in activity:
            if act.user_id == self.env.user and self.state != 'reject':
                act.action_feedback(feedback='Hoàn thành!!!')
            else:
                act.unlink()

    def check_and_activate_contracts(self):
        now = (fields.Datetime.now() + timedelta(hours=7)).date()
        contracts = self.env['welly.contract'].search([('date_start', '<=', now), ('state', '=', 'waiting_active')])
        for contract in contracts:
            if contract.date_end:
                contract.update({'state': 'activated'})

    def complete_contract(self):
        now = (fields.Datetime.now() + timedelta(hours=7)).date()
        contracts = self.env['welly.contract'].search([('date_end', '<=', now), '|',
                                                       ('state', '=', 'activated'),
                                                       ('is_installment_activated', '=', True)])
        for contract in contracts:
            contract.update({'state': 'done', 'is_installment_activated': False})

    @api.depends('other_contact_id')
    def _compute_other_contact(self):
        for rec in self:
            partner = rec.other_contact_id
            street = partner.street
            street2 = partner.street2
            city = partner.city
            non_empty_values = [value for value in [
                street, street2, city] if value]
            full_address = ', '.join(
                non_empty_values) if non_empty_values else ''
            rec.other_contact_name_print = partner.name
            rec.other_contact_address = full_address
            rec.other_contact_id_number = partner.partner_id_number
            rec.other_contact_gender = partner.gender
            rec.other_contact_nationality_id = partner.nationality_id
            rec.other_contact_phone = partner.phone

    @api.depends('guardian_id')
    def _compute_guardian_info(self):
        for rec in self:
            partner = rec.guardian_id
            street = partner.street
            street2 = partner.street2
            city = partner.city
            non_empty_values = [value for value in [
                street, street2, city] if value]
            full_address = ', '.join(
                non_empty_values) if non_empty_values else ''
            rec.guardian_name_print = partner.name
            rec.guardian_address = full_address
            rec.guardian_id_number = partner.partner_id_number
            rec.guardian_gender = partner.gender
            rec.guardian_nationality_id = partner.nationality_id
            rec.guardian_phone = partner.phone

    @api.constrains('date_start', 'date_end')
    def _check_dates(self):
        for record in self:
            if record.date_start and record.date_end and record.date_start > record.date_end:
                raise UserError("Ngày kết thúc phải lớn hơn hoặc bằng ngày bắt đầu!")

    @api.depends('partner_id')
    def _compute_partner_info(self):
        for rec in self:
            partner = rec.partner_id
            street = rec.partner_id.street
            street2 = rec.partner_id.street2
            city = rec.partner_id.city
            non_empty_values = [value for value in [
                street, street2, city] if value]
            full_address = ', '.join(
                non_empty_values) if non_empty_values else ''
            rec.partner_code = partner.welly_code
            rec.partner_name_print = partner.name
            rec.address = full_address
            rec.partner_id_number = partner.partner_id_number
            rec.gender = partner.gender
            rec.birthdate = partner.birthdate
            rec.nationality_id = partner.nationality_id
            rec.phone = partner.phone
            rec.email = partner.email

    @api.depends('registration_form_id')
    def _compute_registration_form_info(self):
        for rec in self:
            rec.registration_form_name_print = rec.registration_form_id.name

    @api.depends('welly_gift_id')
    def _compute_gift_info(self):
        for rec in self:
            gifts = [gift.name for gift in rec.welly_gift_id]
            rec.welly_gift_name_print = ', '.join(gifts)

    @api.depends('exercise_form_id')
    def _compute_exercise_form_info(self):
        for rec in self:
            rec.exercise_form_name_print = rec.exercise_form_id.name

    @api.depends('sale_order_template_id')
    def _compute_sale_order_template_info(self):
        for rec in self:
            rec.sale_order_template_name_print = rec.sale_order_template_id.name

    @api.depends('campaign_id')
    def _compute_campaign_info(self):
        for rec in self:
            rec.campaign_name_print = rec.campaign_id.name

    @api.depends('coach_id')
    def _compute_coach_info(self):
        for rec in self:
            rec.coach_name_print = rec.coach_id.name

    @api.depends('pay_amount')
    def _compute_pay_amount_to_text(self):
        for rec in self:
            if rec.pay_amount:
                rec.pay_amount_to_text = rec._num2words(
                    rec.pay_amount, 'vi_VN') + ' ' + rec.currency_id.name
            else:
                rec.pay_amount_to_text = ''

    def unlink(self):
        for record in self:
            if record.state not in ('draft', 'admin_confirm'):
                raise UserError(_("Hợp đồng đã được xác nhận không thể bị xóa."))
        return super(WellyContract, self).unlink()

    def notify_user(self, vals):
        notifications = []
        if 'partners' in vals:
            for p in vals['partners']:
                action = self.env.ref('welly_base.action_view_welly_contract_tree_welly')
                bus_message = {
                    "type": vals['type'] if 'type' in vals else 'default',
                    'message': '%s',
                    'links': [{
                        'label': f'Hợp đồng {self.name}',
                        'url': f'#action={action.id}&id={self.id}&view_type=form&model=welly.contract'
                    }],
                    "title": vals['title'],
                    "sticky": True,
                }
                notifications.append((
                    p, 'web.notify', [bus_message]
                ))
        self.env['bus.bus']._sendmany(notifications)

    def _num2words(self, number, lang):
        if num2words is None:
            return ""
        try:
            return num2words(number, lang=lang).title()
        except NotImplementedError:
            return num2words(number, lang='en').title()

    @api.model
    def _get_last_month_domain(self):
        # Lấy thời gian hiện tại theo UTC
        today = datetime.utcnow()

        # Tính toán ngày đầu tiên của tháng hiện tại và trừ đi 1 tháng
        first_day_of_this_month = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        first_day_of_last_month_utc = (first_day_of_this_month - relativedelta(months=1) - relativedelta(hours=7))

        # Tính toán ngày cuối cùng của tháng trước
        last_day_of_last_month_utc = first_day_of_this_month - timedelta(seconds=1) - timedelta(hours=7)

        # Trả về domain để lọc các bản ghi trong khoảng thời gian này
        return [('&'),
                ('create_date', '>=', first_day_of_last_month_utc.strftime('%Y-%m-%d %H:%M:%S')),
                ('create_date', '<=', last_day_of_last_month_utc.strftime('%Y-%m-%d %H:%M:%S'))]

    @api.model
    def _get_current_month_domain(self):
        # Lấy thời gian hiện tại theo UTC
        today = datetime.utcnow()

        # Tính toán ngày đầu tiên của tháng hiện tại
        first_day_of_this_month = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        first_day_of_this_month_utc = first_day_of_this_month - relativedelta(hours=7)

        # Tính toán ngày đầu tiên của tháng sau
        first_day_of_next_month = (first_day_of_this_month + relativedelta(months=1)).replace(day=1)
        first_day_of_next_month_utc = first_day_of_next_month - relativedelta(hours=7)

        # Ngày cuối cùng của tháng này là một ngày trước ngày đầu tiên của tháng sau
        last_day_of_this_month_utc = first_day_of_next_month_utc - relativedelta(seconds=1)

        # Trả về domain để lọc các bản ghi trong khoảng thời gian này
        return [('&'),
                ('create_date', '>=', first_day_of_this_month_utc.strftime('%Y-%m-%d %H:%M:%S')),
                ('create_date', '<=', last_day_of_this_month_utc.strftime('%Y-%m-%d %H:%M:%S'))]

    # override hàm searh để thêm bộ lọc tùy chỉnh
    @api.model
    def search(self, domain, offset=0, limit=None, order=None, count=False):
        # Kiểm tra nếu domain chứa 'last_month'
        for index, arg in enumerate(domain):
            if isinstance(arg, (list, tuple)) and len(arg) == 3 and arg[0] == 'create_date' and arg[2] == 'last_month':
                # Thay thế với domain tháng trước
                domain[index:index + 1] = self._get_last_month_domain()
            if isinstance(arg, (list, tuple)) and len(arg) == 3 and arg[0] == 'create_date' and arg[2] == 'current_month':
                # Thay thế với domain tháng này
                domain[index:index + 1] = self._get_current_month_domain()
        return super(WellyContract, self).search(domain, offset, limit, order, count)

    @api.model
    def read_group(self, domain, fields, groupby, offset=0, limit=None, orderby=False, lazy=True):
        # Kiểm tra nếu domain chứa 'last_month'
        for index, arg in enumerate(domain):
            if isinstance(arg, (list, tuple)) and len(arg) == 3 and arg[0] == 'create_date' and arg[2] == 'last_month':
                # Thay thế với domain tháng trước
                domain[index:index + 1] = self._get_last_month_domain()
            if isinstance(arg, (list, tuple)) and len(arg) == 3 and arg[0] == 'create_date' and arg[2] == 'current_month':
                # Thay thế với domain tháng này
                domain[index:index + 1] = self._get_current_month_domain()
        return super(WellyContract, self).read_group(domain, fields, groupby, offset, limit, orderby, lazy)


    def action_view_partner(self):
        """Mở form xem thông tin chi tiết của khách hàng"""
        self.ensure_one()
        return {
            'name': 'Thông tin khách hàng',
            'type': 'ir.actions.act_window',
            'res_model': 'res.partner',
            'view_mode': 'form',
            'res_id': self.partner_id.id,
            'target': 'current',
            'flags': {'mode': 'readonly'},
            'context': {'create': False, 'edit': False}
        }

    def action_report_expiring_membership_statistics(self):
        """
        Mở danh sách các hợp đồng hội viên sắp hết hạn
        """
        # Tính ngày bắt đầu (hôm nay) và ngày kết thúc (30 ngày tới)
        today = date.today()
        date_end_limit = today + timedelta(days=30)

        # Trạng thái cần lọc
        valid_states = [
            'confirm_admin', 'sign_coo', 'sign_customer',
            'confirm_recep', 'waiting_active', 'activated'
        ]

        # Tạo domain tìm kiếm
        domain_contracts = [
            ('state', 'in', valid_states),
            ('date_end', '>=', today),
            ('date_end', '<=', date_end_limit),
        ]

        return {
            'name': 'Thống kê hội viên sắp hết hạn (Trong 30 ngày tới)',
            'type': 'ir.actions.act_window',
            'res_model': 'welly.contract',
            'view_mode': 'tree,form',
            'domain': domain_contracts,
            'target': 'current',
        }
