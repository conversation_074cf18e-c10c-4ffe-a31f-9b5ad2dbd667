<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="welly_contract_coach_report" model="ir.actions.report">
        <field name="name">H<PERSON><PERSON> đ<PERSON>ng hu<PERSON>n luy<PERSON> viên</field>
        <field name="type">ir.actions.report</field>
        <field name="model">welly.contract</field>
        <field name="report_name">coach_contract_py3o_2023</field>
        <field name="report_type">py3o</field>
        <field name="py3o_filetype">pdf</field>
        <field name="module">welly_base</field>
        <field name="py3o_multi_in_one">True</field>
        <field name="py3o_template_fallback">reports/coach_contract_report.odt</field>
        <field name="binding_model_id" ref="welly_base.model_welly_contract" />
        <field name="binding_type">report</field>
    </record>
</odoo>