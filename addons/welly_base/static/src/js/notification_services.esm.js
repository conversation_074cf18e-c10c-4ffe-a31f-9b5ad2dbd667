/** @odoo-module **/
import {Markup} from "web.utils";
import {browser} from "@web/core/browser/browser";
import {registry} from "@web/core/registry";
import { sprintf } from "@web/core/utils/strings";
import { session } from "@web/session";


export const webNotificationService = {
    dependencies: ["bus_service", "notification", "company"],

    start(env, {bus_service, notification, company}) {
        const {allowedCompanyIds} = company;
        let webNotifTimeouts = {};
        /**
         * Displays the web notification on user's screen
         */
        function displayWebNotification(notifications) {
            Object.values(webNotifTimeouts).forEach((notif) =>
                browser.clearTimeout(notif)
            );
            webNotifTimeouts = {};
            
            notifications.forEach(function (notif) {
                browser.setTimeout(function () {
                    let a_links = ((notif.links) || []).map((link) => {
                        return `<a href="${link.url}" target="_blank">${link.label}</a>`;
                    });
                    const message_link = sprintf(_.escape(notif.message), ...a_links);
                    notification.add(Markup(message_link), {
                        title: notif.title,
                        type: notif.type,
                        sticky: notif.sticky,
                        className: notif.className,
                    });
                });
            });
            if (notifications.length > 0) {
                const notification_manager = document.querySelector("div.o_notification_manager");
                if (!notification_manager) {
                    return;
                }
                const existBtnCloseAll = notification_manager.querySelector("button.o_close_all_notifications");
                if (existBtnCloseAll) {
                    return;
                }
                // add button close all
                const parentBtn = document.createElement("div");
                parentBtn.className = "text-right";
                const button_close_all = document.createElement("button");
                button_close_all.classList.add("o_close_all_notifications", "btn", "btn-primary");
                button_close_all.style.fontSize = "0.85rem"
                button_close_all.innerHTML = "Close All";
                parentBtn.appendChild(button_close_all)
                notification_manager.prepend(parentBtn);
                button_close_all.onclick = () => {
                    notification_manager.querySelectorAll("button").forEach(e=>e.click())
                };
            }
        }

        bus_service.addEventListener("notification", ({detail: notifications}) => {
            for (const {payload, type} of notifications) {
                if (type === "web.notify") {
                    const data = payload.filter(e => allowedCompanyIds.indexOf(e.company_id) !== -1);
                    displayWebNotification(data);
                }
            }
        });
        bus_service.start();
    },
};

registry.category("services").add("webNotification", webNotificationService);
