<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--    view tree-->
    <record id="view_res_partner_form_inherit" model="ir.ui.view">
        <field name="name">res.partner.form.inherit</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='image_1920']" position="attributes">
                <attribute name="options">{"zoom": true, "preview_image": "image_128"}</attribute>
            </xpath>
            <xpath expr="//field[@name='category_id']" position="replace">
            </xpath>
            <xpath expr="//field[@name='lang']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='mobile']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='website']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='title']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='vat']" position="after">
                <field name="nationality_id"/>
                <field name="category_id" widget="many2many_tags"
                       options="{'color_field': 'color', 'no_create_edit': True}"
                       placeholder='e.g. "B2B", "VIP", "Consulting", ...'/>
                <field name="lang" attrs="{'invisible': [('active_lang_count', '&lt;=', 1)]}"/>
                <field name="user_signature" widget="signature"/>
            </xpath>
            <xpath expr="//field[@name='website']" position="after">
                <field name="welly_code" string="Mã Hội Viên"/>
                <field name="is_member" invisible="1"/>
                <field name="birthdate"/>
                <field name="gender"/>
                <field name="partner_id_number"/>
                <field name="identification_card_front" widget="image_editor" options="{'zoom': true, 'preview_image': 'identification_card_front'}"/>
                <field name="identification_card_back" widget="image_editor" options="{'zoom': true, 'preview_image': 'identification_card_back'}"/>
            </xpath>
            <xpath expr="//field[@name='function']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//notebook/page[@name='contact_addresses']" position="before">
                <page name="Danh sách Dịch vụ" string="Danh sách Dịch vụ">
                    <field name="welly_contract_ids">
                        <tree create="false" delete="false">
                            <field name="name" string="Mã HĐ"/>
                            <field name="sale_order_template_name_print" string="Tên dịch vụ"/>
                            <field name="service_type" string="Loại hình"/>
                            <field name="payment_type_welly" string="Kiểu thanh toán"/>
                            <field name="registration_form_name_print" string="Hình thức đăng ký"/>
                            <field name="date_start" string="Ngày bắt đầu"/>
                            <field name="date_end" string="Ngày kết thúc"/>
                            <field name="available_session_number" string="Số buổi còn lại"/>
                            <field name="pay_amount" string="Tổng tiền"/>
                            <field name="remaining_amount" string="Công nợ"/>
                            <field name="state" string="Trạng thái"/>
                        </tree>
                    </field>
                </page>
            </xpath>

            <xpath expr="//field[@name='parent_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>

    <!--    view tree-->
    <record id="view_welly_res_partner_tree_inherit" model="ir.ui.view">
        <field name="name">res.partner.tree.inherit</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='display_name']" position="before">
                <field name="create_date" string="Ngày tạo" optional="hide"/>
            </xpath>
            <xpath expr="//field[@name='email']" position="before">
                <field name="birthdate" string="Ngày sinh" optional="show"/>
                <field name="gender" string="Giới tính" optional="hide"/>
            </xpath>
            <xpath expr="//field[@name='email']" position="after">
                <field name="welly_code" string="Mã hội viên" optional="hide"/>
                <field name="street" string="Địa chỉ" optional="show"/>
                <field name="partner_id_number" optional="hide"/>
            </xpath>
            <xpath expr="//field[@name='country_id']" position="after">
                <field name="create_uid" string="Người tạo" optional="hide"/>
            </xpath>
            <xpath expr="//field[@name='company_id']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='country_id']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='city']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='user_id']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
        </field>
    </record>

    <!-- Add search view with custom filters -->
    <record id="view_res_partner_filter_inherit" model="ir.ui.view">
        <field name="name">res.partner.search.inherit</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_res_partner_filter"/>
        <field name="arch" type="xml">
            <filter name="inactive" position="after">
                <separator/>
                <filter string="Số lượng hóa đơn lớn hơn 1" name="invoice_count_gt_1" 
                        domain="[('total_invoiced_store', '>', 1)]"/>
            </filter>
        </field>
    </record>

    <record id="action_contacts_not_user" model="ir.actions.act_window">
        <field name="name">Khách Hàng</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.partner</field>
        <field name="view_mode">kanban,tree,form,activity</field>
        <field name="domain">[('user_ids', '=', False)]</field>
        <field name="search_view_id" ref="base.view_res_partner_filter"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a Contact in your address book
            </p>
            <p>
                Odoo helps you track all activities related to your contacts.
            </p>
        </field>
    </record>
    <record id="action_contacts_user" model="ir.actions.act_window">
        <field name="name">Liên hệ nhân viên</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.partner</field>
        <field name="view_mode">kanban,tree,form,activity</field>
        <field name="domain">[('user_ids', '!=', False)]</field>
        <field name="search_view_id" ref="base.view_res_partner_filter"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a Contact in your address book
            </p>
            <p>
                Odoo helps you track all activities related to your contacts.
            </p>
        </field>
    </record>
    <record model="ir.ui.view" id="partner_view_buttons">
        <field name="name">partner.view.buttons</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="priority" eval="11"/>
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <button type="object" class="oe_stat_button" icon="fa-pencil-square-o"
                        name="action_view_welly_contracts"
                        groups="account.group_account_invoice,account.group_account_readonly"
                        context="{'default_partner_id': active_id}">
                    <div class="o_form_field o_stat_info">
                        <span class="o_stat_value">
                            <field name="total_contract"/>
                        </span>
                        <span class="o_stat_text">Hợp đồng</span>
                    </div>
                </button>
            </div>
        </field>
    </record>

    <record id="view_res_user_form_inherit" model="ir.ui.view">
        <field name="name">res.users.form.inherit</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_form_simple_modif"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='email']" position="after">
                <field name="telegram_id"/>
            </xpath>
            <xpath expr="//field[@name='signature']" position="after">
                <field name="user_signature" string="Chữ ký số" widget="signature"/>
            </xpath>
        </field>
    </record>

    <record id="model_res_partner_action_remove_user_signature" model="ir.actions.server">
        <field name="name">Xóa chữ ký</field>
        <field name="model_id" ref="base.model_res_partner"/>
        <field name="binding_model_id" ref="base.model_res_partner"/>
        <field name="binding_view_types">form</field>
        <field name="state">code</field>
        <field name="code">action = records.action_remove_user_signature()</field>
    </record>

    <record id="res_user_action_user_signature" model="ir.actions.server">
        <field name="name">Xóa chữ ký</field>
        <field name="model_id" ref="base.model_res_users"/>
        <field name="binding_model_id" ref="base.model_res_users"/>
        <field name="binding_view_types">form</field>
        <field name="state">code</field>
        <field name="code">action = records.action_remove_user_signature()</field>
    </record>

    <record id="res_partner_action_membership" model="ir.actions.act_window">
        <field name="name">Hội Viên</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.partner</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="domain">[('is_member', '=', True)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new customer in your address book
            </p><p>
                Odoo helps you easily track all activities related to a customer.
            </p>
        </field>
    </record>

        <!-- Action gọi method action_open_not_register_customers -->
    <record id="action_open_partner_not_register" model="ir.actions.server">
        <field name="name">Khách hàng chưa đăng ký dịch vụ</field>
        <field name="model_id" ref="base.model_res_partner"/>
        <field name="state">code</field>
        <field name="code">action = model.action_open_not_register_customers()</field>
    </record>

    <record id="action_report_partner_duplicate" model="ir.actions.server">
        <field name="name">Liên hệ trùng lặp</field>
        <field name="model_id" ref="base.model_res_partner"/>
        <field name="state">code</field>
        <field name="code">action = model.action_report_partner_duplicate()</field>
    </record>
</odoo>
