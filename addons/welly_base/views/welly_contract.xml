<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_welly_contract_form" model="ir.ui.view">
            <field name="name">welly.contract.form</field>
            <field name="model">welly.contract</field>
            <field name="arch" type="xml">
                <form string="Welly Contract" class="o_sale_order" style="flex-wrap: wrap !important">
                    <style>
                        .welly_border input,.welly_border select,.welly_border div &gt; span:first-child {
                        font-weight: bold;
                        }
                    </style>
                    <header>
                        <button name="action_draft" type="object"
                                attrs="{'invisible': [('state', '!=', 'cancel')]}"
                                string="Set to Quotation" data-hotkey="w"/>
                        <!-- request -->
                        <button name="button_send_request" type="object" string="Send Request"
                                groups="welly_base.group_sales,welly_base.group_coo,welly_base.group_admin_club"
                                class="btn-primary"
                                data-hotkey="o"
                                title="Send request to admin"
                                attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                        <!-- admin confirm -->
                        <button name="button_confirm_admin" type="object" string="Confirm"
                                class="btn-primary"
                                data-hotkey="o"
                                groups="welly_base.group_admin_club"
                                attrs="{'invisible': [('state', '!=', 'confirm_admin')]}"/>
                        <!--  confirm customer -->
                        <button name="button_confirm_to_active" type="object" string="Xác nhận chờ kích hoạt"
                                groups="welly_base.group_receptionist,welly_base.group_admin_club,welly_base.group_admin_online"
                                class="btn-primary"
                                data-hotkey="o"
                                attrs="{'invisible': [('state', '!=', 'confirm_recep')]}"/>
                        <!--  confirm customer -->
                        <button name="button_recep_reject_to_sign_customer" type="object" string="Reject"
                                groups="welly_base.group_receptionist,welly_base.group_admin_club,welly_base.group_admin_online"
                                class="btn-primary"
                                data-hotkey="o"
                                attrs="{'invisible': [('state', '!=', 'confirm_recep')]}"/>
                        <!-- coo sign -->
                        <button name="button_sign_coo" type="object"
                                class="btn-primary"
                                string="COO Sign"
                                groups="welly_base.group_coo"
                                attrs="{'invisible': [('state', 'not in', ('sign_coo'))]}"/>
                        <button name="%(welly_base.action_sale_order_signature_form)d" type="action"
                                class="btn-primary"
                                string="Customer Sign"
                                groups="welly_base.group_coo,welly_base.group_receptionist"
                                context="{'default_signer_name': partner_name_print}"
                                attrs="{'invisible': [('state', 'not in', ('sign_customer'))]}"/>
                        <!-- admin reject -->
                        <button name="%(welly_base.action_sale_order_reject_form)d" type="action"
                                groups="welly_base.group_admin_club,welly_base.group_coo"
                                string="Reject"
                                attrs="{'invisible': [('state', 'not in', ('confirm_admin','sign_coo'))]}"/>

                        <button name="button_active" type="object" string="Kích hoạt hợp đồng"
                                class="btn-primary"
                                confirm="Are you sure you want to activate this contract ?"
                                data-hotkey="o"
                                groups="welly_base.group_receptionist,welly_base.group_admin_club"
                                attrs="{'invisible': [('state', 'not in', ('waiting_active', 'sign_customer', 'confirm_recep'))]}"/>

                        <!--# thay đổi ngày bắt đầu và hết hạn của hợp đồng khi đã kích hoạt Phân quyền thao tác button: Lễ tân, Admin Club, COO-->
                        <button name="change_date_start_end_button" type="object"
                                class="btn-primary"
                                string="Thay đổi ngày hết hạn"
                                groups="welly_base.group_receptionist,welly_base.group_admin_club,welly_base.group_coo"
                                attrs="{'invisible': [('state', 'not in', ('waiting_active','activated', 'closed', 'done'))]}"/>

                        <!--# thay đổi HLV ở HĐ: Quyền Admin Club, COO, Quản lý PT-->
                        <button name="%(welly_base.action_change_coach)d" type="action"
                                class="btn-primary"
                                string="Thay đổi HLV"
                                groups="welly_base.group_admin_club,welly_base.group_coo,welly_base.group_pt_manager"
                                attrs="{'invisible': [('state', 'not in', ('activated'))]}"/>


                        <!--# Đóng hợp đồng khi ở trạng thái activate-->
                        <button name="%(welly_base.action_close_contract)d" type="action"
                                class="btn-primary"
                                string="Đóng hợp đồng"
                                groups="welly_base.group_receptionist,welly_base.group_admin_club,welly_base.group_admin_online"
                                attrs="{'invisible': [('state', 'not in', ('activated'))]}"/>

                        <field name="state" widget="statusbar"
                               statusbar_visible="draft,confirm_admin,waiting_active"/>
                    </header>
                    <sheet>
                        <!-- Payment status for invoices / receipts -->
                        <widget
                                name="web_ribbon" title="Đã thanh toán"
                                attrs="{'invisible': [('payment_state', '!=', 'paid')]}"/>
                        <widget
                                name="web_ribbon" title="Trả góp một phần"
                                attrs="{'invisible': [('payment_state', '!=', 'partial')]}"/>

                        <div class="oe_title">
                            <h1>
                                <field name="name" string="Mã hợp đồng" readonly="1"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="service_type" widget="radio" nolabel="1"/>
                            </group>
                            <group class="welly_border" style="font-weight: bold;">
                                <field name="welly_location" options='{"no_open": True}'
                                       readonly="1" invisible="1" string="Trung tâm Fitness"/>
                                <field name="welly_location_many2_many"
                                       widget="many2many_tags"
                                       options='{"no_open": True}'
                                       readonly="1" string="Địa điểm phòng tập"/>
                                <field name="welly_invoice_id" options='{"no_open": True}'
                                       domain="[('service_type', '=', service_type)]" invisible="1"/>
                                <field name="partner_code" string="Mã Hội Viên" readonly="1"/>
                            </group>
                            <h4 colspan="4">I/ THÔNG TIN CHUNG</h4>
                            <br/>
                            <br/>
                            <group id="first_para_left_group" class="welly_border" style="font-weight: bold;">
                                <field name="partner_id" options='{"no_open": True}'
                                       readonly="1" invisible="1" string="Người đứng tên"/>
                                    
                                <label for="partner_name_print">Người đứng tên</label>
                                <div class="o_row pt-1">
                                    <field name="partner_name_print" string="Người đứng tên"
                                            help="Người đứng tên hợp đồng"
                                            readonly="1"
                                    />
                                    <button name="action_view_partner" 
                                            type="object"
                                            class="btn btn-link oe_inline"
                                            attrs="{'invisible': [('partner_id', '=', False)]}"
                                            help="Xem thông tin chi tiết của khách hàng"
                                            icon="fa-arrow-right fa-lg"
                                    />
                                </div>
                                <field name="company_id" invisible="1"/>
                                <field name="create_user_id" invisible="1"/>
                                <field name="gender" readonly="1"/>
                                <field name="address" readonly="1"/>
                                <field name="birthdate" readonly="1"/>
                                <h4 colspan="2">II/ THÔNG TIN SẢN PHẨM - THANH TOÁN</h4>

                                <field name="sale_order_template_id" string="Thẻ thành viên"
                                       options='{"no_open": True}'
                                       attrs="{'invisible': ['|', ('state', '!=', 'draft'), ('service_type', '=', 'pt')]}"/>
                                <field name="sale_order_template_name_print" string="Thẻ thành viên" readonly="1"
                                       attrs="{'invisible': ['|', ('state', '=', 'draft'), ('service_type', '=', 'pt')]}"/>
                                <field name="sale_order_template_id" string="Gói dịch vụ"
                                       options='{"no_open": True}'
                                       attrs="{'invisible': ['|', ('state', '!=', 'draft'), ('service_type', 'not in', ['pt','turn_card'])]}"/>
                                <field name="sale_order_template_name_print" string="Gói dịch vụ" readonly="1"
                                       attrs="{'invisible': ['|', ('state', '=', 'draft'), ('service_type', 'not in', ['pt','turn_card'])]}"/>
                                <field name="welly_service_ids" string="Dịch vụ" widget="many2many_tags" readonly="1"/>
                                <field name="coach_id"
                                       attrs="{'invisible': ['|', ('service_type', '!=', 'pt'), ('state', '!=', 'draft')]}"/>
                                <field name="coach_name_print" readonly="1" string="Huấn luyện viên"
                                       attrs="{'invisible': ['|', ('service_type', '!=', 'pt'), ('state', '=', 'draft')]}"/>
                                <field name="exercise_form_id"
                                       attrs="{'invisible': ['|', ('service_type', '!=', 'pt'), ('state', '!=', 'draft')]}"
                                       readonly="1"/>
                                <field name="exercise_form_name_print" string="Hình thức tập luyện" readonly="1"
                                       attrs="{'invisible': ['|', ('service_type', '!=', 'pt'), ('state', '=', 'draft')]}"/>

                                <!-- Số buổi tập với PT -->
                                <field name="session_number" string="Số buổi" readonly="1"
                                       attrs="{'invisible': [('service_type', '!=', 'pt')]}"/>
                                <field name="available_session_number" string="Số buổi còn hiệu lực"
                                       attrs="{'invisible': [('service_type', '!=', 'pt')],
                                               'readonly': [('state', '!=', 'draft')]}"/>
                                <!-- Số lượt -->
                                <field name="session_number" string="Số lượt" readonly="1"
                                       attrs="{'invisible': [('service_type', '!=', 'turn_card')]}"/>
                                <field name="available_session_number" string="Buổi lượt còn lại"
                                       attrs="{'invisible': ['|', ('service_type', '!=', 'turn_card'), ('state', '=', 'draft')],
                                               'readonly': [('state', '!=', 'draft')]}"/>
                               
                                <!-- Thời hạn hợp đồng -->
                                <label for="valid_time"/>
                                <div>
                                    <field name="valid_time" readonly="1" class="oe_inline"/> <field
                                        name="valid_time_type" readonly="1" class="oe_inline"/>
                                </div>
                                <!-- Quà tặng -->
                                <field name="welly_gift_id"
                                       widget="many2many_tags"
                                       attrs="{'invisible': [('state', '!=', 'draft')], 'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="welly_gift_name_print" string="Quà tặng" readonly="1"
                                       attrs="{'invisible': [('state', '=', 'draft')]}"/>
                                <!-- Ngày bắt đầu và ngày kết thúc hợp đồng -->
                                <field name="date_start"
                                       attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="date_end" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <!-- Hình thức đăng ký -->
                                <field name="registration_form_id"
                                       attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                                <field name="registration_form_name_print" string="Hình thức đăng ký" readonly="1"
                                       attrs="{'invisible': [('state', '=', 'draft')]}"/>
                                <field name="is_family_service" string="Gói gia đình" attrs="{'invisible':[('is_family_service', '=', False)]}"/>
                                <field name="presenter_partner" widget="many2one"/>
                            </group>
                            <group id="first_para_right_group" class="welly_border" style="font-weight: bold;">
                                <field name="partner_id_number" readonly="1"/>
                                <field name="nationality_id" readonly="1"/>
                                <field name="phone" readonly="1"/>
                                <field name="email" readonly="1"/>
                                <br/>
                                <br/>
                                <field name="pay_amount" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="currency_id" invisible="1"/>
                                <field name="pay_amount_to_text" readonly="1"/>
                                <field name="payment_type_welly"/>
                                <field name="payment_state" readonly="1"/>
                                <field name="remaining_amount" readonly="1"/>
                                <field name="campaign_name_print" string="Chương trình" readonly="1"/>
                                
                                <field name="free_session_number" string="Buổi tập PT khuyến mãi" readonly="1"/>
                                <field name="turn_card_free_number" readonly="1"
                                       attrs="{'invisible': [('service_type', '!=', 'turn_card')]}"/>
                                <field name="family_member_qty" string="Số lượng thành viên" attrs="{'invisible':[('is_family_service', '=', False)]}"/>
                                <!-- Thời gian checkin -->
                                <label for="welly_service_checkin_time_from" string="Thời gian checkin" />
                                <div>
                                    <field name="welly_service_checkin_time_from" class="oe_inline"/>
                                    <span class="ms-2">-</span>
                                    <span class="ms-2"/>
                                    <field name="welly_service_checkin_time_to" class="oe_inline"/>
                                </div>
                            </group>
                        </group>
                        <notebook>
                            <page id="service_users_id"
                                  name="service_users"
                                  string="Người sử dụng dịch vụ">
                                <field name="partner_account_move_ids"
                                        widget="one2many_list"
                                        options="{'always_reload': True}"
                                        attrs="{'readonly':['|', ('state','in',['done','reject','cancel','closed']),('is_family_service', '=', False)]}">
                                    <tree editable="bottom">
                                        <field name="partner_id"/>
                                        <field name="partner_id_number" string="CCCD/Hộ chiếu"/>
                                        <field name="phone" string="Số điện thoại"/>
                                        <field name="email" string="Email"/>
                                        <field name="gender" string="Giới tính"/>
                                        <field name="birthdate" string="Ngày sinh"/>
                                        <field name="nationality_id" string="Quốc tịch"/>
                                        <field name="address" string="Địa chỉ"/>
                                        <field name="is_attached" string="Người đi kèm"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="CCCD" name="cccd_info">
                                <group>
                                    <field name="partner_id_number" string="Số CCCD/Hộ chiếu"/>
                                </group>
                                <group colspan="2">
                                    <group>
                                        <field name="identification_card_front" widget="identification_card_image" options="{'preview_image': 'identification_card_front', 'zoom': true}"/>
                                    </group>
                                    <group>
                                        <field name="identification_card_back" widget="identification_card_image" options="{'preview_image': 'identification_card_back', 'zoom': true}"/>
                                    </group>
                                </group>
                            </page>
                            <page id="other_contact"
                                  name="other_contact"
                                  string="Other Contact" readonly="1">
                                <group>
                                    <group class="welly_border" style="font-weight: bold;">
                                        <field name="other_contact_id"
                                               attrs="{'readonly': [('state', '!=', 'draft')],'invisible': [('state', '!=', 'draft')]}"/>
                                        <field name="other_contact_name_print" string="Liên hệ" readonly="1"
                                               attrs="{'invisible': [('state', '=', 'draft')]}"/>
                                        <field name="other_contact_gender"/>
                                        <field name="other_contact_address"/>
                                    </group>
                                    <group class="welly_border" style="font-weight: bold;">
                                        <field name="other_contact_id_number"/>
                                        <field name="other_contact_nationality_id"/>
                                        <field name="other_contact_phone"/>
                                    </group>
                                </group>
                            </page>
                            <page id="guardian"
                                  name="guardian"
                                  string="Guardian">
                                <group>
                                    <group class="welly_border" style="font-weight: bold;">
                                        <field name="guardian_id"
                                               attrs="{'readonly': [('state', '!=', 'draft')],'invisible': [('state', '!=', 'draft')]}"/>
                                        <field name="guardian_name_print" string="Liên hệ" readonly="1"
                                               attrs="{'invisible': [('state', '=', 'draft')]}"/>
                                        <field name="guardian_gender"/>
                                        <field name="guardian_address"/>
                                    </group>
                                    <group class="welly_border" style="font-weight: bold;">
                                        <field name="guardian_id_number"/>
                                        <field name="guardian_relationship"
                                               attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                        <field name="guardian_phone"/>
                                    </group>
                                </group>
                            </page>
                            <page id="internal_info" string="Thông tin nội bộ">
                                <group>
                                    <group class="welly_border" style="font-weight: bold;">
                                        <field name="marketing_staff_id" widget="many2many_tags"/>
                                        <field name="pt_staff_id" string="PT hỗ trợ" widget="many2many_tags"/>
                                    </group>
                                    <group class="welly_border" style="font-weight: bold;">
                                        <field name="membership_code" attrs="{'readonly': [('state', 'in', ('closed','cancel','reject','done'))]}"/>
                                        <field name="care_staff_id" widget="many2many_tags"/>
                                    </group>
                                </group>
                            </page>
                            <page string="Reject Reason" name="reject_reason"
                                  attrs="{'invisible': [('state', '!=', 'reject')]}">
                                <group>
                                    <field name="reject_reason"/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <sheet>
                        <group>
                            <group>
                                <field name="coo_signature" widget="signature" readonly="1"/>
                            </group>
                            <group>
                                <field name="cus_signature" widget="signature"/>
                            </group>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_ids"/>
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                    </div>
                </form>
            </field>
        </record>
        <record id="view_welly_contract_tree" model="ir.ui.view">
            <field name="name">welly.contract.tree</field>
            <field name="model">welly.contract</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name" string="Mã hợp đồng"/>
                    <field name="service_type" string="Loại dịch vụ"/>
                    <field name="sale_order_template_name_print" string="Gói dịch vụ" optional="show"/>
                    <field name="partner_name_print" string="Khách hàng đứng tên"/>
                    <field name="phone" string="SĐT"/>
                    <field name="payment_type_welly" string="Loại thanh toán" optional="show"/>
                    <field name="coach_name_print" string="Huấn luyện viên" optional="show"/>
                    <field name="session_number" string="Tổng số buổi" optional="hide"/>
                    <field name="available_session_number" string="Buổi còn lại" optional="show"/>
                    <field name="free_session_number" string="Buổi PT free" optional="show"/>
                    <field name="turn_card_free_number" string="Lượt free" optional="show"/>
                    <field name="registration_form_name_print" string="Hình thức" optional="hide"/>
                    <field name="pt_staff_id" widget="many2many_tags" string="PT hỗ trợ" optional="show"/>
                    <field name="marketing_staff_id" string="NV kinh doanh" widget="many2many_tags" optional="show"/>
                    <field name="pay_amount" string="Số tiền"  optional="show"/>
                    <field name="remaining_amount" string="Công nợ" optional="show"/>
                    <field name="activity_ids" widget="list_activity" optional="hide"/>
                    <field name="create_date" optional="show"/>
                    <field name="date_start"/>
                    <field name="date_end" readonly="1"/>
                    <field name="state" 
                        widget="badge"
                        decoration-info="state in ('draft')"
                        decoration-warning="state in ('confirm_admin', 'confirm_recep', 'sign_coo','sign_customer','waiting_active')"
                        decoration-success="state in ('activated')"
                        decoration-danger="state in ('closed','cancel','reject')"
                    />
                </tree>
            </field>
        </record>
        <record id="view_welly_contract_filter" model="ir.ui.view">
            <field name="name">welly.contract.select</field>
            <field name="model">welly.contract</field>
            <field name="arch" type="xml">
                <search string="Search Contract">
                    <field name="name"/>
                    <field name="partner_id"/>
                    <field name="partner_code"/>
                    <field name="phone"/>
                    <separator/>
                    <filter name="draft" string="Draft" domain="[('state','=','draft')]"/>
                    <filter name="admin_confirm" string="Admin xác nhận" domain="[('state', '=', 'confirm_admin')]"/>
                    <filter name="sign_coo" string="Sign COO" domain="[('state', '=', 'sign_coo')]"/>
                    <filter name="sign_customer" string="KH Ký" domain="[('state', '=', 'sign_customer')]"/>
                    <filter name="confirm_recep" string="Lễ tân xác nhận" domain="[('state', '=', 'confirm_recep')]"/>
                    <filter name="waiting_active" string="Chờ kích hoạt" domain="[('state', '=', 'waiting_active')]"/>
                    <filter name="activated" string="Activated" domain="[('state', '=', 'activated')]"/>
                    <filter name="done" string="Hoàn tất" domain="[('state', '=', 'done')]"/>
                    <filter name="reject" string="Hủy bỏ" domain="[('state', '=', 'reject')]"/>
                    <filter name="cancel" string="Đã hủy" domain="[('state', '=', 'cancel')]"/>
                    <filter name="closed" string="Đóng" domain="[('state', '=', 'closed')]"/>
                    <filter name="create_date_current_month"
                            string="Tạo trong tháng"
                            domain="[('create_date',  '=', 'current_month')]"/>
                    <filter string="Tạo trong tháng trước" name="create_date_last_month"
                            domain="[('create_date', '=', 'last_month')]"/>
                    <separator/>
                    <filter invisible="1" string="Late Activities" name="activities_overdue"
                            domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                            help="Show all records which has next action date is before today"/>
                    <filter invisible="1" string="Today Activities" name="activities_today"
                            domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                            domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Loại dịch vụ" name="service_type" context="{'group_by':'service_type'}"/>
                        <filter name="sale_order_template_id" string="Gói dịch vụ" context="{'group_by':'sale_order_template_id'}"/>
                        <separator/>
                        <filter name="marketing_staff_id" string="NV Kinh Doanh" context="{'group_by':'marketing_staff_id'}"/>
                        <filter name="pt_staff_id" string="PT Hỗ Trợ" context="{'group_by':'pt_staff_id'}"/>
                        <separator/>
                        <filter string="Khách hàng đứng tên" name="group_partner_name_print" context="{'group_by':'partner_name_print'}" />
                    </group>
                </search>
            </field>
        </record>
        <record id="action_view_welly_contract_tree_welly" model="ir.actions.act_window">
            <field name="name">Welly Contract</field>
            <field name="res_model">welly.contract</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="view_welly_contract_form"/>
            <field name="search_view_id" ref="view_welly_contract_filter"/>
            <field name="target">current</field>
            <field name="view_ids"
                   eval="[(5, 0, 0),
                            (0, 0, {'view_mode': 'tree', 'sequence': 1, 'view_id': ref('welly_base.view_welly_contract_tree')}),
                            (0, 0, {'view_mode': 'form', 'sequence': 2, 'view_id': ref('welly_base.view_welly_contract_form')}),
                        ]"/>
            <field name="context">{'date_field': 'create_date', 'date_field_label': 'Ngày tạo hợp đồng'}</field>
        </record>

        <record id="welly_contract_action_reject" model="ir.actions.server">
            <field name="name">Huỷ Hoàn Toàn</field>
            <field name="model_id" ref="welly_base.model_welly_contract"/>
            <field name="binding_model_id" ref="welly_base.model_welly_contract"/>
            <field name="binding_view_types">form</field>
            <field name="state">code</field>
            <field name="code">action = records.action_reject_contract()</field>
        </record>

        <record id="welly_contract_action_change_session" model="ir.actions.server">
            <field name="name">Cộng/Trừ buổi</field>
            <field name="model_id" ref="welly_base.model_welly_contract"/>
            <field name="binding_model_id" ref="welly_base.model_welly_contract"/>
            <field name="binding_view_types">form</field>
            <field name="state">code</field>
            <field name="code">action = records.action_change_session_contract()</field>
        </record>

<!--        report-->
        <record id="view_welly_contract_search_report" model="ir.ui.view">
            <field name="name">view_welly_contract_search_report</field>
            <field name="model">welly.contract</field>
            <field name="priority">32</field>
            <field name="arch" type="xml">
                <search string="Báo cáo">
                    <filter name="activated" string="Đang hoạt động" domain="[('state', '=', 'activated')]"/>
                    <filter name="contract_expire_in_30_days"
                        string="Hết hạn trong 30 ngày tới"
                        domain="[('date_end', '&lt;=', (context_today() + datetime.timedelta(days=30)))]"/>
                    <group expand="1" string="Group By">
                        <filter string="Khách hàng đứng tên" name="group_partner_name_print" context="{'group_by':'partner_name_print'}" />
                    </group>
                </search>
            </field>
        </record>

        <record id="action_report_expiring_membership" model="ir.actions.act_window">
            <field name="name">Thống kê hội viên sắp hết hạn</field>
            <field name="res_model">welly.contract</field>
            <field name="view_mode">tree,form</field>
            <!-- Gán search view bạn đã tạo -->
            <field name="view_id" ref="view_welly_contract_tree"/>
            <field name="search_view_id" ref="view_welly_contract_search_report"/>
            <field name="domain">[('state', '=', 'activated'), ]
            </field>
            <!-- Điều kiện lọc (mặc định ngày tạo trong tháng) -->
            <field name="context">
                {
                    'search_default_contract_expire_in_30_days': 1,
                    'search_default_group_partner_name_print': 1,
                    'date_field': 'date_end',
                    'date_field_type': 'date',
                    'date_field_label': 'Ngày hết hạn'
                }
            </field>
        </record>

        <!--Công việc của tôi-->
        <record id="view_welly_contract_my_activity_tree" model="ir.ui.view">
            <field name="name">welly.contract.my.activity.tree</field>
            <field name="model">welly.contract</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="partner_id"/>
                    <field name="phone"/>
                    <field name="activity_ids" widget="list_activity" string="Tên công việc"/>
                    <field name="activity_type_id" string="Loại công việc"/>
                    <field name="my_activity_date_deadline" string="Hạn chót" widget="remaining_days" options="{'allow_order': '1'}"/>
                    <field name="activity_user_id"/>
                </tree>
            </field>
        </record>

        <record id="welly_contract_filter_my_activity" model="ir.ui.view">
            <field name="name">welly.contract.my.activity.filter</field>
            <field name="model">welly.contract</field>
            <field name="arch" type="xml">
                <search>
                    <filter string="Công việc của tôi"
                            name="my_activity"
                            domain="[('activity_user_id', '=', uid)]"/>
                </search>
            </field>
        </record>

        <record id="action_report_welly_contract_my_tasks" model="ir.actions.act_window">
            <field name="name">Công việc của tôi - Hợp đồng</field>
            <field name="res_model">welly.contract</field>
            <field name="view_mode">tree,form,activity</field>
            <field name="view_id" ref="view_welly_contract_my_activity_tree"/>
            <field name="search_view_id" ref="welly_base.welly_contract_filter_my_activity"/>
            <field name="domain">[('activity_ids','!=',False)]</field>
            <field name="context">
                {
                    "search_default_my_activity": 1
                }
            </field>
        </record>
    </data>
</odoo>
