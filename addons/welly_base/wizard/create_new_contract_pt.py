from odoo import models, fields, api, _
from odoo.exceptions import UserError


class CreateContractWizard(models.TransientModel):
    _name = 'create.pt.contract.wizard'
    _description = 'Create PT Contract Wizard'

    partner_account_move_ids = fields.Many2many('welly.partner.account.move', string='Partner', required=True)

    partner_account_move_ids_domain = fields.Many2many(
        'welly.partner.account.move',
        string='Partner domain',
        store=False,
        readonly=True,
    )

    def create_contract(self):
        move = self.env['account.move'].browse(self.env.context.get('active_ids'))
        has_exist_contract = self.env.context.get('has_exist_contract')
        total_session_number = move.session_number
        available_session_number = total_session_number
        date_start = move.date_start
        date_end = move.date_end
        is_installment_activated = False
        # Hóa đơn có loại thanh toán là trả góp
        if move.payment_type_welly == 'installment':
            is_installment_activated = True
            welly_partner_account_move_ids = move.partner_account_move_ids.ids
            # Danh sách các payment line có trạng thái posted, sắp xếp tăng dần theo ngày thanh toán
            welly_payment_lines = self.env['welly.payment.line'].search([
                ('welly_account_move_id', '=', move.id),
                ('payment_state', '=', 'posted')
            ])
            # Sắp xếp tăng dần theo ngày thanh toán và id tăng dần
            welly_payment_lines = sorted(welly_payment_lines, key=lambda x: (x.date, x.id))
            # Số buổi có hiệu lực của HĐ trả góp PT bằng tổng số buổi được sử dụng của các payment line có trạng thái posted
            available_session_number = sum(x.allow_session_number for x in welly_payment_lines)
            # Hóa đơn có trạng thái thanh toán một phần
            if move.payment_state == 'partial':
                date_start = welly_payment_lines[0].effective_date
                date_end = welly_payment_lines[-1].expired_date
            else:
            # Hóa đơn có trạng thái thanh toán đủ
                date_end = move.date_end
                date_start = move.date_start
        else:
            welly_partner_account_move_ids = self.partner_account_move_ids.ids
        if has_exist_contract:
            available_session_number = 0
        view_id = self.env.ref('welly_base.view_welly_contract_form').id
        if not self.partner_account_move_ids:
            raise UserError(_("Hãy chọn ít nhất một người hưởng dịch vụ trong hợp đồng."))

        return {
            'type': 'ir.actions.act_window',
            'name': 'Create New Contract',
            'res_model': 'welly.contract',
            'view_mode': 'form',
            'view_id': view_id,
            'target': 'current',
            'context': {
                'default_welly_invoice_id': move.id,
                'default_partner_id': move.partner_id.id,
                'default_partner_account_move_ids': [(6, 0, welly_partner_account_move_ids)],
                'default_service_type': move.service_type,
                'default_welly_location_many2_many': move.welly_location_many2_many.ids,
                'default_sale_order_template_id': move.sale_order_template_id.id,
                'default_other_contact_id': move.other_contact_id.id,
                'default_guardian_id': move.guardian_id.id,
                'default_guardian_relationship': move.guardian_relationship,
                'default_date_start': date_start,
                'default_date_end': date_end,
                'default_session_number': total_session_number,
                'default_available_session_number': available_session_number,
                'default_is_installment_activated': is_installment_activated,
                'default_create_user_id': self.env.user.id,
                'default_exercise_form_id': move.exercise_form_id.id,
                'default_welly_gift_id': [(6, 0, move.welly_gift_id.ids)],
                'form_view_initial_mode': 'edit'
            }
        }
