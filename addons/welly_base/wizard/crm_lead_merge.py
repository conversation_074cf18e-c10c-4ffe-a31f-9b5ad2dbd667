# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
import logging

import psycopg2

from odoo import api, fields, models, Command, _
from odoo.exceptions import UserError
from odoo.tools import mute_logger

_logger = logging.getLogger(__name__)


class WellyCRM(models.Model):
    _inherit = 'crm.lead'

    def name_get(self):
        names = super(WellyCRM, self).name_get()
        if self._context.get('show_db_id'):
            # Ghép ID vào cuối tên hiển thị
            names = [(lead_id, f"{display_name} ({lead_id})") for lead_id, display_name in names]
        return names


class MergeCrmLead(models.TransientModel):
    _name = 'crm.lead.merge'
    _description = 'Hợp nhất Ticket'

    @api.model
    def default_get(self, fields):
        res = super(MergeCrmLead, self).default_get(fields)
        active_ids = self.env.context.get('active_ids')
        if self.env.context.get('active_model') == 'crm.lead' and active_ids:
            leads = self.env['crm.lead'].browse(active_ids)
            if len(leads) > 2:
                raise UserError("Bạn chỉ được chọn tối đa 2 Ticket để hợp nhất.")
            if len(leads) < 2:
                raise UserError("Bạn phải chọn đủ 2 Ticket để hợp nhất.")
            if 'opportunity_ids' in fields:
                res['opportunity_ids'] = [Command.set(active_ids)]
            if 'dst_lead' in fields:
                # Tìm lead có ngày tạo mới nhất
                latest_lead = max(leads,
                                  key=lambda l: l.create_date or fields.Datetime.from_string('1970-01-01 00:00:00'))
                res['dst_lead'] = latest_lead.id
        return res

    opportunity_ids = fields.Many2many('crm.lead', string='Chọn Lead/Cơ hội', context={'active_test': False})
    dst_lead = fields.Many2one('crm.lead', string='Ticket đích',
                               help='Là ticket được hợp nhất các thông tin của ticket nguồn')
    src_lead = fields.Many2one('crm.lead', string='Ticket nguồn',
                               help='Ticket nguồn sẽ được chuyển dữ liệu sang ticket đích và bị unlink')

    @api.onchange('dst_lead')
    def chose_src_lead(self):
        if self.dst_lead:
            ids = self.opportunity_ids.ids
            other_ids = [i for i in ids if i != self.dst_lead.id]
            self.src_lead = other_ids[0] if other_ids else False

    @api.onchange('opportunity_ids')
    def check_opportunity_ids(self):
        if self.opportunity_ids:
            partner_ids = self.opportunity_ids.mapped('partner_id.id')
            if len(set(partner_ids)) > 1:
                raise UserError("Bạn đang thao tác hợp nhất ticket của 2 khách hàng khác nhau, vui lòng kiểm tra lại.")
            dst_id = self.dst_lead.id
            opportunity_ids = self.opportunity_ids.ids
            # Nếu ticket đích không nằm trong danh sách đang chọn → reset
            if dst_id and dst_id not in opportunity_ids:
                self.dst_lead = False

    def _get_fk_on(self, table):
        """ return a list of many2one relation with the given table.
            :param table : the name of the sql table to return relations
            :returns a list of tuple 'table name', 'column name'.
        """
        query = """
            SELECT cl1.relname as table, att1.attname as column
            FROM pg_constraint as con, pg_class as cl1, pg_class as cl2, pg_attribute as att1, pg_attribute as att2
            WHERE con.conrelid = cl1.oid
                AND con.confrelid = cl2.oid
                AND array_lower(con.conkey, 1) = 1
                AND con.conkey[1] = att1.attnum
                AND att1.attrelid = cl1.oid
                AND cl2.relname = %s
                AND att2.attname = 'id'
                AND array_lower(con.confkey, 1) = 1
                AND con.confkey[1] = att2.attnum
                AND att2.attrelid = cl2.oid
                AND con.contype = 'f'
        """
        self._cr.execute(query, (table,))
        return self._cr.fetchall()

    @api.model
    def _update_foreign_keys(self, src_lead, dst_lead):
        _logger.info('[LEAD MERGE] Updating FK using ORM approach')

        # Tìm tất cả models có field reference đến crm.lead
        all_models = self.env.registry.keys()
        updated_models = []

        for model_name in all_models:
            try:
                if model_name in ['crm.lead.merge']:  # Skip wizard models
                    continue

                Model = self.env[model_name]
                if Model._abstract or Model._transient:
                    continue

                # Tìm fields Many2one reference đến crm.lead
                lead_fields = []
                for field_name, field in Model._fields.items():
                    if (isinstance(field, fields.Many2one) and
                            field.comodel_name == 'crm.lead'):
                        lead_fields.append(field_name)

                if not lead_fields:
                    continue

                for field_name in lead_fields:
                    domain = [(field_name, '=', src_lead)]
                    records = Model.search(domain)

                    if records:
                        _logger.info('[LEAD MERGE ORM] Updating %d records in %s.%s',
                                     len(records), model_name, field_name)
                        try:
                            records.write({field_name: dst_lead})
                            updated_models.append(f"{model_name}.{field_name}")
                        except Exception as e:
                            _logger.warning('[LEAD MERGE ORM] Failed to update %s.%s: %s',
                                            model_name, field_name, e)
                            # Try to delete conflicting records
                            records.unlink()

            except Exception as e:
                _logger.debug('[LEAD MERGE ORM] Skip model %s: %s', model_name, e)
                continue

        _logger.info('[LEAD MERGE ORM] Updated models: %s', updated_models)

    @api.model
    def _update_reference_fields(self, src_lead, dst_lead):
        _logger.info('[LEAD MERGE] Updating reference fields from %s → %s', src_lead.id, dst_lead.id)

        src_ref = f'crm.lead,{src_lead.id}'
        dst_ref = f'crm.lead,{dst_lead.id}'

        reference_fields = self.env['ir.model.fields'].sudo().search([
            ('ttype', '=', 'reference')
        ])

        for ref_field in reference_fields:
            model_name = ref_field.model
            field_name = ref_field.name
            try:
                Model = self.env[model_name]
                field = Model._fields[ref_field.name]
                if Model._abstract or Model._transient or field.compute is not None:
                    continue
                if not hasattr(Model, field_name):
                    continue
            except Exception:
                continue
            records = Model.sudo().search([(field_name, '=', src_ref)])
            try:
                records.sudo.write({field_name: dst_ref})
            except Exception as e:
                _logger.warning('[LEAD MERGE] Error writing ref field %s.%s: %s', model_name, field_name, e)
                # Optionally delete or log
                records.sudo().unlink()
        self.env.flush_all()

    def _merge_field_values(self, src_lead, dst_lead):
        """Cập nhật các thông tin được chỉ định từ nguồn sang đích với điều kiện đích chưa tồn tại dữ liệu"""

        fields_to_merge = [
            'partner_id',
            'data_type',
            'source_id',
            'service_id',
            'user_id',
            'user_pt_id',
            'priority',
            'expected_revenue',
            'probability',
            'date_deadline'
        ]

        vals_to_write = {}
        for field_name in fields_to_merge:
            dst_value = dst_lead[field_name]
            src_value = src_lead[field_name]
            if not dst_value and src_value:
                field = dst_lead._fields[field_name]
                if isinstance(field, fields.Many2one):
                    vals_to_write[field_name] = src_value.id
                else:
                    vals_to_write[field_name] = src_value

        # Merge tag_ids (Many2many): thêm tag từ nguồn vào đích
        if src_lead.tag_ids:
            dst_lead.tag_ids |= src_lead.tag_ids

        # Merge description (Text): gộp nếu cả 2 có
        if src_lead.description:
            if dst_lead.description:
                combined_description = f"{dst_lead.description.strip()}\n---\n{src_lead.description.strip()}"
            else:
                combined_description = src_lead.description.strip()
            vals_to_write['description'] = combined_description

        if vals_to_write:
            _logger.debug('[LEAD MERGE] Fields updated on dst_lead %s: %s', dst_lead.id, vals_to_write)
            dst_lead.write(vals_to_write)

    def _merge_notes_tags(self, src_lead, dst_lead):
        _logger.info('[LEAD MERGE] Merging notes, tags, activities, attachments from %s → %s', src_lead.id, dst_lead.id)

        # 1. Merge tag_ids (Many2many)
        dst_lead.tag_ids |= src_lead.tag_ids

        # 2. Chuyển mail.message (ghi chú, lịch sử trao đổi)
        messages = self.env['mail.message'].sudo().search([
            ('model', '=', 'crm.lead'),
            ('res_id', '=', src_lead.id)
        ])
        messages.write({'res_id': dst_lead.id})

        # 3. Chuyển mail.activity
        activities = self.env['mail.activity'].sudo().search([
            ('res_model', '=', 'crm.lead'),
            ('res_id', '=', src_lead.id)
        ])
        activities.write({'res_id': dst_lead.id})

        # 4. Chuyển đính kèm (ir.attachment)
        attachments = self.env['ir.attachment'].sudo().search([
            ('res_model', '=', 'crm.lead'),
            ('res_id', '=', src_lead.id)
        ])
        attachments.write({'res_id': dst_lead.id})

        # 5. Log lại thông tin hợp nhất (tùy chọn)
        dst_lead.message_post(
            body=_("Hợp nhất từ ticket <a href='#' data-oe-model='crm.lead' data-oe-id='%d'>%s</a>.") % (
            src_lead.id, src_lead.display_name),
            subtype_xmlid='mail.mt_note',
        )

    def action_merge(self):
        if self.opportunity_ids:
            if len(self.opportunity_ids) > 2:
                raise UserError("Bạn chỉ được chọn tối đa 2 Ticket để hợp nhất.")
            if len(self.opportunity_ids) < 2:
                raise UserError("Bạn phải chọn đủ 2 Ticket để hợp nhất.")
            partner_ids = self.opportunity_ids.mapped('partner_id.id')
            if len(set(partner_ids)) > 1:
                raise UserError("Bạn đang thao tác hợp nhất ticket của 2 khách hàng khác nhau, vui lòng kiểm tra lại.")
        else:
            raise UserError("Bạn phải chọn đủ 2 Ticket để hợp nhất.")
        if not self.dst_lead:
            raise UserError("Bạn cần chọn ticket đích để hợp nhất.")

        with self.env.cr.savepoint():
            try:
                _logger.info('[LEAD MERGE] Bắt đầu hợp nhất lead %s → %s', self.src_lead.id, self.dst_lead.id)
                self._update_foreign_keys(self.src_lead.id, self.dst_lead.id)
                self._update_reference_fields(self.src_lead, self.dst_lead)
                self._merge_field_values(self.src_lead, self.dst_lead)
                self._merge_notes_tags(self.src_lead, self.dst_lead)
                # Xoá bản ghi nguồn
                self.src_lead.sudo().with_context({'forced_unlink': True}).unlink()
                self.env.invalidate_all()

                _logger.info('[LEAD MERGE] Hoàn thành hợp nhất lead')
            except Exception as e:
                _logger.error('[LEAD MERGE] Lỗi trong quá trình hợp nhất: %s', str(e))
                raise UserError(f"Có lỗi xảy ra khi hợp nhất: {str(e)}")
        return self.dst_lead.redirect_lead_opportunity_view()
