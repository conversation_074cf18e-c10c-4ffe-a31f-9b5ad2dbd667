from datetime import datetime, timedelta
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from ..fields import selection
import logging
import io
import base64
import xlsxwriter
import pytz
from pytz import timezone
from dateutil.relativedelta import relativedelta

_logger = logging.getLogger(__name__)

class CustomCalendarAttendee(models.Model):
    _inherit = 'calendar.attendee'

    STATE_SELECTION = [
        ('needsAction', 'Cần có hành động'),
        ('tentative', 'Chờ KH xác nhận'),
        ('declined', 'Hủy'),
        ('accepted', 'Đã xác nhận'),
        ('cheat_commission', 'Hủy com show'),
        ('refund_session', '<PERSON><PERSON>n buổi tập'),
        ('during_practice', '<PERSON>ang tập'),
        ('done', '<PERSON><PERSON><PERSON> thành ca tập'),
        ('await_pt_confirm', 'Chờ PT xác nhận'),
        ('partner_skip_session', '<PERSON>h<PERSON><PERSON> bỏ tập'),
    ]

    TYPE_CHECKIN = [
        ('reception_checkin', '<PERSON><PERSON> tân checkin'),
        ('customer_checkin', '<PERSON>h<PERSON><PERSON> hàng checkin')
    ]

    confirm_commission = fields.<PERSON>olean(string='Được tính COM', default=False, tracking=True)

    time_checkin = fields.Datetime(string='Thời gian checkin')

    type_checkin = fields.Selection(string='Kiểu checkin', selection=TYPE_CHECKIN)

    currency_id = fields.Many2one('res.currency', string='Currency', default=lambda self: self.env.company.currency_id)

    state = fields.Selection(STATE_SELECTION, string='Trạng thái', readonly=True, default='needsAction')

    previous_state = fields.Selection(STATE_SELECTION, string='Trạng thái cũ', default=False)

    is_checked_in = fields.Boolean('Đã checkin', default=False, readonly=True)
    is_past_booking = fields.Boolean(string='Là lịch quá khứ', related='event_id.is_past_booking')

    main_session_number = fields.Char('Buổi chính số')
    gift_session_number = fields.Char('Buổi tặng số')

    note = fields.Char('Ghi chú')
    declined_note = fields.Char('Ghi chú từ chối')
    session_number = fields.Integer('Buổi tập số')
    contract_id = fields.Many2one('welly.contract', string='Số hợp đồng')
    sale_order_template_name_print = fields.Char(related='contract_id.sale_order_template_name_print', string='Tên gói dịch vụ')
    # Hoa hồng buổi tặng dùng để tính toán
    gift_session_commission = fields.Monetary(string='Hoa hồng buổi tặng', currency_field='currency_id')

    available_session_number = fields.Integer(string='Số buổi còn lại', compute='_compute_available_session_number', store=True)

    # Thêm trường lưu lý do quên checkin
    miss_checkin_reason_id = fields.Many2one('miss.checkin.reason', string='Lý do quên checkin')

    @api.depends('contract_id')
    def _compute_available_session_number(self):
        for attendee in self:
            if attendee.contract_id and attendee.contract_id.available_session_number:
                attendee.available_session_number = attendee.contract_id.available_session_number
            else:
                attendee.available_session_number = 0

    partner_action = fields.Many2one('res.partner', string='Người thực hiện', ondelete='set null')
    refund_note = fields.Char('Ghi chú hoàn buổi tập', readonly=True)
    is_refunded = fields.Boolean('Đã hoàn buổi', default=False, readonly=True)
    event_type = fields.Selection(related='event_id.event_type', readonly=True, string='Loại lịch', invisible=True)
    event_state = fields.Selection(related='event_id.state', readonly=True, string='Trạng thái lịch', invisible=True)
    # Mục đích ẩn hiện button Quên checkin
    is_today_utc7 = fields.Boolean(
        string="Là hôm nay (UTC+7)",
        compute="_compute_is_today_utc7",
        store=False
    )

    @api.depends('event_id.start')
    def _compute_is_today_utc7(self):
        for record in self:
            event = record.event_id
            if event and event.start and isinstance(event.start, datetime):
                utc7_tz = pytz.timezone('Asia/Ho_Chi_Minh')
                start_date_utc7 = event.start.astimezone(utc7_tz).date()
                today_utc7 = datetime.now(utc7_tz).date()
                # So sánh ngày
                record.is_today_utc7 = (start_date_utc7 == today_utc7)
            else:
                record.is_today_utc7 = False
    event_is_full_participants = fields.Boolean(related='event_id.is_full_participants', readonly=True, string='Đã đủ số lượng người', invisible=True)
    is_notified_full_participants = fields.Boolean('Đã được thông báo lớp đã đầy', default=False, readonly=True)
    is_deducted_session = fields.Boolean('Đã trừ buổi', default=False, readonly=True)
    # Field related từ event_id
    categ_ids = fields.Many2many('calendar.event.type', related='event_id.categ_ids', string='Thẻ')

    valid_contract_ids = fields.Many2many('welly.contract', string="Hợp đồng hợp lệ",
                                          compute='_compute_valid_contract_ids', store=True)

    @api.depends('event_id.location_id', 'event_id.exercise_id', 'event_id.service_id',
                 'event_id.partner_id', 'event_id.start', 'event_id.stop', 'event_id.is_merge_class')
    def _compute_valid_contract_ids(self):
        for attendee in self:
            event = attendee.event_id
            if not event.start or not event.stop or not event.exercise_id or not event.service_id:
                attendee.valid_contract_ids = [(6, 0, [])]
            else:
                start_str = (event.start + timedelta(hours=7)).strftime("%H:%M")
                stop_str = (event.stop + timedelta(hours=7)).strftime("%H:%M")
                start = float(start_str[:2]) + float(start_str[3:5]) / 60
                stop = float(stop_str[:2]) + float(stop_str[3:5]) / 60
                if event.event_type == 'pt':
                    domain = [
                        ('service_type', 'in', ['member', 'pt']),
                        ('state', '=', 'activated'),
                        ('partner_account_ids', 'in', [attendee.partner_id.id]),
                        '|',
                        ('available_session_number', '>', 0),
                        ('free_session_number', '>', 0),
                        ('welly_service_ids', 'in', [event.service_id.id]),
                        ('date_end', '>=', event.start),
                        ('checkin_time_from', '<=', start),
                        ('checkin_time_to', '>=', stop)
                    ]
                    # Chỉ check hình thức tập khi không phải lịch gộp lớp hoặc gộp lớp khác hình thức
                    if not (event.is_merge_class and event.calendar_config_id.merge_class_type == 'different_exercise'):
                        domain += [
                            '|',
                            '&',
                            ('service_type', '=', 'pt'),
                            ('exercise_form_id', '=', event.exercise_id.id),
                            '&',
                            ('service_type', '=', 'member'),
                            (1, '=', 1),
                        ]
                    if event.location_id:
                        domain.append(('welly_location_many2_many', 'in', [event.location_id.id]))
                    contracts = self.env['welly.contract'].search(domain)
                    attendee.valid_contract_ids = [(6, 0, contracts.ids)]
                else:
                    attendee.valid_contract_ids = [(6, 0, [])]

    # Bổ sung trường cho báo cáo PT
    date = fields.Datetime(string='Ngày', related='event_id.start', store=True)

    pt_id = fields.Many2one(comodel_name='res.users', string='Huấn luyện viên', compute='_compute_pt', store=True)

    @api.depends('event_id.pt_id', 'event_id.pt_id_substitute')
    def _compute_pt(self):
        for attendee in self:
            event = attendee.event_id
            attendee.pt_id = event.pt_id_substitute if event.pt_id_substitute else event.pt_id

    exercise_id = fields.Many2one(comodel_name='welly.exercise.form', string='Hình thức tập', related='event_id.exercise_id')

    pay_amount = fields.Monetary(string='Giá gói PT', related='contract_id.pay_amount', currency_field='currency_id', store=True)

    main_session_char = fields.Char(string='Buổi chính', compute='_compute_main_session_char')

    @api.depends('contract_id')
    def _compute_main_session_char(self):
        for attendee in self:
            contract = attendee.contract_id
            main_session_char = '0/0'
            if contract:
                session_number = contract.session_number or 0
                main_session_number = int(attendee.main_session_number) if attendee.main_session_number else 0
                main_session_char = f'{main_session_number}/{session_number}'
            attendee.main_session_char = main_session_char

    gift_session_char = fields.Char(string='Buổi tặng', compute='_compute_gift_session_char')

    @api.depends('contract_id')
    def _compute_gift_session_char(self):
        for attendee in self:
            contract = attendee.contract_id
            gift_session_char = '0/0'
            if contract:
                details = contract.detail_ids
                free_session_number = 0
                if details:
                    for detail in details:
                        free_session_number += detail.gift_session_number_created
                gift_session_number = int(attendee.gift_session_number) if attendee.gift_session_number else 0
                gift_session_char = f'{gift_session_number}/{free_session_number}'
            attendee.gift_session_char = gift_session_char

    pay_amount_per_session_number = fields.Monetary(string='Phân bổ', currency_field='currency_id',
                                                    compute='_compute_pay_amount_per_session_number')

    @api.depends('contract_id', 'gift_session_number')
    def _compute_pay_amount_per_session_number(self):
        for attendee in self:
            contract = attendee.contract_id
            pay_amount_per_session_number = 0
            if contract:
                # Nếu là buổi tặng, Phân bổ = 0
                if attendee.gift_session_number:
                    attendee.pay_amount_per_session_number = 0
                    continue
                session_number = contract.session_number
                if session_number and session_number > 0:
                    pay_amount_per_session_number = attendee.pay_amount/session_number
            attendee.pay_amount_per_session_number = pay_amount_per_session_number

    event_notes = fields.Text(string='Ghi chú lịch', related='event_id.notes')

    @api.constrains('state')
    def _check_attendee_state(self):
        for attendee in self:
            event = attendee.event_id
            if event.is_merge_class:
                continue
            if event.event_type == 'pt' and event.exercise_id:
                number_of_participants = len(event.partner_ids)
                number_of_declined = len(event.attendee_ids.filtered(lambda a: a.state == 'declined'))
                if number_of_participants - number_of_declined > event.exercise_id.limit_participants:
                    raise UserError(
                        f'Số lượng người tham gia có trạng thái khác hủy không được vượt quá {event.exercise_id.limit_participants}.')

    active = fields.Boolean(string='Active', default=True)

    @api.model_create_multi
    def create(self, vals_list):
        user = self.env.user
        for values in vals_list:
            # Tìm sự kiện dựa trên ID
            event = self.env['calendar.event'].search([('id', '=', values['event_id'])])

            if event.event_type == 'pt':
                contract_id = None
                if not 'contract_id' in values:
                    # Tìm danh sách hợp đồng phù hợp và chọn hợp đồng đầu tiên theo danh sách sắp xếp
                    contracts, show_f_member = self._find_appropriate_contracts(event, values['partner_id'])
                    valid_contracts = []
                    for contract in contracts:
                        # Tính toán số buổi có thể đặt cho từng hợp đồng
                        booked_events = self.env['calendar.event'].search([
                            ('event_type', '=', 'pt'),
                            ('state', 'in', ['draft']),
                            ('attendee_ids.partner_id', 'in', [values['partner_id']]),
                        ])

                        count_booked_events = sum(
                            1 for booked_event in booked_events
                            for attendee in booked_event.attendee_ids
                            if attendee.partner_id.id == values['partner_id'] and attendee.state != 'declined' and attendee.contract_id == contract
                        )

                        available_session_number = contract.available_session_number + contract.free_session_number
                        possible_sessions = available_session_number - count_booked_events

                        # Kiểm tra điều kiện để hợp đồng được coi là hợp lệ
                        if possible_sessions > 0 and not (contract.service_type in ['pt', 'turn_card'] and event.service_id not in contract.welly_service_ids):
                            valid_contracts.append(contract)
                    if valid_contracts:
                        contract_id = self._select_valid_contract(contracts=valid_contracts, show_f_member=show_f_member)
                else:
                    contract_id = self.env['welly.contract'].sudo().browse(int(values['contract_id']))

                if contract_id:
                    # Tính số buổi đã đặt thành công của khách hàng với hợp đồng đã chọn là số buổi có trạng thái draft, attendee có partner_id có trạng thái khác declined
                    booked_events= self.env['calendar.event'].search([
                        ('event_type', '=', 'pt'),
                        ('state', 'in', ['draft']),
                        ('attendee_ids.partner_id', 'in', [values['partner_id']]),
                    ])
                    count_booked_events = 0
                    for booked_event in booked_events:
                        for attendee in booked_event.attendee_ids:
                            if attendee.partner_id.id == values['partner_id'] and attendee.state != 'declined' and attendee.contract_id == contract_id:
                                count_booked_events += 1
                                break
                    total_ss = contract_id.session_number + contract_id.total_free_session_number
                    available_session_number = contract_id.available_session_number + contract_id.free_session_number


                    # Cập nhật thông tin hợp đồng và buổi số cho attendee
                    values['contract_id'] = contract_id.id
                    values['session_number'] = total_ss - available_session_number + 1
                    values['available_session_number'] = contract_id.available_session_number

                    # Số buổi có thể đặt lịch
                    possible_sessions = available_session_number - count_booked_events
                    partner = self.env['res.partner'].browse(values.get('partner_id'))

                    # Kiểm tra số buổi có thể đặt lịch
                    if possible_sessions <= 0:
                        raise ValidationError(f"Khách hàng {partner.name} có hợp đồng {contract_id.name} đã book đủ số buổi trên hợp đồng, không thể tiếp tục đặt lịch")

                    # Check dịch vụ trên lịch và trên hợp đồng có trùng nhau không nếu đó là hợp đồng pt hoặc turn card
                    if contract_id.service_type in ['pt', 'turn_card'] and event.service_id not in contract_id.welly_service_ids:
                        raise ValidationError(f"Hợp đồng khách hàng {partner.name} có dịch vụ khác dịch vụ của lịch, không thể đặt lịch.")

                pt = event.pt_id_substitute if event.pt_id_substitute else event.pt_id
                if user == pt:
                    values['state'] = 'tentative'
                
                # Trường hợp event là lớp ghép và đang trong trạng thái đang tập
                if event.is_merge_class and event.state == 'during_practice':
                    values['state'] = 'accepted'

            if 'state' not in values and values.get('partner_id') == self.env.user.partner_id.id:
                values['state'] = 'accepted'

            if not values.get("email") and values.get("common_name"):
                common_nameval = values.get("common_name").split(':')
                email = [x for x in common_nameval if '@' in x]
                values['email'] = email[0] if email else ''
                values['common_name'] = values.get("common_name")

        attendees = super().create(vals_list)
        attendees._subscribe_partner()
        for attendee in attendees:
            attendee.event_id._compute_event_state()
        return attendees

    @api.model
    def _find_appropriate_contracts(self, event, partner_id):
        # Show F
        show_f_type = self.env['calendar.event.type'].search([('name', '=', 'Show F')], limit=1)
        show_f_member = True
        start_str = (event.start + timedelta(hours=7)).strftime('%H:%M')
        stop_str = (event.stop + timedelta(hours=7)).strftime('%H:%M')
        start = float(start_str[:2]) + float(start_str[3:5]) / 60
        stop = float(stop_str[:2]) + float(stop_str[3:5]) / 60
        if event.categ_ids and show_f_type and show_f_type in event.categ_ids:
            # Show F + Member
            domain = [
                ('service_type', '=', 'member'),
                ('state', '=', 'activated'),
                ('partner_account_ids', 'in', [partner_id]),
                ('free_session_number', '>', 0),
                ('date_end', '>=', event.start),
                ('checkin_time_from', '<=', start),
                ('checkin_time_to', '>=', stop)
            ]
            if event.location_id:
                domain.append(('welly_location_many2_many', 'in', [event.location_id.id]))
            contracts = self.env['welly.contract'].search(domain)
            # Show F + PT
            if not contracts:
                show_f_member = False
                # Nếu không có hợp đồng member "Show F", tìm hợp đồng PT
                domain = [
                    ('service_type', '=', 'pt'),
                    ('state', '=', 'activated'),
                    ('partner_account_ids', 'in', [partner_id]),
                    ('free_session_number', '>', 0),
                    ('welly_service_ids', 'in', [event.service_id.id]),
                    ('date_end', '>=', event.start),
                    ('checkin_time_from', '<=', start),
                    ('checkin_time_to', '>=', stop)
                ]
                if event.location_id:
                    domain.append(('welly_location_many2_many', 'in', [event.location_id.id]))
                if not event.is_merge_class:
                    domain.append(('exercise_form_id', '=', event.exercise_id.id))
                # Thêm điều kiện coach_id nếu có
                if event.pt_id:
                    # Kiểm tra có hợp đồng nào thỏa mãn điều kiện với coach_id
                    contracts_with_coach = self.env['welly.contract'].search(
                        domain + [('coach_id', '=', event.pt_id.id)])
                    if contracts_with_coach:
                        contracts = contracts_with_coach
                    else:
                        contracts = self.env['welly.contract'].search(domain)
        else:
            show_f_member = False
            # Not Show F lấy ra hợp đồng PT và hợp đồng member
            domain = [
                ('service_type', 'in', ['pt', 'member']),
                ('state', '=', 'activated'),
                ('partner_account_ids', 'in', [partner_id]),
                '|',
                ('available_session_number', '>', 0),
                ('free_session_number', '>', 0),
                ('welly_service_ids', 'in', [event.service_id.id]),
                ('date_end', '>=', event.start),
                ('checkin_time_from', '<=', start),
                ('checkin_time_to', '>=', stop)
            ]
            if event.location_id:
                domain.append(('welly_location_many2_many', 'in', [event.location_id.id]))
            # Chỉ check hình thức tập khi không phải lịch gộp lớp hoặc gộp lớp khác hình thức
            if not (event.is_merge_class and event.calendar_config_id.merge_class_type == 'different_exercise'):
                domain += [
                    '|',
                    '&',
                    ('service_type', '=', 'pt'),
                    ('exercise_form_id', '=', event.exercise_id.id),
                    '&',
                    ('service_type', '=', 'member'),
                    (1, '=', 1),
                ]
            contracts = None
            # Thêm điều kiện coach_id nếu có
            if event.pt_id:
                # Kiểm tra có hợp đồng nào thỏa mãn điều kiện với coach_id
                contracts_with_coach = self.env['welly.contract'].search(
                    domain + [('coach_id', '=', event.pt_id.id)])
                if contracts_with_coach:
                    contracts = contracts_with_coach
            if not contracts:
                contracts = self.env['welly.contract'].search(domain)
        return contracts, show_f_member

    def _select_valid_contract(self, contracts, show_f_member):
        # Nếu show_f + member, lấy hợp đồng có buổi tập miễn phí lớn nhất, ngày hết hạn nhỏ nhất, ngày kích hoạt nhỏ nhất và ID nhỏ nhất
        if show_f_member:
            valid_contract = sorted(
                contracts,
                key=lambda c: (
                    -c.free_session_number,  # Sắp xếp giảm dần theo số buổi tập miễn phí
                    c.date_end,  # Sắp xếp tăng dần theo ngày hết hạn
                    c.date_start,  # Sắp xếp tăng dần theo ngày kích hoạt
                    c.id  # Sắp xếp ID tăng dần
                )
            )[0]
        else:
            valid_contract = sorted(
                contracts,
                key=lambda c: (
                    c.date_end,  # Sắp xếp tăng dần theo ngày hết hạn
                    c.date_start,  # Sắp xếp tăng dần theo ngày kích hoạt
                    c.id  # Sắp xếp ID tăng dần
                )
            )[0]

        return valid_contract

    def write(self, val):
        user_impacting = self.env.user
        state = ''
        user_action = ''
        type_checkin = ''
        if 'state' in val:
            self.previous_state = self.state
        if 'state' in val and val['state'] != 'refund_session':
            state = dict(self.STATE_SELECTION).get(val['state'])
        if 'is_checked_in' in val and val['is_checked_in']:
            state = 'Đã checkin'
        if 'partner_action' in val:
            user_action = self.env['res.partner'].browse(val['partner_action']).name
        elif user_impacting.id != 4:
            val['partner_action'] = user_impacting.partner_id.id
            user_action = user_impacting.partner_id.name
        if 'contract_id' in val:
            # Check event của attendee này xem có đang ở trạng thái draft, accepted, await_confirm không
            if self.event_id.state not in ['draft', 'accepted', 'await_confirm']:
                raise ValidationError('Chỉ thay đổi được hợp đồng khi lịch ở trạng thái nháp, khách đã đến hoặc chờ xác nhận.')
        if 'type_checkin' in val and val['type_checkin']:
            if val['type_checkin'] == 'customer_checkin':
                type_checkin = 'Khách hàng checkin FaceID'
            else:
                type_checkin = 'Lễ tân checkin'
        res = super(CustomCalendarAttendee, self).write(val)
        if res:
            msg = ''
            if state:
                if user_action:
                    msg = f"Khách hàng: <strong>{self.partner_id.name}</strong><br>"
                    msg += f"- Trạng thái: {state} bởi <strong>{user_action}</strong><br>"
                else:
                    msg = f"Khách hàng: <strong>{self.partner_id.name}</strong><br>"
                    msg += f"- Trạng thái: {state}<br>"
            if type_checkin:
                if not msg:
                    msg = f"Khách hàng: <strong>{self.partner_id.name}</strong><br>"
                msg += f"- Kiểu checkin: {type_checkin}"
            
            if msg:
                self._log_activities(event_id=self.event_id.id, message=msg)
            # Cập nhật trạng thái lịch nếu confirm_commission thay đổi
            # Ghi log khi thay đổi confirm_commission
            if 'confirm_commission' in val:
                confirm_msg = f"Khách hàng: <strong>{self.partner_id.name}</strong><br>"
                confirm_msg += f"- Được tính COM: {'Có' if val['confirm_commission'] else 'Không'} bởi <strong>{user_action}</strong>"
                self._log_activities(event_id=self.event_id.id, message=confirm_msg)
                event = self.event_id
                if event:
                    # Kiểm tra điều kiện chuyển lịch sang trạng thái hoàn thành nếu có KH hoàn thành ca tập
                    any_attendee_done = any(att.state == 'done' for att in event.attendee_ids)
                    if any_attendee_done and event.state != 'done':
                        event.write({'state': 'done'})

            event = self.event_id
            event._compute_event_state()
            return res

    def do_tentative(self):
        """ Makes event invitation as Tentative. """
        event = self.event_id
        if event.event_type != 'pt':
            return self.write({'state': 'tentative'})
        else:
            if event.state != 'draft':
                raise ValidationError('Chỉ sử dụng khi lịch ở trạng thái nháp.')
            # logic check xem user thao tác có phải đúng là lễ tân của lịch này không
            user = self.env.user
            # lấy quyền lễ tân
            if not user.has_group('welly_base.group_receptionist'):
                raise ValidationError('Chỉ Lễ Tân mới có quyền thao tác.')
            else:
                return self.write({'state': 'tentative'})

    def partner_confirm_button(self, partner_action=None):
        for attendee in self:
            user = self.env.user
            event = attendee.event_id
            state_accept = ['draft', 'accepted']
            if event.is_merge_class:
                state_accept += ['during_practice']
            if event.state not in state_accept:
                raise UserError(_("Chỉ được thao tác khi lịch ở trạng thái nháp, khách đã đến, đang tập"))
            if user.id != 4 and not user.has_group('welly_base.group_receptionist') and not user.has_group(
                    'welly_base.group_coo') and not user.has_group('welly_base.group_admin_club'):
                raise ValidationError('Bạn không có quyền thao tác.')

            # check xem partner đã có trong 1 lịch khác chưa
            partner = attendee.partner_id
            domain = [
                ('id', '!=', event.id),
                ('state', '!=', 'reject'),
                ('partner_ids', '=', partner.id),
                '|',
                '&', ('start', '<', event.stop), ('stop', '>', event.start),
                '&', ('start', '<', event.stop), ('stop', '>', event.start),
            ]
            overlapping_events = self.env['calendar.event'].search(domain)
            domain_attendee = [
                ('partner_id', '=', partner.id),
                ('event_id', 'in', overlapping_events.ids),
                ('state', 'in', ['accepted', 'pt_confirm'])
            ]
            attendees_with_accepted_state = self.env['calendar.attendee'].search(domain_attendee)
            if attendees_with_accepted_state:
                raise ValidationError(
                    f'Khách hàng {partner.name} đã được chỉ định cho một sự kiện khác trong khoảng thời gian này!')

            # TH lịch: nháp, khách: needAction -> Chờ PT xác nhận
            if attendee.state == 'needsAction':
                if partner_action:
                    attendee.write({'state': 'await_pt_confirm', 'partner_action': partner_action})
                else:
                    attendee.write({'state': 'await_pt_confirm'})
            # TH lịch: nháp, khách: Chờ KH xác nhận -> Đã xác nhận
            if attendee.state == 'tentative':
                if partner_action:
                    attendee.write({'state': 'accepted', 'partner_action': partner_action})
                else:
                    attendee.write({'state': 'accepted'})

    def do_accept(self):
        """ Marks event invitation as Accepted. """
        for attendee in self:
            event = attendee.event_id
            # logic check xem user thao tác có phải đúng là lễ tân của lịch này không
            if event.event_type == 'pt':
                # Lấy thời gian hiện tại
                now = datetime.now()
                # Chỉ có quyền Lễ Tân
                user = self.env.user
                if not user.has_group('welly_base.group_receptionist'):
                    raise UserError(_("Bạn không có quyền thao tác."))
                if attendee.state not in ['accepted', 'tentative']:
                    raise UserError(_("Khách hàng chỉ được checkin khi đang có trạng thái xác nhận, chờ KH xác nhận"))
                if event.state not in ['draft', 'accepted', 'during_practice']:
                    raise UserError(_("Chỉ được thao tác khi lịch ở trạng thái nháp, khách đã đến, đang tập."))
                    # Tính chênh lệch thời gian checkin và thời gian bắt đầu lịch tập, thời gian checkin hợp lệ của hệ thống
                time_checkin_difference_by_seconds = (
                        event.start - now).total_seconds()  # chuyển đổi timedelta thành giây
                # Giá trị thời gian cho phép checkin trước event_start
                company_id = event.user_id.company_id.id
                calendar_config = self.env['welly.calendar.config'].search([('company_id', '=', company_id)])
                if calendar_config:
                    valid_time_checkin_before_event_start_by_seconds = calendar_config.allow_time_early_checkin * 60
                    valid_time_checkin_after_event_start_by_seconds = calendar_config.allow_time_late_checkin * 60
                else:
                    valid_time_checkin_after_event_start_by_seconds = 0
                    valid_time_checkin_before_event_start_by_seconds = 1800
                values = {'is_checked_in': True,
                          'time_checkin': now,
                          'type_checkin': 'reception_checkin'}
                # Trường hợp checkin trước khi ca tập diễn ra
                if 0 < time_checkin_difference_by_seconds <= valid_time_checkin_before_event_start_by_seconds:
                    if attendee.state == 'tentative':
                        values['state'] = 'accepted'
                    attendee.write(values)
                # Trường hợp checkin sau khi qua thời gian ca tập bắt đầu
                elif 0 > time_checkin_difference_by_seconds >= -valid_time_checkin_after_event_start_by_seconds:
                    if event.state == 'draft':
                        values['state'] = 'accepted'
                    else:
                        values['state'] = 'during_practice'
                    attendee.write(values)
                else:
                    raise UserError(
                        _(f"Khoảng thời gian được checkin là từ {int(valid_time_checkin_before_event_start_by_seconds / 60)} phút trước đến {int(valid_time_checkin_after_event_start_by_seconds / 60)} phút sau thời gian bắt đầu lịch tập"))
            else:
                attendee.write({'state': 'accepted'})

    # Button Từ chối
    def do_decline(self):
        """ Marks event invitation as Declined. """
        # logic check xem user thao tác có phải đúng là lễ tân của lịch này không
        user = self.env.user
        now = datetime.now()
        for attendee in self:
            event = attendee.event_id
            if event.event_type == 'pt':
                if event.state not in ['draft', 'accepted']:
                    raise UserError(_("Chỉ được thao tác khi lịch ở trạng thái nháp, khách đã đến"))
                # lấy quyền lễ tân
                if not user.has_group('welly_base.group_receptionist'):
                    raise ValidationError('Chỉ Lễ Tân mới có quyền thao tác.')
                # TH khách: needsAction, chờ KH xác nhận, chờ PT xác nhận, đã xác nhận -> Hủy
                if attendee.state in ['needsAction', 'tentative', 'await_pt_confirm', 'accepted']:
                    attendee.write({'state': 'declined'})
                # Check hủy event nếu:
                # Lịch: Nháp, tất cả khách từ chối, thời gian trước khi diễn ra event
                if event.state == 'draft' and now < event.start and all(
                        attendee.state == 'declined' for attendee in event.attendee_ids):
                    event.write({'state': 'reject'})
            if event.event_type == 'calendar':
                attendee.write({'state': 'declined'})
                if not all(attendee.state == 'declined' for attendee in event.attendee_ids):
                    event.write({'state': 'draft'})
                else:
                    event.write({'state': 'reject'})

    def nothing_button(self):
        raise ValidationError('Không thể thao tác, chức năng này của khách hàng.')


    # Button Khách quên checkin
    def partner_forget_checkin_button(self):
        # Lấy thời gian hiện tại
        now = datetime.now()
        # Chỉ có quyền Lễ Tân
        user = self.env.user
        if user.id != 4 and not user.has_group('welly_base.group_receptionist'):
            raise UserError(_("Bạn không có quyền thao tác."))
        for attendee in self:
            if attendee.state != 'partner_skip_session':
                raise UserError(_("Chỉ được thao tác khi khách hàng có trạng thái bỏ tập."))
            event = attendee.event_id
            attendee.write({'is_checked_in': True,
                            'time_checkin': now,
                            'type_checkin': 'reception_checkin'})
            if event.state == 'during_practice':
                attendee.write({'state': 'during_practice'})
            if event.state == 'done':
                attendee.write({'state': 'done', 'confirm_commission': True})
            if event.state == 'partner_skip_session':
                if event.start < now < event.stop:
                    attendee.write({'state': 'during_practice'})
                    event.write({'state': 'during_practice'})
                if now >= event.stop:
                    attendee.write({'state': 'done', 'confirm_commission': True})
                    event.write({'state': 'done'})

    # Button PT xác nhận
    def pt_confirm_button(self, user_action=None):
        for attendee in self:
            event = attendee.event_id
            if event.state not in ['draft', 'accepted']:
                raise UserError(_("Chỉ được thao tác khi lịch ở trạng thái nháp, khách đã đến"))
            # TH lịch: nháp, khách: needAction -> Chờ KH xác nhận
            if attendee.state == 'needsAction':
                if user_action:
                    partner_action = user_action.partner_id
                    attendee.write({'state': 'tentative', 'partner_action': partner_action.id})
                else:
                    attendee.write({'state': 'tentative'})
            # TH lịch: nháp, khách: Chờ PT xác nhận -> Đã xác nhận
            if attendee.state == 'await_pt_confirm':
                if user_action:
                    partner_action = user_action.partner_id
                    attendee.write({'state': 'accepted', 'partner_action': partner_action.id})
                else:
                    attendee.write({'state': 'accepted'})
            # Gửi thông báo cho KH qua firebase
            service_name = f"{event.service_id.name} " if event.service_id.name else ""
            title = 'Lịch tập đã được PT xác nhận'
            content = f'Bạn đã được PT xác nhận tham gia vào lớp học {service_name}vào {self._convert_date_format(str(event.start + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")} đến {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")}' \
                      f' {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%d/%m/%Y")}'
            partner_id = attendee.partner_id.id
            self._send_noti_to_attendee(partner_id, title, content)

    # Button Khách hàng từ chối
    def partner_decline_button(self, partner_action=None):
        now = datetime.now()
        for attendee in self:
            event = attendee.event_id
            if event.state not in ['draft', 'accepted', 'during_practice']:
                raise UserError(_("Chỉ được thao tác khi lịch ở trạng thái nháp, khách đã đến, đang tập "))
            # TH khách: needsAction, chờ KH xác nhận, chờ PT xác nhận, đã xác nhận -> Hủy
            if attendee.state in ['needsAction', 'tentative', 'await_pt_confirm', 'accepted']:
                if partner_action:
                    attendee.write({'state': 'declined', 'partner_action': partner_action})
                else:
                    attendee.write({'state': 'declined'})
            # Check hủy event nếu:
            # Lịch: Nháp + Khách đã đến, tất cả khách từ chối, thời gian trước khi diễn ra event
            # Lịch: Đang tập, tất cả khách từ chối, bỏ tập, hoàn buổi, thời gian trong khi event diễn ra
            if (event.state in ['draft', 'accepted'] and now < event.start and all(
                    attendee.state == 'declined' for attendee in event.attendee_ids)) \
                    or (event.state == 'during_practice' and event.start <= now < event.stop and all(
                attendee.state in ['declined', 'partner_skip_session', 'refund_session'] for attendee in
                event.attendee_ids)):
                event.write({'state': 'reject'})

    # Button PT từ chối
    def pt_decline_button(self, user_action=None):
        now = datetime.now()
        for attendee in self:
            event = attendee.event_id
            if event.state not in ['draft', 'accepted', 'during_practice']:
                raise UserError(_("Chỉ được thao tác khi lịch ở trạng thái nháp, khách đã đến, đang tập"))
            # TH khách: needsAction, chờ KH xác nhận, chờ PT xác nhận, đã xác nhận -> Hủy
            if attendee.state in ['needsAction', 'await_pt_confirm', 'accepted', 'tentative']:
                if user_action:
                    partner_action = user_action.partner_id
                    attendee.write({'state': 'declined', 'partner_action': partner_action.id})
                else:
                    attendee.write({'state': 'declined'})
            # Gửi thông báo cho KH qua firebase
            service_name = f"{event.service_id.name} " if event.service_id.name else ""
            title = 'Lịch tập đã bị hủy'
            content = f'PT vừa hủy ' \
                      f'Ca tập {self._convert_date_format(str(event.start + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")} - {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")}' \
                      f' {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%d/%m/%Y")}'
            partner_id = attendee.partner_id.id
            self._send_noti_to_attendee(partner_id, title, content)
            # Check hủy event nếu:
            # Lịch: Nháp + Khách đã đến, tất cả khách từ chối, thời gian trước khi diễn ra event
            # Lịch: Đang tập, tất cả khách từ chối, bỏ tập, hoàn buổi, thời gian trong khi event diễn ra
            if (event.state in ['draft', 'accepted'] and now < event.start and all(
                    attendee.state == 'declined' for attendee in event.attendee_ids)) \
                    or (event.state == 'during_practice' and event.start <= now < event.stop and all(
                attendee.state in ['declined', 'partner_skip_session', 'refund_session'] for attendee in
                event.attendee_ids)):
                event.write({'state': 'reject'})


    # Button Hoàn buổi tập
    def refund_session_button(self):
        # nếu đã hoàn buổi lần trước thì không được hoàn nữa
        if self.is_refunded:
            raise UserError(_("Chỉ được thao tác Hoàn buổi một lần duy nhất."))
        # Booking của Người tham dự phải map với 1 Hợp Đồng hợp lệ
        if not self.contract_id:
            raise UserError(_("Người tham dự phải có hợp đồng hợp lệ mới được hoàn buổi."))
        # check quyền
        user = self.env.user
        # chỉ có quyền Lễ Tân, System Admin mới có quyền
        if not user.has_group('welly_base.group_receptionist') and not user.has_group('base.group_system'):
            raise UserError(_("Bạn không có quyền thao tác."))
        # direct đến wizard
        view_id = self.env.ref('welly_book_pt.popup_refund_session').id
        return {
            'name': 'Hoàn buổi tập',
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'popup.refund.session',
            'view_id': view_id,
            'type': 'ir.actions.act_window',
            'target': 'new',
            'context': {}
        }

    def pt_delete_button(self, user_action=None):
        user = self.env.user
        if user_action:
            user = user_action
        partner_action = user.partner_id
        if not user.has_group('welly_base.group_pt'):
            raise UserError(_("Bạn không có quyền thao tác."))

        for attendee in self:
            event = attendee.event_id
            if event.state not in ['draft', 'accepted']:
                raise UserError(_("Chỉ được thao tác khi lịch ở trạng thái nháp hoặc khách đã đến."))
            pt = event.pt_id
            pt_user = event.pt_id_substitute if event.pt_id_substitute else pt
            if pt_user != user:
                raise UserError(_("Chỉ PT của lịch mới được thao tác."))
            partner_ids = event.partner_ids.ids
            partner_id_delete = attendee.partner_id.id

            if partner_ids:
                partner_ids = [pid for pid in partner_ids if pid != partner_id_delete]

            # Gửi thông báo cho KH qua firebase
            title = 'Lịch tập đã bị hủy'
            content = f'PT vừa hủy '\
                      f'Ca tập {self._convert_date_format(str(event.start + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")} - {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")}'\
                      f' {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%d/%m/%Y")}'
            partner_id = attendee.partner_id.id
            notification_q = self.env['notification.queue'].sudo()
            # Tạo thông báo
            noti_to_attendee = notification_q.create({
                'partner_id': partner_id,
                'title': title,
                'content_summary': content,
                'type': 'readonly',
            })
            notification_q.send_notification_to_firebase(noti_to_attendee)

            event.with_user(user.id).write({'partner_ids': [[6, False, partner_ids]]})

    def confirm_button(self, user_action=None):
        user = self.env.user
        if user_action:
            user = user_action
        partner_action = user.partner_id
        if not user.has_group('welly_base.group_receptionist') and not user.has_group('welly_base.group_pt') and not user.has_group('welly_base.group_admin_club'):
            raise UserError(_("Bạn không có quyền thao tác."))

        for attendee in self:
            event = attendee.event_id
            if event.state not in ['draft', 'accepted']:
                raise UserError(_("Chỉ được thao tác khi lịch ở trạng thái nháp hoặc khách đã đến."))
            if user.has_group('welly_base.group_pt'):
                pt = event.pt_id
                pt_user = event.pt_id_substitute if event.pt_id_substitute else pt
                if pt_user != user:
                    raise UserError(_("Chỉ PT của lịch mới được thao tác."))

            attendee_values = {'partner_action': partner_action.id}
            pre_state = attendee.previous_state
            if pre_state == 'await_pt_confirm':
                attendee_values['state'] = 'accepted'
            if pre_state in ['tentative', 'accepted']:
                attendee_values['state'] = pre_state
            if pre_state == 'needsAction':
                attendee_values['state'] = 'tentative'
            attendee.write(attendee_values)

            # Gửi thông báo cho KH qua firebase
            service_name = f"{event.service_id.name} " if event.service_id.name else ""
            title = 'Lịch tập đã được PT xác nhận'
            content = f'Bạn đã được PT xác nhận tham gia vào lớp học {service_name}vào {self._convert_date_format(str(event.start + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")} đến {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")}' \
                      f' {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%d/%m/%Y")}'
            partner_id = attendee.partner_id.id
            self._send_noti_to_attendee(partner_id, title, content)

    def _convert_utc_to_timestamp_milliseconds(self, utc_time):
        timestamp_seconds = utc_time.timestamp()
        timestamp_milliseconds = int(timestamp_seconds * 1000)
        return timestamp_milliseconds

    # ví dụ: input_date: str time; original_format = "%Y-%m-%d"; destination_format= "%d-%m-%Y"
    def _convert_date_format(self, input_date, original_format, destination_format):
        datetime_object = datetime.strptime(input_date, original_format)
        formatted_date = datetime_object.strftime(destination_format)
        return formatted_date

    def _log_activities(self, event_id: int, message):
        # cập nhật log activities
        self.env['mail.message'].create({
            'model': 'calendar.event',
            'res_id': event_id,
            'message_type': 'comment',
            'body': message,
        })

    def action_show_details(self):
        return {
            'type': 'ir.actions.act_window',
            'name': 'Chi tiết khách hàng',
            'res_model': 'calendar.attendee',
            'view_id': self.env.ref('welly_book_pt.view_form_custom_calendar_attendee').id,
            'view_mode': 'form',
            'res_id': self.id,
            'target': 'new',
            'context': {'create': False,
                        'edit': False,
                        'delete': False
            }
        }

    def action_close(self):
        return {'type': 'ir.actions.act_window_close'}

    '''
    Tạo và gửi thông báo cho khách hàng qua firebase
    - partner_id: id của khách hàng
    - title: tiêu đề thông báo
    - content_summary: nội dung thông báo
    '''
    def _send_noti_to_attendee(self, partner_id, title, content_summary):
        # Lấy quyền super user để truy cập vào model notification.queue
        notification_q = self.env['notification.queue'].sudo()
        # Tạo thông báo
        noti_to_attendee = notification_q.create({
            'partner_id': partner_id,
            'title': title,
            'content_summary': content_summary,
            'type': 'training',
            'calendar_id': self.event_id.id
        })
        # Gửi thông báo cho khách hàng qua firebase
        notification_q.send_notification_to_firebase(noti_to_attendee)

    @api.model
    def _get_last_month_domain(self):
        # Lấy thời gian hiện tại theo UTC
        today = datetime.utcnow()
        user = self.env.user

        # Tính toán ngày đầu tiên của tháng hiện tại và trừ đi 1 tháng
        first_day_of_this_month = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        first_day_of_last_month_utc = (first_day_of_this_month - relativedelta(months=1) - relativedelta(hours=7))

        # Tính toán ngày cuối cùng của tháng trước
        last_day_of_last_month_utc = first_day_of_this_month - timedelta(seconds=1) - timedelta(hours=7)
        domain_last_month = [
            '&',
            ('date', '>=', first_day_of_last_month_utc.strftime('%Y-%m-%d %H:%M:%S')),
            ('date', '<=', last_day_of_last_month_utc.strftime('%Y-%m-%d %H:%M:%S'))
        ]
        return domain_last_month

    @api.model
    def _get_current_month_domain(self):
        # Lấy thời gian hiện tại theo UTC
        today = datetime.utcnow()

        # Tính toán ngày đầu tiên của tháng hiện tại
        first_day_of_this_month = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        first_day_of_this_month_utc = first_day_of_this_month - relativedelta(hours=7)

        # Tính toán ngày đầu tiên của tháng sau
        first_day_of_next_month = (first_day_of_this_month + relativedelta(months=1)).replace(day=1)
        first_day_of_next_month_utc = first_day_of_next_month - relativedelta(hours=7)

        # Ngày cuối cùng của tháng này là một ngày trước ngày đầu tiên của tháng sau
        last_day_of_this_month_utc = first_day_of_next_month_utc - relativedelta(seconds=1)

        # Trả về domain để lọc các bản ghi trong khoảng thời gian này
        domain_current_month = ['&',
                                ('date', '>=', first_day_of_this_month_utc.strftime('%Y-%m-%d %H:%M:%S')),
                                ('date', '<=', last_day_of_this_month_utc.strftime('%Y-%m-%d %H:%M:%S'))]
        return domain_current_month

    # override hàm search để thêm bộ lọc tùy chỉnh
    @api.model
    def search(self, domain, offset=0, limit=None, order=None, count=False):
        # Thêm domain chỉ cho pt xem bản ghi của chính mình
        is_report_view = self.env.context.get('is_report_view')
        user = self.env.user
        if not user.has_group('welly_base.group_coo') \
                and not user.has_group('welly_base.group_welly_account') \
                and not user.has_group('welly_base.group_admin_club') \
                and not user.has_group('welly_base.group_receptionist') \
                and not user.has_group('welly_base.group_pt_manager') \
                and user.has_group('welly_base.group_pt') and is_report_view:
            domain += [('pt_id', 'ilike', user.name)]

        # Kiểm tra nếu domain chứa 'last_month'
        for index, arg in enumerate(domain):
            if isinstance(arg, (list, tuple)) and len(arg) == 3 and arg[0] == 'date' and arg[2] == 'last_month':
                # Thay thế với domain tháng trước
                domain[index:index + 1] = self._get_last_month_domain()
            if isinstance(arg, (list, tuple)) and len(arg) == 3 and arg[0] == 'date' and arg[2] == 'current_month':
                # Thay thế với domain tháng trước
                domain[index:index + 1] = self._get_current_month_domain()

        return super(CustomCalendarAttendee, self).search(domain, offset, limit, order, count)

    @api.model
    def read_group(self, domain, fields, groupby, offset=0, limit=None, orderby=False, lazy=True):
        # Thêm domain chỉ cho pt xem bản ghi của chính mình
        user = self.env.user
        is_report_view = self.env.context.get('is_report_view')
        if not user.has_group('welly_base.group_coo') \
                and not user.has_group('welly_base.group_welly_account') \
                and not user.has_group('welly_base.group_admin_club') \
                and not user.has_group('welly_base.group_receptionist') \
                and not user.has_group('welly_base.group_pt_manager') \
                and user.has_group('welly_base.group_pt') and is_report_view:
            domain += [('pt_id', 'ilike', user.name)]
        # Kiểm tra nếu domain chứa 'last_month'
        for index, arg in enumerate(domain):
            if isinstance(arg, (list, tuple)) and len(arg) == 3 and arg[0] == 'date' and arg[2] == 'last_month':
                # Thay thế với domain tháng trước
                domain[index:index + 1] = self._get_last_month_domain()
            if isinstance(arg, (list, tuple)) and len(arg) == 3 and arg[0] == 'date' and arg[2] == 'current_month':
                # Thay thế với domain tháng trước
                domain[index:index + 1] = self._get_current_month_domain()

        return super(CustomCalendarAttendee, self).read_group(domain, fields, groupby, offset, limit, orderby, lazy)

    def export_data(self):
        try:
            # Tạo file Excel trong bộ nhớ
            output = io.BytesIO()
            workbook = xlsxwriter.Workbook(output)
            worksheet = workbook.add_worksheet('BÁO CÁO TỔNG HỢP LỊCH DẠY PT')

            # Định nghĩa format cho các cell
            header = workbook.add_format({'bold': True, 'border': 1, 'color': 'white', 'bg_color': 'blue', 'align': 'center', 'valign': 'vcenter'})
            content_center = workbook.add_format({'border': 1, 'align': 'center'})
            content_left = workbook.add_format({'border': 1, 'align': 'left'})
            content_right = workbook.add_format({'border': 1, 'num_format': '#,##0', 'align': 'right'})


            # Viết header
            worksheet.write('A1', 'STT', header)
            worksheet.write('B1', 'Ngày', header)
            worksheet.write('C1', 'HLV', header)
            worksheet.write('D1', 'Khách hàng', header)
            worksheet.write('E1', 'SĐT', header)
            worksheet.write('F1', 'Gói dịch vụ PT', header)
            worksheet.write('G1', 'Số hợp đồng', header)
            worksheet.write('H1', 'Hình thức tập', header)
            worksheet.write('I1', 'Trạng thái lịch', header)
            worksheet.write('J1', 'Trạng thái khách hàng', header)
            worksheet.write('K1', 'Kiểu checkin', header)
            worksheet.write('L1', 'Thời gian checkin', header)
            worksheet.write('M1', 'Giá gói PT', header)
            worksheet.write('N1', 'Buổi chính', header)
            worksheet.write('O1', 'Buổi tặng', header)
            worksheet.write('P1', 'Phân bổ', header)
            worksheet.write('Q1', 'Hoa hồng buổi tặng', header)
            worksheet.write('R1', 'Ghi chú lịch', header)
            worksheet.write('S1', 'Được tính COM', header)


            # Lấy múi giờ của người dùng hiện tại
            user_tz = self.env.user.tz or 'UTC'
            tz = timezone(user_tz)

            # Viết dữ liệu của các bản ghi
            row = 1
            for record in self:
                date = record.date.astimezone(tz).date().strftime('%d/%m/%Y')
                pt = record.pt_id.name or ''
                customer = record.partner_id.name or ''
                phone = record.phone or ''
                service = record.sale_order_template_name_print or ''
                contract = record.contract_id.name or ''
                exercise = record.exercise_id.name or ''
                event_state = record.event_state or ''
                dict_event_state = dict(selection.EventState.selection)
                if event_state:
                    event_state = dict_event_state.get(event_state, '')
                state = record.state or ''
                dict_state = dict(self.STATE_SELECTION)
                if state:
                    state = dict_state.get(state, '')
                type_checkin = record.type_checkin or ''
                dict_type_checkin = dict(self.TYPE_CHECKIN)
                if type_checkin:
                    type_checkin = dict_type_checkin.get(type_checkin, '')
                time_checkin = record.time_checkin.astimezone(tz).strftime(
                    '%d/%m/%Y %H:%M:%S') if record.time_checkin else ''
                pay_amount = record.pay_amount or 0
                main_session = record.main_session_char or ''
                gift_session = record.gift_session_char or ''
                pay_amount_per_session_number = record.pay_amount_per_session_number or 0
                gift_session_commission = record.gift_session_commission or 0
                event_notes = record.event_notes or ''
                confirm_commission = 'Có' if record.confirm_commission else 'Không'

                worksheet.write(row, 0, row, content_center)
                worksheet.write(row, 1, date, content_center)
                worksheet.write(row, 2, pt, content_center)
                worksheet.write(row, 3, customer, content_center)
                worksheet.write(row, 4, phone, content_center)
                worksheet.write(row, 5, service, content_center)
                worksheet.write(row, 6, contract, content_center)
                worksheet.write(row, 7, exercise, content_center)
                worksheet.write(row, 8, event_state, content_center)
                worksheet.write(row, 9, state, content_center)
                worksheet.write(row, 10, type_checkin, content_center)
                worksheet.write(row, 11, time_checkin, content_center)
                worksheet.write(row, 12, pay_amount, content_right)
                worksheet.write(row, 13, main_session, content_center)
                worksheet.write(row, 14, gift_session, content_center)
                worksheet.write(row, 15, pay_amount_per_session_number, content_right)
                worksheet.write(row, 16, gift_session_commission, content_right)
                worksheet.write(row, 17, event_notes, content_left)
                worksheet.write(row, 18, confirm_commission, content_left)
                row += 1

            # Đóng file Excel
            workbook.close()

            # Lấy nội dung file từ bộ nhớ
            file_data = output.getvalue()
            output.close()

            # Mã hóa base64 cho file
            encoded_file_data = base64.b64encode(file_data)

            # Tạo attachment trong Odoo
            attachment = self.env['ir.attachment'].create({
                'name': 'Báo cáo tổng hợp lịch dạy PT.xlsx',
                'datas': encoded_file_data,
                'type': 'binary',
            })

            return {
                'type': 'ir.actions.act_url',
                'url': f'/web/content/{attachment.id}?download=true',
                'target': 'new',
            }

        except Exception as e:
            # Ghi lỗi vào log
            _logger.error('Error exporting data to Excel: %s', str(e))
            # Hiển thị thông báo lỗi cho người dùng
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Xuất dữ liệu thất bại',
                    'message': 'Có lỗi xảy ra khi xuất dữ liệu.',
                    'type': 'danger',
                }
            }



