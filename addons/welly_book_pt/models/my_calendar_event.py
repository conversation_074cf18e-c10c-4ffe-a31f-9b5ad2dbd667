import logging
from datetime import date, timedelta, datetime, timezone
import threading

import pytz
from dateutil.relativedelta import relativedelta

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from ..fields import selection
_logger = logging.getLogger(__name__)


class CalendarEvent(models.Model):
    _inherit = 'calendar.event'

    categ_ids = fields.Many2many(
        'calendar.event.type', 'meeting_category_rel', 'event_id', 'type_id', 'Thẻ')
    attendee_ids = fields.One2many(
        'calendar.attendee', 'event_id', 'Danh sách người được mời')
    # timing
    start = fields.Datetime(
        'Bắt đầu vào', required=True, tracking=True, default=fields.Date.today,
        help="Start date of an event, without time for full days events")
    stop = fields.Datetime(
        'Kết thúc vào', required=True, tracking=True, default=lambda self: fields.Datetime.today() + timedelta(hours=1),
        compute='_compute_stop', readonly=False, store=True,
        help="Stop date of an event, without time for full days events")
    location_id = fields.Many2one('welly.location', string='Địa điểm trung tâm')
    event_type = fields.Selection([
        ('pt', 'Lịch tập PT'),
        ('calendar', 'Lịch hẹn')
    ], string='Loại lịch', index=True, default='calendar')

    is_app_booking = fields.Boolean(string='Lịch do App đặt', default=False)

    calendar_config_id = fields.Many2one(
        comodel_name='welly.calendar.config',
        default=lambda self: self.env['welly.calendar.config'].search([('company_id', '=', self.env.company.id)], limit=1),
        store=True)

    allow_merge_class = fields.Boolean(
        related='calendar_config_id.allow_merge_class',
        string='Cho phép ghép lớp')

    is_merge_class = fields.Boolean(
        string='Ghép lớp',
        default=False
    )

    state = fields.Selection(selection=selection.EventState.selection, string=selection.EventState.string, default=selection.EventState.DRAFT, index=True, copy=False)

    @api.model
    def _compute_event_state(self):
        for event in self:
            if event.event_type == 'calendar':
                if event.attendee_ids and all(att.state == 'accepted' for att in event.attendee_ids):
                    event.state = 'accepted'
            elif any((att.state == 'accepted' and att.is_checked_in == True) for att in event.attendee_ids) and event.state == 'draft':
                    event.state = 'accepted'

    # Đặt lịch quá khứ
    is_past_booking = fields.Boolean(string='Đặt lịch trong quá khứ', store=True)

    attachment_ids = fields.Many2many('ir.attachment', string="File đính kèm")

    description_past_booking = fields.Text(string="Mô tả")

    total_attendance = fields.Integer(
        string='Tổng người được mời',
        compute='_compute_total_attendance',
        store=True,
    )

    took_attendance = fields.Integer(
        string='KH đã check-in',
        compute='_compute_took_attendance',
        store=True,
    )
    # pt chính ứng với hợp đồng
    pt_id = fields.Many2one('res.users', string='PT chính', domain=lambda self: self._compute_coach_domain(),
                            default=lambda self: self.env.user
                            if (self.env.user.has_group('welly_base.group_pt')
                                or self.env.user.has_group('welly_base.group_pt_manager'))
                            else False)
    # pt dạy thay trường hợp pt chính có việc bận
    pt_id_substitute = fields.Many2one('res.users', string='PT dạy thay',
                                       domain=lambda self: self._compute_coach_domain())

    @api.model
    def _compute_coach_domain(self):
        # lấy biến môi trường id của department_pt
        company_id = self.env.company.id
        group_welly_account = self.env.ref('welly_base.group_pt')
        group_pt_manager = self.env.ref('welly_base.group_pt_manager')

        # Tìm tất cả người dùng thuộc nhóm quyền pt và pt_manager
        users = self.env['res.users'].search([
            '|',
            ('groups_id', 'in', group_welly_account.id),
            ('groups_id', 'in', group_pt_manager.id),
            ('company_id', '=', company_id)
        ])
        # Trả về domain với danh sách user_ids
        return [('id', 'in', users.ids)]

    session_type = fields.Many2one(
        'calendar.event.class.type', 'Loại lớp')

    exercise_id = fields.Many2one('welly.exercise.form', string='Hình thức tập')

    service_id = fields.Many2one('welly.service.type', string='Dịch vụ')

    attendee_status = fields.Selection(
        selection=selection.AttendeeState.selection, string='Trạng thái người được mời', compute='_compute_attendee')
    # với case user book đã có contract
    contract_id = fields.Many2one('welly.contract', string='Mã hợp đồng')

    # với loại calendar = pt thì không tự động điền người tham gia
    @api.model
    def _default_partners(self):
        if self._context.get('default_event_type') != 'pt':
            partners = self.env.user.partner_id
            active_id = self._context.get('active_id')
            if self._context.get('active_model') == 'res.partner' and active_id and active_id not in partners.ids:
                partners |= self.env['res.partner'].browse(active_id)
            return partners

    partner_ids = fields.Many2many(
        'res.partner', 'calendar_event_res_partner_rel',
        string='Người được mời',
        default=_default_partners
    )

    @api.onchange('pt_id', 'exercise_id', 'location_id', 'event_type')
    def _onchange_partner_ids(self):
        domain = {'partner_ids': []}
        if self.event_type == 'pt' or not self.event_type:
            all_partners = self.env['res.partner'].search([('customer_rank', '>', 0)])
            domain = {'partner_ids': [('id', 'in', all_partners.ids)]}
        elif self.event_type == 'calendar':
            partners_event_type_calendar = self.env['res.partner'].search([('type', '!=', 'private')])
            domain = {'partner_ids': [('id', 'in', partners_event_type_calendar.ids)]}
        return {'domain': domain}

    partner_phones = fields.Char(string='Số điện thoại khách hàng', related='attendee_ids.phone')

    # những khách hàng đã tham gia và có trạng thái attendee_status = 'accepted'
    accepted_partner_ids = fields.Many2many(
        'res.partner', 'calendar_event_accepted_partner_ids_rel', 'event_id', 'partner_id', compute='_compute_accepted_partners', string='KH đồng ý tham gia', store=True
    )

    @api.depends('attendee_ids', 'attendee_ids.state', 'attendee_ids.partner_id')
    def _compute_accepted_partners(self):
        for event in self:
            accepted_partners = event.attendee_ids.filtered(lambda a: a.state in ['accepted', 'pt_confirm']).mapped('partner_id')
            event.accepted_partner_ids = [(6, 0, accepted_partners.ids)]

    is_full_participants = fields.Boolean(string='Đã đủ số lượng người tham gia', compute='_compute_is_full_participants', store=True, default=False)
    @api.depends('attendee_ids', 'attendee_ids.state', 'attendee_ids.partner_id', 'exercise_id')
    def _compute_is_full_participants(self):
        for event in self:
            if event.event_type == 'pt' and not event.is_merge_class:
                # Lấy số người đã tham gia lớp có trạng thái khác huỷ
                number_accepted_partners = len(event.attendee_ids.filtered(lambda a: a.state != 'declined'))
                # So sánh với số lượng người tối đa của lớp và trả về true false
                if number_accepted_partners >= event.exercise_id.limit_participants:
                    event.is_full_participants = True
                else:
                    event.is_full_participants = False

    contract_code = fields.Html(string="Mã HĐ tương ứng", compute='_compute_contract_code', store=True, readonly=True)

    @api.depends('attendee_ids', 'attendee_ids.partner_id')
    def _compute_contract_code(self):
        for event in self:
            # ko phải lịch PT return luôn k cần check
            if event.event_type != 'pt':
                continue
            contract_code = ""
            for attendee in event.attendee_ids:
                if attendee.contract_id:
                    contract_code = f"{contract_code}{attendee.partner_id.name}-<strong>{attendee.contract_id.name}</strong>,<br>"
            event.contract_code = contract_code

    notes = fields.Text(string='Ghi chú')

    can_edit_notes = fields.Boolean(string='Có thể sửa ghi chú', compute='_compute_can_edit_notes')

    # admin được sửa notes với tất cả lịch có trạng thái khác Hủy
    # user khác được sửa notes với tất cả lịch có trạng thái Nháp, Chờ xác nhận
    def _compute_can_edit_notes(self):
        is_admin = self.env.user.has_group('welly_base.group_admin_club')
        for record in self:
            if is_admin:
                record.can_edit_notes = record.state != 'reject'
            else:
                record.can_edit_notes = record.state in ['draft', 'await_confirm']

    is_deduct_session_canceled = fields.Boolean(
        string='Trừ buổi đối với khách bỏ tập',
        compute='_compute_is_deduct_session_canceled',
        store=True
    )
    
    @api.depends('calendar_config_id')
    def _compute_is_deduct_session_canceled(self):
        for event in self:
            if event.calendar_config_id:
                event.is_deduct_session_canceled = event.calendar_config_id.is_deduct_session_canceled

    @api.constrains('partner_ids', 'user_id', 'exercise_id')
    def _check_null_partner_and_user(self):
        if self.event_type == 'pt':
            user = self.env.user
            limit_participants = self.exercise_id.limit_participants

            # Kiểm tra nếu partner_ids và user_id tồn tại
            if not self.is_app_booking and (limit_participants == 1) and (not self.partner_ids or not self.partner_ids[0]):
                raise ValidationError(_("Lịch 1:1 người được mời không được để trống."))

            if not self.user_id:
                raise ValidationError(_("Người tạo không được để trống."))

    # rule: PT chỉ được đặt lịch quá khứ cho chính mình
    @api.constrains('pt_id')
    def _check_pt_past_booking(self):
        user = self.env.user
        if self.event_type == 'pt' and self.is_past_booking and user.has_group('welly_base.group_pt') and not user.has_group('welly_base.group_receptionist') and not user.has_group('welly_base.group_admin_club'):
            if not self.is_app_booking and user != self.pt_id:
                raise ValidationError(_("Lịch quá khứ: Chỉ Admin, lễ tân và PT mới có quyền đặt lịch trong quá khứ (PT chỉ được đặt cho chính mình)"))

    @api.model_create_multi
    def create(self, vals: list[dict]):
        user = self.env.user
        for values in vals:
            if 'event_type' in values and values['event_type'] == 'pt':
                is_app_booking = values.get('is_app_booking')
                if not is_app_booking:
                    if user.id != 4 and (not user.has_group('welly_base.group_receptionist') and not user.has_group('welly_base.group_pt') and not user.has_group('welly_base.group_admin_club')):
                        raise UserError(_("Chỉ lễ tân và PT và Admin Club có quyền thao tác."))
        res = super(CalendarEvent, self).create(vals)
        for r in res:
            # tạo log booking
            if r.event_type == 'pt':
                if r.description_past_booking or r.attachment_ids:
                    description_past_booking = r.description_past_booking or ''
                    attachment_ids = r.attachment_ids.ids or []
                    r._post_message_to_chatter(event_id=r.id, description_past_booking=description_past_booking, attachment_ids=attachment_ids)
                attendee_name = r.attendee_ids.mapped('partner_id.name')
                self._log_activities(event_id=r.id, message=f"Tạo mới lịch tập:"
                                                              f"<br>- Dịch vụ: {r.service_id.name}"
                                                              f"<br>- PT chính: {r.pt_id.name}"
                                                              f"<br>- PT dạy thay: {r.pt_id_substitute.name}"
                                                              f"<br>- Hình thức tập: {r.exercise_id.name}"
                                                              f"<br>- Địa điểm: {r.location_id.name}"
                                                              f"<br>- Bắt đầu: {str(r.start + timedelta(hours=7))}"
                                                              f"<br>- Kết thúc: {str(r.stop + timedelta(hours=7))}"
                                                              f"<br>- Ghi chú: {r.notes}"
                                                              f"<br>- Người tham dự: {attendee_name}")
                if r.is_past_booking:
                    r.state = 'await_confirm'
        return res

    def write(self, val):
        user = self.env.user

        records_to_update_state = self.env['calendar.event']
        log_messages_to_create = {}

        for record in self:
            log_fields = []

            if record.event_type == 'pt':
                if 'is_past_booking' in val:
                    raise UserError("Mục lịch quá khứ không thể thay đổi")

                if (user.id != 4 or not record.is_app_booking) and not user.has_group(
                        'welly_base.group_receptionist') and not user.has_group(
                        'welly_base.group_pt') and not user.has_group('welly_base.group_admin_club'):
                    raise UserError(_("Chỉ lễ tân và PT và Admin Club có quyền thao tác."))

                if record.is_past_booking:
                    if user.has_group('welly_base.group_pt') and not user.has_group(
                            'welly_base.group_receptionist') and not user.has_group(
                            'welly_base.group_admin_club'):
                        if record.pt_id.id != user.id:
                            raise UserError(f"Lịch quá khứ '{record.name}': PT chỉ được sửa lịch của chính mình.")

                        if record.state == 'draft':
                            records_to_update_state |= record

            if 'description_past_booking' in val or 'attachment_ids' in val:
                description_past_booking = val.get('description_past_booking',
                                                   '') or record.description_past_booking or ''
                attachment_ids = val.get('attachment_ids', [[0, 0, []]])[0][2] or record.attachment_ids.ids or []
                message_params = {}
                if description_past_booking:
                    message_params['description_past_booking'] = description_past_booking
                if attachment_ids:
                    message_params['attachment_ids'] = attachment_ids
                if message_params:
                    record._post_message_to_chatter(event_id=record.id, **message_params)

            if val.get('service_id'):
                service_name = self.env['welly.service.type'].sudo().browse(int(val['service_id'])).name
                log_fields.append(f"Dịch vụ: {service_name}")
            if val.get('state'):
                state_selection = dict(record._fields['state'].selection)
                state = state_selection.get(val['state'])
                log_fields.append(f"Trạng thái: {state}")
            if val.get('pt_id'):
                pt_name = self.env['res.users'].sudo().browse(int(val['pt_id'])).name
                log_fields.append(f"PT chính: {pt_name}")
            if val.get('exercise_id'):
                exercise_name = self.env['welly.exercise.form'].sudo().browse(int(val['exercise_id'])).name
                log_fields.append(f"Hình thức tập: {exercise_name}")
            if val.get('pt_id_substitute'):
                pt_sub_name = self.env['res.users'].sudo().browse(int(val['pt_id_substitute'])).name
                log_fields.append(f"PT dạy thay: {pt_sub_name}")
            if val.get('location_id'):
                location_name = self.env['welly.location'].sudo().browse(int(val['location_id'])).name
                log_fields.append(f"Địa điểm: {location_name}")
            if 'notes' in val:
                notes = val['notes']
                log_fields.append(f"Ghi chú: {notes}")
            if 'is_merge_class' in val:
                is_merge = val['is_merge_class']
                log_fields.append(f"Gộp lớp: {'Có' if is_merge else 'Không'}")
            if 'partner_ids' in val:
                old_partner = record.partner_ids.mapped('id')
                index = len(val['partner_ids'][0])
                if index == 2:
                    add = self.env['res.partner'].browse(int(val['partner_ids'][0][1])).mapped('name')
                    log_fields.append(f"Thêm người tham dự: {add}")
                if index > 2:
                    new_partner = val['partner_ids'][0][2]
                    partner = record.compare_lists(old_partner, new_partner)
                    removed = partner['removed']
                    added = partner['added']
                    if removed:
                        log_fields.append(f"Xóa người tham dự: {removed}")
                    if added:
                        log_fields.append(f"Thêm người tham dự: {added}")

            if log_fields:
                change_fields_str = "<br>- ".join(log_fields)
                log_messages_to_create[record.id] = f"Cập nhật thông tin:<br> {change_fields_str}"

        res = super(CalendarEvent, self).write(val)

        if res:
            if records_to_update_state:
                records_to_update_state.write({'state': 'await_confirm'})

            if log_messages_to_create:
                for record in self:
                    if record.id in log_messages_to_create:
                        record._log_activities(event_id=record.id, message=log_messages_to_create[record.id])

        return res

    def _post_message_to_chatter(self, event_id, description_past_booking=None, attachment_ids=None):
        event = self.env['calendar.event'].browse(event_id)
        if event:
            # Xóa tất cả tin nhắn có subject "Mô tả lịch quá khứ"
            chatter_messages = event.message_ids.filtered(lambda m: m.subject == "Mô tả đính kèm")
            if chatter_messages:
                chatter_messages.unlink()
            # Đăng message vào Chatter với danh sách file đính kèm
            attachment_ids = attachment_ids or []
            description_past_booking = description_past_booking or ""
            if attachment_ids or description_past_booking:
                event.message_post(
                    body=description_past_booking,
                    subject="Mô tả đính kèm",
                    subtype_xmlid="mail.mt_comment",
                    attachment_ids=attachment_ids
                )

    # Check xem hợp đồng của khách hàng có đúng với domain hợp đồng sau khi thay đổi thông tin của lịch không
    @api.constrains('categ_ids', 'attendee_ids', 'partner_ids', 'service_id', 'exercise_id', 'location_id', 'start', 'stop')
    def _check_valid_contract(self):
        # ko phải lịch PT return luôn k cần check
        if self.event_type != 'pt':
            return
        # Lấy danh sách attendee
        event = self.env['calendar.event'].browse(self.id)
        attendees = self.env['calendar.attendee'].search([('event_id', '=', self.id)])
        attendees_with_contract = attendees.filtered(lambda a: a.contract_id)
        if attendees_with_contract:
            for attendee in attendees_with_contract:
                contract = attendee.contract_id
                partner = attendee.partner_id
                # Check xem hợp đồng đã đặt hết số buổi chưa
                booked_events = self.env['calendar.event'].search([
                    ('event_type', '=', 'pt'),
                    ('state', 'in', ['draft']),
                    ('attendee_ids.partner_id', 'in', [attendee.partner_id.id]),
                ])
                count_booked_events = sum(
                    1 for booked_event in booked_events
                    for att in booked_event.attendee_ids
                    if att.partner_id == partner and att.state != 'declined' and att.contract_id == contract
                )
                possible_sessions = contract.available_session_number + contract.free_session_number - count_booked_events + 1
                if possible_sessions <= 0:
                    raise ValidationError(f"Khách hàng {partner.name} có hợp đồng {contract.name} đã book đủ số buổi trên hợp đồng, không thể tiếp tục đặt lịch")

                valid_contracts = attendee.valid_contract_ids
                if not valid_contracts or contract not in valid_contracts:
                    if event.service_id not in contract.welly_service_ids:
                        raise ValidationError(f'Hợp đồng của khách hàng {attendee.partner_id.name} có Dịch vụ khác Dịch vụ của lịch, không thể đặt lịch.')
                    start = (event.start + timedelta(hours=7)).time()
                    stop = (event.stop + timedelta(hours=7)).time()
                    checkin_time_from = datetime.strptime(contract.welly_service_checkin_time_from, '%H:%M').time()
                    checkin_time_to = datetime.strptime(contract.welly_service_checkin_time_to, '%H:%M').time()
                    if start < checkin_time_from or stop > checkin_time_to:
                        raise ValidationError(f'Hợp đồng của khách hàng {attendee.partner_id.name} đặt lịch ngoài khung giờ của ca tập.')
                    else:
                        raise ValidationError(f'Hợp đồng của khách hàng {attendee.partner_id.name} không đúng với thông tin lịch.')

    # tính tổng khách tham gia
    @api.depends('attendee_ids')
    def _compute_total_attendance(self):
        for event in self:
            event.total_attendance = len(event.attendee_ids)

    # tính tổng khách đã checkin
    @api.depends('attendee_ids', 'attendee_ids.is_checked_in')
    def _compute_took_attendance(self):
        for event in self:
            event.took_attendance = len(event.attendee_ids.filtered(lambda a: a.is_checked_in == True))

    # rule check điều kiện khách hàng đang có trong 1 lịch khác và có trạng thái là accepted và pt_confirm
    @api.constrains('partner_ids', 'start', 'stop')
    def _check_partner_availability(self):
        for event in self:
            if event.event_type == 'pt':
                for partner in event.partner_ids:
                    domain = [
                        ('id', '!=', event.id),
                        ('event_type', '=', 'pt'),
                        ('state', '!=', 'reject'),
                        ('partner_ids', '=', partner.id),
                        '|',
                        '&', ('start', '<', event.stop), ('stop', '>', event.start),
                        '&', ('start', '<', event.stop), ('stop', '>', event.start),
                    ]
                    overlapping_events = self.env['calendar.event'].search(domain)

                    domain_attendee = [
                        ('partner_id', '=', partner.id),
                        ('event_id', 'in', overlapping_events.ids),
                        ('state', 'in', ['accepted', 'await_pt_confirm', 'tentative', 'needsAction'])
                    ]
                    attendees_with_accepted_state = self.env['calendar.attendee'].search(domain_attendee)

                    if attendees_with_accepted_state:
                        raise ValidationError(
                            f'Khách hàng {partner.name} đã được chỉ định cho một sự kiện khác trong khoảng thời gian này!')

    # rule check điều kiện pt đang có trong 1 lịch khác
    @api.constrains('pt_id', 'start', 'stop', 'pt_id_substitute')
    def _check_pt_availability(self):
        for event in self:
            if event.event_type == 'pt' and event.pt_id and event.start and event.stop:
                domain = [
                    ('id', '!=', event.id),
                    ('event_type', '=', 'pt'),
                    ('state', '!=', 'reject'),
                    '|',
                    '&', ('start', '<', event.start), ('stop', '>', event.stop),
                    '&', ('start', '<', event.stop), ('stop', '>', event.start),
                ]
                overlapping_events = self.env['calendar.event'].search(domain)
                if overlapping_events:
                    name_pt = ''
                    for e in overlapping_events:
                        if e.pt_id.id == event.pt_id.id and not event.pt_id_substitute and not e.pt_id_substitute:
                            name_pt = f'{e.pt_id.name}'

                        if e.pt_id_substitute.id == event.pt_id.id and e.pt_id_substitute and not event.pt_id_substitute:
                            name_pt = f'{e.pt_id_substitute.name}'

                        if event.pt_id_substitute and not e.pt_id_substitute and event.pt_id_substitute == e.pt_id:
                            name_pt = f'{e.pt_id.name}'

                        if e.pt_id_substitute.id == event.pt_id_substitute.id and e.pt_id_substitute.id and event.pt_id_substitute.id:
                            name_pt = f'{e.pt_id_substitute.name}'

                        if name_pt == '':
                            continue
                        if name_pt != '':
                            raise ValidationError(
                                f'Thời gian tập không hợp lệ. HLV {name_pt} đã có ca dạy trong khoảng thời gian đã chọn.')

    # rule: Check thời gian book lịch trước tối thiểu và tối đa
    @api.constrains('start', 'stop')
    def _check_event_time_constraint(self):
        # ko phải lịch PT return luôn k cần check
        if self.event_type != 'pt':
            return
        company_id = self.env.company.id
        calendar_config = self.env['welly.calendar.config'].search([('company_id', '=', company_id)], limit=1)
        now = datetime.utcnow()
        if calendar_config:
            for event in self:
                if event.event_type == 'pt':
                    # TH Đặt lịch bình thường
                    if not event.is_past_booking:
                        min_time_booking = calendar_config.allow_time_min_early_booking * 60
                        max_time_booking = calendar_config.allow_time_max_early_booking
                        unit = calendar_config.allow_time_max_early_booking_unit
                        if unit == 'minutes':
                            max_time_booking_to_second = max_time_booking * 60
                        if unit == 'hours':
                            max_time_booking_to_second = max_time_booking * 60 * 60
                        if unit == 'days':
                            max_time_booking_to_second = max_time_booking * 60 * 60 * 24
                        time_difference = (event.start - now).total_seconds()
                        if time_difference < min_time_booking:
                            raise ValidationError(
                                f'Thời gian đặt lịch phải trước ít nhất {int(min_time_booking / 60)} phút.')
                        if time_difference > max_time_booking_to_second:
                            dict = {'minutes': 'phút', 'hours': 'giờ', 'days': 'ngày'}
                            unit_string = dict.get(unit)
                            raise ValidationError(
                                f'Thời gian đặt lịch trước không được quá {max_time_booking} {unit_string}.')
                    # TH Đặt lịch quá khứ: Chỉ đặt lịch trước thời điểm hiện tại và trước 0 -> 7 ngày theo cấu hình
                    else:
                        allow_day = int(calendar_config.allow_time_confirm_past_booking)
                        current_time = fields.Datetime.now() + timedelta(hours=7)
                        start_date = current_time.date() - timedelta(days=allow_day)
                        start_time_compare = datetime(start_date.year, start_date.month, start_date.day, 0, 0, 0)
                        start = event.start + timedelta(hours=7)
                        stop = event.stop + timedelta(hours=7)
                        if not (start_time_compare <= start < stop < current_time):
                            if allow_day == 0:
                                raise ValidationError(f'Chỉ được đặt lịch bù trong ngày và trước thời điểm hiện tại.')
                            else:
                                raise ValidationError(
                                    f'Chỉ được đặt lịch bù muộn {allow_day} ngày.')
    @api.constrains('start', 'stop', 'start_date', 'stop_date')
    def _check_closing_date(self):
        # Đặt múi giờ UTC+7
        tz = pytz.timezone('Asia/Bangkok')
        for meeting in self:
            if not meeting.allday and meeting.start and meeting.stop:
                # Chuyển đổi thời gian sang UTC+7
                start_utc7 = meeting.start.astimezone(tz)
                stop_utc7 = meeting.stop.astimezone(tz)
                if stop_utc7 < start_utc7:
                    raise ValidationError(
                        _('The ending date and time cannot be earlier than the starting date and time.') + '\n' +
                        _("Meeting '%(name)s' starts '%(start_datetime)s' and ends '%(end_datetime)s'",
                          name=meeting.name,
                          start_datetime=start_utc7.strftime('%Y-%m-%d %H:%M:%S'),
                          end_datetime=stop_utc7.strftime('%Y-%m-%d %H:%M:%S')
                          )
                    )
            if meeting.allday and meeting.start_date and meeting.stop_date:
                start_date_utc7 = datetime.combine(meeting.start_date, datetime.min.time()).astimezone(tz).date()
                stop_date_utc7 = datetime.combine(meeting.stop_date, datetime.min.time()).astimezone(tz).date()
                if stop_date_utc7 < start_date_utc7:
                    raise ValidationError(
                        _('The ending date cannot be earlier than the starting date.') + '\n' +
                        _("Meeting '%(name)s' starts '%(start_datetime)s' and ends '%(end_datetime)s'",
                          name=meeting.name,
                          start_datetime=start_date_utc7.strftime('%Y-%m-%d'),
                          end_datetime=stop_date_utc7.strftime('%Y-%m-%d')
                          )
                    )

    # rule: k được sửa lịch khi khác trạng thái nháp
    @api.constrains('start', 'stop')
    def _check_event_not_modify_constraint(self):
        for event in self:
            # convert start, stop sang múi giờ UTC+7
            start = event.start + timedelta(hours=7)
            stop = event.stop + timedelta(hours=7)
            # start và stop phải cùng ngày
            if start.date() != stop.date():
                raise ValidationError('Thời gian bắt đầu và kết thúc phải cùng ngày.')
            if start >= stop:
                raise ValidationError('Thời gian kết thúc phải sau thời gian bắt đầu.')
            if event.event_type == 'pt' and event.state not in ['draft', 'await_confirm']:
                raise ValidationError(
                    f'Chỉ sửa lịch ở trạng thái nháp hoặc chờ xác nhận.')

    # rule:  Số lượng khách hàng - SL khách có trạng thái Hủy < Số lượng người tham gia của hình thức tập
    @api.constrains('partner_ids', 'exercise_id', 'is_merge_class')
    def _check_number_of_participants(self):
        for event in self:
            # Nếu là lịch gộp thì bỏ qua không check
            if event.is_merge_class:
                continue
            if event.event_type == 'pt' and event.exercise_id:
                number_of_participants = len(event.partner_ids)
                number_of_declined = len(event.attendee_ids.filtered(lambda a: a.state == 'declined'))
                if number_of_participants - number_of_declined > event.exercise_id.limit_participants:
                    raise UserError(
                        f'Số lượng người tham gia có trạng thái khác hủy không được vượt quá {event.exercise_id.limit_participants}.')

    # job chuyển đổi trạng thái bắt đầu tập khi đến giờ tập điều kiện có số lượng khách hàng đồng ý join > 0
    @api.model
    def _during_practice_events_scheduled_job(self):
        current_time = fields.Datetime.now()
        try:
            events_to_during_practice = self.env['calendar.event'].search([
                ('is_past_booking', '=', False),
                ('start', '<=', current_time),
                ('state', 'in', ['draft', 'accepted']),
                ('event_type', '=', 'pt')
            ])
            if events_to_during_practice:
                for event in events_to_during_practice:
                    attendees = event.attendee_ids
                    # Lọc KH Chờ KH xác nhận hoặc (Đã xác nhận + Checkin) hoặc đã xác nhận
                    accepted_attendees = attendees.filtered(
                        lambda attendee: (attendee.is_checked_in and attendee.state == 'accepted') or attendee.state in ['tentative', 'accepted', 'during_practice'])
                    attendees_to_decline = attendees.filtered(lambda a: a.state == 'needsAction')
                    if attendees_to_decline:
                        for attendee in attendees_to_decline:
                            attendee.write({'state': 'declined'})
                    if not accepted_attendees or all(attendee.state in ['declined', 'refund_session'] for attendee in attendees):
                        event.write({'state': 'reject'})
                    else:
                        event.write({'state': 'during_practice'})
                        for attendee in accepted_attendees:
                            if attendee.state == 'during_practice':
                                pass
                            elif attendee.is_checked_in:
                                attendee.write({'state': 'during_practice'})
        except Exception as e:
            _logger.error('Error _during_practice_events_scheduled_job: %s', e)

    # job kết thúc buổi tập, lịch đang tập -> hoàn thành + com, khách đang tập -> hoàn thành
    @api.model
    def _confirm_events_scheduled_job(self):
        current_time = fields.Datetime.now()
        try:
            events_to_end = self.env['calendar.event'].search([
                ('is_past_booking', '=', False),
                ('event_type', '=', 'pt'),
                ('state', '=', 'during_practice'),
                ('stop', '<', current_time)
            ])
            if events_to_end:
                for event in events_to_end:
                    # Chuyển trạng thái của khách đang tập sang done, KH chưa checkin -> bỏ tập
                    attendees = event.attendee_ids
                    if attendees:
                        attendees_to_done = attendees.filtered(lambda a: a.state == 'during_practice')
                        attendees_to_skip_session = attendees.filtered(lambda a: a.state in ['accepted', 'tentative', 'needsAction'] and not a.is_checked_in)
                        # RULE: Hết giờ:
                        # Khách đang tập -> Hoàn thành (Tính COM) , lịch -> Hoàn thành
                        # Khách đã xác nhận, chờ KH xác nhận -> Bỏ tập (đồng thời dựa vào cấu hình có tính COM hay không)
                        if attendees_to_done:
                            for attendee in attendees_to_done:
                                attendee.write({'state': 'done', 'confirm_commission': True})
                            event.write({'state': 'done'})
                        if attendees_to_skip_session:
                            is_confirm_com = event.calendar_config_id.is_confirm_commission
                            values = {'state': 'partner_skip_session'}
                            if is_confirm_com:
                                values.update({'confirm_commission': True})
                            for attendee in attendees_to_skip_session:
                                attendee.write(values)

                        # - Tất cả khách (hoàn buổi, hủy) thì lịch -> Hủy
                        # - Tất cả khách (hoàn buổi, bỏ tập, hủy) và có khách bỏ tập thì lịch -> Khách bỏ tập
                        if all(attendee.state in ['refund_session', 'declined'] for attendee in event.attendee_ids):
                            event.write({'state': 'reject'})
                        elif all(attendee.state in ['refund_session', 'partner_skip_session', 'declined'] for
                                 attendee in
                                 event.attendee_ids):
                            event.write({'state': 'partner_skip_session'})

        except Exception as e:
            _logger.error('Error _confirm_events_scheduled_job: %s', e)

    # job đổi trạng thái khách hàng khi hết thời gian checkin theo quy định
    # nếu khách chưa checkin -> chuyển trạng thái sang bỏ tập đồng thời đánh dấu tính COM dựa vào cấu hình
    @api.model
    def _partner_skip_session_scheduled_job(self):
        now = fields.Datetime.now()
        try:
            # Lấy ra các lịch có state = 'during_practice' và event_type = 'pt'
            events = self.env['calendar.event'].search([
                ('is_past_booking', '=', False),
                ('event_type', '=', 'pt'),
                ('state', '=', 'during_practice')
            ])
            if events:
                for event in events:
                    company_id = event.user_id.company_id
                    welly_caledar_config = self.env['welly.calendar.config'].sudo().search([
                        ('company_id', '=', company_id.id)
                    ])
                    valid_time_checkin_to_seconds = 0
                    if welly_caledar_config:
                        valid_time_checkin_to_seconds = welly_caledar_config.allow_time_late_checkin * 60
                    time_checkin_difference_to_seconds = (now - event.start).total_seconds()
                    if time_checkin_difference_to_seconds >= valid_time_checkin_to_seconds:
                        attendees = event.attendee_ids
                        attendees_uncheckin = attendees.filtered(lambda a: not a.is_checked_in and a.state in ['accepted', 'tentative'])
                        if attendees_uncheckin:
                            is_confirm_com = event.calendar_config_id.is_confirm_commission
                            values = {'state': 'partner_skip_session'}
                            if is_confirm_com:
                                values.update({'confirm_commission': True})
                            for attendee in attendees_uncheckin:
                                attendee.write(values)
                    # - Tất cả khách (hoàn buổi, hủy) thì lịch -> Hủy
                    # - Tất cả khách (hoàn buổi, bỏ tập, hủy) và có khách bỏ tập thì lịch -> Khách bỏ tập
                    if event.state == 'during_practice' and event.start <= now < event.stop:
                        if all(attendee.state in ['refund_session', 'declined'] for attendee in event.attendee_ids):
                            event.write({'state': 'reject'})
                        elif all(attendee.state in ['refund_session', 'partner_skip_session', 'declined'] for attendee in
                                 event.attendee_ids):
                            event.write({'state': 'partner_skip_session'})

        except Exception as e:
            _logger.error('Error _partner_skip_session_scheduled_job: %s', e)

    # job đổi trạng thái của khách và huỷ lịch trường hợp khi pt không xác nhận
    @api.model
    def _reject_event_if_pt_not_confirm_scheduled_job(self):
        now = fields.Datetime.now()
        try:
            events = self.env['calendar.event'].search([
                ('is_past_booking', '=', False),
                ('event_type', '=', 'pt'),
                ('state', '!=', 'reject'),
                ('start', '<=', now)
            ])
            if events:
                for event in events:
                    attendees = event.attendee_ids
                    attendees_await_pt_confirm = attendees.filtered(lambda a: a.state == 'await_pt_confirm')
                    if attendees_await_pt_confirm:
                        for attendee in attendees_await_pt_confirm:
                            attendee.write({'state': 'declined'})
                    if all(attendee.state in ['declined', 'refund_session'] for attendee in attendees):
                        event.write({'state': 'reject'})

        except Exception as e:
            _logger.error('Error _reject_event_if_pt_not_confirm_scheduled_job: %s', e)

    #job trừ buổi của khách hàng có trạng thái bỏ tập và đang tập
    @api.model
    def _update_session_number_scheduled_job(self):
        try:
            attendees_deduct_session = self.env['calendar.attendee'].search([
                ('state', 'in', ['during_practice', 'done', 'partner_skip_session']),
                ('is_deducted_session', '=', False)
            ])
                    
            if attendees_deduct_session:
                # trừ buổi tập trong hợp đồng
                # điều kiện nhiều người tham dự cùng có 1 hợp đồng
                # gán biến để kiểm tra xem hợp đồng này đã bị trừ buổi trước đó chưa
                contract_id_check_exit = None
                for attendee in attendees_deduct_session:
                    # Check xem nếu khách bỏ tập và ko trừ buổi thì bỏ qua không trừ buổi
                    event = attendee.event_id
                    if not event or (attendee.state == 'partner_skip_session' and not event.is_deduct_session_canceled):
                        continue
                    contract = attendee.contract_id
                    # kiểm tra id của hợp đồng xem đã bị trừ buổi trước đó chưa
                    if contract and contract_id_check_exit != contract.id:
                        is_pilates = event.service_id.name == 'Pilates'
                        delete_main_session_first = False
                        # Với dịch vụ pilates ưu tiên trừ buổi chính trước nếu:
                        # Lịch có huấn luyện viên dạy thay
                        # Hoặc Lịch không có huấn luyện viên dạy thay + HLV chính khác nv kinh doanh và pt hỗ trợ trên hợp đồng
                        if is_pilates:
                            if event.pt_id_substitute or (event.pt_id != contract.marketing_staff_id and event.pt_id != contract.pt_staff_id and not event.pt_id_substitute):
                                delete_main_session_first = True

                        if delete_main_session_first:
                            if contract.available_session_number > 0:
                                    contract.available_session_number -= 1
                                    attendee.write({'main_session_number': str(contract.session_number - contract.available_session_number),
                                                    'gift_session_number': False})
                                    attendee._log_activities(event_id=event.id,
                                                             message=f"Hợp đồng {contract.name} của khách hàng {attendee.partner_id.name} đã trừ buổi chính")
                            elif contract.free_session_number > 0:
                                contract.free_session_number -= 1
                                message = f"Hợp đồng {contract.name} của khách hàng {attendee.partner_id.name} đã trừ buổi tặng"
                                # Lọc danh sách detail còn buổi tặng
                                details = contract.detail_ids.filtered(lambda d: d.gift_session_number > 0)
                                # Sắp xếp theo gift_commission giảm dần, sau đó theo id tăng dần
                                sorted_details = details.sorted(key=lambda d: (d.gift_commission, d.id))
                                if sorted_details:
                                    detail_with_max_commission = sorted_details[0]
                                    detail_with_max_commission.gift_session_number -= 1
                                    attendee.write({'gift_session_number': str(contract.total_free_session_number - contract.free_session_number),
                                                    'gift_session_commission': detail_with_max_commission.gift_commission,
                                                    'main_session_number': False})
                                    message += f" {detail_with_max_commission.gift_name}"

                                attendee._log_activities(event_id=event.id, message=message)
                        else:
                            if contract.free_session_number > 0:
                                contract.free_session_number -= 1
                                attendee_update_values = {'gift_session_number': str(contract.total_free_session_number - contract.free_session_number)}
                                message = f"Hợp đồng {contract.name} của khách hàng {attendee.partner_id.name} đã trừ buổi quà tặng"
                                # Lọc danh sách detail còn buổi tặng
                                details = contract.detail_ids.filtered(lambda d: d.gift_session_number > 0)
                                # Sắp xếp theo gift_commission giảm dần, sau đó theo id tăng dần
                                sorted_details = details.sorted(key=lambda d: (d.gift_commission, d.id))
                                if sorted_details:
                                    detail_with_max_commission = sorted_details[0]
                                    detail_with_max_commission.gift_session_number -= 1
                                    attendee.write({'gift_session_number': str(contract.total_free_session_number - contract.free_session_number),
                                                    'gift_session_commission': detail_with_max_commission.gift_commission,
                                                    'main_session_number': False})
                                    message += f" {detail_with_max_commission.gift_name}"

                                attendee._log_activities(event_id=event.id, message=message)
                            elif contract.available_session_number > 0:
                                    contract.available_session_number -= 1
                                    attendee.write({'main_session_number': str(contract.session_number - contract.available_session_number),
                                                    'gift_session_number': False})
                                    attendee._log_activities(event_id=event.id,
                                                             message=f"Hợp đồng {contract.name} của khách hàng {attendee.partner_id.name} đã trừ buổi chính")
                        # tính lại buổi tập số bao nhiêu trong attendee
                        total_ss = contract.session_number + contract.total_free_session_number
                        available_session_number = contract.available_session_number + contract.free_session_number
                        attendee.session_number = total_ss - available_session_number

                        # Đánh dấu attendee đã được trừ buổi
                        attendee.is_deducted_session = True
                    contract_id_check_exit = contract.id
        except Exception as e:
            _logger.error('Error _update_session_number_scheduled_job: %s', e)

    # PUBLIC FUNTION
    """Kiểm tra khách hàng khi check-in có trong thời gian của lịch tập không. Có 3 trường hợp lịch hợp lệ:
        - Trước thời gian diễn ra lịch tập trong cài đặt: allow_time_early_checkin (check-in sớm)
        - Sau thời gian bắt đầu lịch nhưng có thời gian allow_time_late_checkin đủ điều kiện (check-in muộn)
        - Sau thời gian kết thúc lịch tập nhưng có thời gian allow_time_checkin_after_stop đủ điều kiện (check-in bù)
        Đồng thời xử lý việc update trạng thái của Khách hàng (attendee) trong lịch (event)
    *   Request có trường is_update để đánh dấu thao tác này có thể update trạng thái của Khách hàng trong lịch.
    =>  Response trả ra là dict sẽ bao gồm các thông tin:
        - Lịch Tập (calendar)
        - Thông tin tất cả Hợp đồng (contract)
        - Id của Hợp đồng(contract_id_checkin)
        - Tên của hợp đồng(description_checkin) """
    @api.model
    def check_booking(self, partner_id: int, location_id: int, is_update=None):
        calendar_checkin = None
        time = fields.Datetime.now()
        company_id = self.env.company.id
        calendar_config = self.env['welly.calendar.config'].search([
            ('company_id', '=', company_id)
        ], limit=1)
        allow_time_early_checkin = 30
        allow_time_late_checkin = 0
        allow_time_checkin_after_stop = 0
        if calendar_config:
            allow_time_early_checkin = calendar_config.allow_time_early_checkin
            allow_time_late_checkin = calendar_config.allow_time_late_checkin
            allow_time_checkin_after_stop = calendar_config.allow_time_checkin_after_stop
        # tìm lịch chưa đến giờ tập hoặc check-in muộn
        domain = [
            ('partner_id', '=', partner_id),
            ('event_id.stop', '>', time),
            ('event_id.start', '<=', time + timedelta(minutes=allow_time_early_checkin)),
            ('event_id.start', '>=', time - timedelta(minutes=allow_time_late_checkin)),
            ('state', 'in', ['await_pt_confirm', 'accepted', 'tentative', 'pt_confirm'])
        ]
        attendee_checkin = None
        attendee_checkin_records = self.env['calendar.attendee'].sudo().search(domain)
        if attendee_checkin_records:
            attendee_checkin = attendee_checkin_records.sorted(key=lambda r: r.event_id.start)[0]

        if not attendee_checkin:
            # tìm lịch check-in bù sau giờ tập
            retro_checkin_domain = [
                ('partner_id', '=', partner_id),
                ('event_id.stop', '>=', time - timedelta(minutes=allow_time_checkin_after_stop)),
                ('event_id.stop', '<', time),
                '|',
                ('state', '=', ['partner_skip_session', 'tentative']),
                '&',
                ('state', '=', 'done'),
                ('type_checkin', '=', 'reception_checkin')
            ]
            attendees_checkin = self.env['calendar.attendee'].sudo().search(retro_checkin_domain)
            if attendees_checkin:
                attendee_checkin = attendees_checkin.sorted(key=lambda r: r.event_id.stop, reverse=True)[0]
            else:
                # tìm lịch đang tập
                domain_during_practice = [
                    ('partner_id', '=', partner_id),
                    ('event_id.state', '=', 'during_practice'),
                    ('event_id.stop', '>=', time),
                    ('state', 'in', ['during_practice'])
                ]
                attendee_checkin = self.env['calendar.attendee'].sudo().search(domain_during_practice, limit=1)

        if attendee_checkin:
            event_checkin = attendee_checkin.event_id
            if location_id != event_checkin.location_id.id:
                pass
            else:
                contract_checkin = attendee_checkin.contract_id
                if not contract_checkin:
                    pass
                else:
                    coach_checkin = event_checkin.pt_id
                    base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
                    image = self.env['ir.attachment'].sudo().search(
                        [('res_model', '=', 'res.partner'), ('res_id', '=', coach_checkin.partner_id.id),
                         ('res_field', '=', 'image_128')], limit=1)
                    image_url_128 = ""
                    if image:
                        access_token = image.generate_access_token()[0]
                        image_url_128 = f"{base_url}/web/image/{image.id}/image_128?access_token={access_token}"
                    calendar_checkin = {'attendee_id': attendee_checkin.id,
                                        'calendar_id': event_checkin.id,
                                        'attendee_session_number': attendee_checkin.session_number,
                                        'calendar_start': self.convert_date_time_format(
                                            str(event_checkin.start + timedelta(hours=7))),
                                        'calendar_stop': self.convert_date_time_format(
                                            str(event_checkin.stop + timedelta(hours=7))),
                                        'calendar_name': contract_checkin.sale_order_template_name_print,
                                        'calendar_exercise_form_name': contract_checkin.exercise_form_id.name,
                                        'contract_total_session_number': contract_checkin.session_number,
                                        'contract_available_session_number': contract_checkin.available_session_number,
                                        'contract_date_start': self.convert_date_format(
                                            str(contract_checkin.date_start)),
                                        'contract_date_end': self.convert_date_format(str(contract_checkin.date_end)),
                                        'coach_name': coach_checkin.name,
                                        'coach_avatar': image_url_128,
                                        'contract_image': None,
                                        'contract_id': contract_checkin.id,
                                        'total_available_session_number': contract_checkin.available_session_number + contract_checkin.free_session_number
                                        }

        contract_records = self.env['welly.contract'].sudo().search_read(
            domain=[('partner_account_ids', 'in', int(partner_id)),
                    ('welly_location_many2_many', 'in', int(location_id)),
                    '|',
                    ('state', '=', 'activated'),
                    ('is_installment_activated', '=', True),
                    ('date_end', '>', fields.Datetime.now())],
            fields=['id', 'sale_order_template_name_print', 'available_session_number', 'exercise_form_id',
                    'session_number',
                    'free_session_number', 'date_start', 'date_end', 'service_type', 'name', 'welly_service_checkin_time_from', 'welly_service_checkin_time_to']
        )
        for record in contract_records:
            record['contract_name'] = record['name']
            record['name'] = record.pop('sale_order_template_name_print', None)
            record['exercise_form_name'] = record['exercise_form_id'][1] if record['exercise_form_id'] else None
            record.pop('exercise_form_id', None)
            record['contract_image'] = None
            date_start = record['date_start']
            date_end = record['date_end']
            record['date_start'] = self.convert_date_format(str(date_start))
            record['date_end'] = self.convert_date_format(str(date_end))
            if record['welly_service_checkin_time_from'] and record['welly_service_checkin_time_to']:
                today = date.today()
                tz = timezone(timedelta(hours=7))
                # Lấy thời gian bắt đầu check-in từ cấu hình
                time_checkin_start = datetime.combine(today, datetime.strptime(record['welly_service_checkin_time_from'], "%H:%M").time(), tz).time()
                time_checkin_end = datetime.combine(today, datetime.strptime(record['welly_service_checkin_time_to'], "%H:%M").time(), tz).time()
                # Tính khoảng cách thời gian hiện tại so với thời gian bắt đầu check-in
                current_time = datetime.now(tz).time()
                time_checkin_start_seconds = time_checkin_start.hour * 3600 + time_checkin_start.minute * 60 + time_checkin_start.second
                time_checkin_end_seconds = time_checkin_end.hour * 3600 + time_checkin_end.minute * 60 + time_checkin_end.second
                current_time_seconds = current_time.hour * 3600 + current_time.minute * 60 + current_time.second

                delta_start = current_time_seconds - time_checkin_start_seconds
                delta_end = time_checkin_end_seconds - current_time_seconds
                record['delta_time_checkin'] = min([delta_start, delta_end])
            else:
                record['delta_time_checkin'] = -1_000_000

        description_checkin = None
        contract_id_checkin = None
        if calendar_checkin:
            description_checkin = calendar_checkin['calendar_name']
            contract_id_checkin = calendar_checkin['contract_id']
            # ghi nhận khách đã check-in
            attendee_id = calendar_checkin['attendee_id']
            if attendee_id:
                attendee = self.env['calendar.attendee'].sudo().browse(int(attendee_id))
                event_checkin = attendee.event_id
                # Tính chênh lệch thời gian checkin và thời gian bắt đầu lịch tập, thời gian checkin hợp lệ của hệ thống
                time_checkin_difference_by_seconds = (
                        event_checkin.start - time).total_seconds()  # chuyển đổi timedelta thành giây
                # Giá trị thời gian cho phép checkin trước event_start
                allow_time_early_checkin_by_seconds = allow_time_early_checkin * 60
                # Giá trị thời gian cho phép checkin sau event_start
                allow_time_late_checkin_by_seconds = allow_time_late_checkin * 60
                if is_update:
                    data = {}
                    if not attendee.is_checked_in:
                        data.update({'is_checked_in': True})
                    if not attendee.time_checkin:
                        data.update({'time_checkin': time})
                    if not attendee.type_checkin or attendee.type_checkin != 'customer_checkin':
                        data.update({'type_checkin': 'customer_checkin'})
                    # Trường hợp checkin trước khi ca tập diễn ra
                    if 0 < time_checkin_difference_by_seconds <= allow_time_early_checkin_by_seconds:
                        if attendee.state in ['tentative', 'accepted']:
                            data.update({'state': 'accepted'})
                    # Trường hợp checkin sau khi qua thời gian ca tập bắt đầu
                    elif 0 > time_checkin_difference_by_seconds >= -allow_time_late_checkin_by_seconds and event_checkin.stop >= time:
                        if event_checkin.state == 'draft':
                            data.update({'state': 'accepted'})
                        elif attendee.state != 'during_practice':
                            data.update({'state': 'during_practice'})
                    # Trường hợp checkin sau khi ca tập kết thúc và trong khoảng thời gian được phép checkin bù
                    elif event_checkin.stop < time <= event_checkin.stop + timedelta(minutes=allow_time_checkin_after_stop):
                        if attendee.state in ['partner_skip_session', 'tentative']:
                            data.update({'state': 'done', 'confirm_commission': True})
                        if event_checkin.state == 'partner_skip_session':
                            event_checkin.with_user(1).write({'state': 'done'})
                    if data:
                        attendee.with_user(1).write(data)
        else:
            if contract_records:
                contract_sort = sorted(contract_records, key=lambda x: x['delta_time_checkin'], reverse=True)
                target_contract = next(
                    (contract for contract in contract_sort if contract.get("service_type") == "member"),
                    None)
                if target_contract:
                    description_checkin = target_contract['name']
                    contract_id_checkin = target_contract['id']
        rs = {'calendar': calendar_checkin, 'contract': contract_records, 'description_checkin': description_checkin,
              'contract_id_checkin': contract_id_checkin}
        return rs

    # fnc tính commission cho pt
    @api.model
    def add_commission_for_pt(self, id: int):
        events = self.browse(id)
        events.write({'state': 'done'})
        # todo + commission cho pt

    @api.model
    def reject_event(self, id: int):
        events = self.browse(id)
        if events.event_type == 'pt' and events.state == 'during_practice' or events.state == 'done':
            if not events.description:
                raise ValidationError(
                    'Description is required for Action Reject Event.')
        # todo reject commission của pt với khách hàng đó

    @api.model
    def check_quyen_le_tan(self):
        user_id = self.env.user.id
        # logic check xem user thao tác có phải đúng là lễ tân của lịch này không
        user = self.env.user
        # lấy quyền lễ tân
        if not user.has_group('welly_base.group_receptionist'):
            return True
        else:
            return True

    @api.model
    def create_calendar_pt(self, partner_id, start, stop, description, session_type, pt_id, exercise_id, service_id, location_id,
                           contract_id):
        try:
            location = self.env['welly.location'].browse(int(location_id))
            partner = self.env['res.partner'].browse(int(partner_id))
            event = self.create({
                'name': f'Lịch HV: {partner.name}',
                'partner_ids': [(4, int(partner_id))],
                'start': start,
                'stop': stop,
                'notes': description,
                'session_type': session_type,
                'event_type': 'pt',
                'pt_id': pt_id,
                'exercise_id': exercise_id,
                'service_id': service_id,
                'location_id': location_id,
                'contract_id': contract_id,
                'company_id': location.company_id.id,
                'is_app_booking': True                  #Đánh dấu lịch do app đặt
            })
            return event
        except Exception as e:
            self.env.cr.rollback()
            raise ValidationError(str(e))

    # Override function unlink: Chỉ cho phép xóa các lịch ở trạng thái " Nháp"
    # User có role= quản trị viên, quản lý PT Hoặc PT đã tạo booking đó
    def unlink(self):
        for rec in self:
            if rec.event_type == 'pt':
                # get ra user thao tác
                user = self.env.user
                user_create = rec.user_id if rec.user_id.id != 4 else rec.pt_id
                if user != user_create or (
                        not user.has_group('welly_base.group_pt_manager') and not user.has_group(
                    'base.group_system') and not user.has_group('welly_base.group_pt')):
                    raise ValidationError('Chỉ quản trị viên, quản lý PT hoặc PT đã tạo booking mới được xóa.')
                if rec.state != 'draft':
                    raise ValidationError("Đối với lịch PT Chỉ cho phép xóa các ở trạng thái 'Nháp'")
                attendees = rec.attendee_ids
                if any(att.state == 'accepted' for att in attendees):
                    raise ValidationError('Lịch đã có khách ở trạng thái xác nhận, không thể xóa')
                # Gửi thông báo cho KH qua firebase
                service_name = f"{rec.service_id.name} " if rec.service_id.name else ""
                title = 'Lịch tập đã bị xóa'
                content = f'Lớp học {service_name}vào {self._convert_date_format(str(rec.start + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")} đến {self._convert_date_format(str(rec.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")}' \
                          f' {self._convert_date_format(str(rec.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%d/%m/%Y")}'
                notification_q = self.env['notification.queue'].sudo()
                for attendee in attendees:
                    partner_id = attendee.partner_id.id
                    # Tạo thông báo
                    noti_to_attendee = notification_q.create({
                        'partner_id': partner_id,
                        'title': title,
                        'content_summary': content,
                        'type': 'readonly',
                    })
                    # Gửi thông báo cho khách hàng qua firebase
                    notification_q.send_notification_to_firebase(noti_to_attendee)
        return super().unlink()

    def convert_date_time_format(self, input_date):
        # Chuyển đổi định dạng từ chuỗi sang đối tượng datetime
        datetime_object = datetime.strptime(input_date, "%Y-%m-%d %H:%M:%S")

        # Chuyển đổi đối tượng datetime thành chuỗi mới với định dạng mong muốn
        formatted_date = datetime_object.strftime("%H:%M:%S %d-%m-%Y")

        return formatted_date

    def convert_date_format(self, input_date):
        # Chuyển đổi định dạng từ chuỗi sang đối tượng datetime
        datetime_object = datetime.strptime(input_date, "%Y-%m-%d")

        # Chuyển đổi đối tượng datetime thành chuỗi mới với định dạng mong muốn
        formatted_date = datetime_object.strftime("%d-%m-%Y")

        return formatted_date

    def compare_lists(self, old, new):
        old_set = set(old)
        new_set = set(new)

        # Tìm các phần tử bị xóa
        removed_ids = old_set - new_set
        # Tìm các phần tử được thêm vào
        added_ids = new_set - old_set

        # Lấy tên người dùng từ các ID
        if removed_ids:
            removed_users = self.env['res.partner'].browse(list(removed_ids)).mapped('name')
        if added_ids:
            added_users = self.env['res.partner'].browse(list(added_ids)).mapped('name')

        return {
            'removed': removed_users if removed_ids else [],
            'added': added_users if added_ids else []
        }

    def _log_activities(self, event_id: int, message):
        # cập nhật log activities
        self.env['mail.message'].create({
            'model': 'calendar.event',
            'res_id': event_id,
            'message_type': 'comment',
            'body': message,
        })

    # ví dụ: input_date: str time; original_format = "%Y-%m-%d"; destination_format= "%d-%m-%Y"
    def _convert_date_format(self, input_date, original_format, destination_format):
        datetime_object = datetime.strptime(input_date, original_format)
        formatted_date = datetime_object.strftime(destination_format)
        return formatted_date

    @api.model
    def _get_last_month_domain(self):
        # Lấy thời gian hiện tại theo UTC
        today = datetime.utcnow()

        # Tính toán ngày đầu tiên của tháng hiện tại và trừ đi 1 tháng
        first_day_of_this_month = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        first_day_of_last_month_utc = (first_day_of_this_month - relativedelta(months=1) - relativedelta(hours=7))

        # Tính toán ngày cuối cùng của tháng trước
        last_day_of_last_month_utc = first_day_of_this_month - timedelta(seconds=1) - timedelta(hours=7)

        # Trả về domain để lọc các bản ghi trong khoảng thời gian này
        return [('&'),
                ('start', '>=', first_day_of_last_month_utc.strftime('%Y-%m-%d %H:%M:%S')),
                ('start', '<=', last_day_of_last_month_utc.strftime('%Y-%m-%d %H:%M:%S'))]

    @api.model
    def _get_current_month_domain(self):
        # Lấy thời gian hiện tại theo UTC
        today = datetime.utcnow()

        # Tính toán ngày đầu tiên của tháng hiện tại
        first_day_of_this_month = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        first_day_of_this_month_utc = first_day_of_this_month - relativedelta(hours=7)

        # Tính toán ngày đầu tiên của tháng sau
        first_day_of_next_month = (first_day_of_this_month + relativedelta(months=1)).replace(day=1)
        first_day_of_next_month_utc = first_day_of_next_month - relativedelta(hours=7)

        # Ngày cuối cùng của tháng này là một ngày trước ngày đầu tiên của tháng sau
        last_day_of_this_month_utc = first_day_of_next_month_utc - relativedelta(seconds=1)

        # Trả về domain để lọc các bản ghi trong khoảng thời gian này
        return [('&'),
                ('start', '>=', first_day_of_this_month_utc.strftime('%Y-%m-%d %H:%M:%S')),
                ('start', '<=', last_day_of_this_month_utc.strftime('%Y-%m-%d %H:%M:%S'))]


    # override hàm searh để thêm bộ lọc tùy chỉnh
    @api.model
    def search(self, domain, offset=0, limit=None, order=None, count=False):
        # Kiểm tra nếu domain chứa 'last_month'
        for index, arg in enumerate(domain):
            if isinstance(arg, (list, tuple)) and len(arg) == 3 and arg[0] == 'start' and arg[2] == 'last_month':
                # Thay thế với domain tháng trước
                domain[index:index + 1] = self._get_last_month_domain()
            if isinstance(arg, (list, tuple)) and len(arg) == 3 and arg[0] == 'start' and arg[2] == 'current_month':
                # Thay thế với domain tháng trước
                domain[index:index + 1] = self._get_current_month_domain()
        return super(CalendarEvent, self).search(domain, offset, limit, order, count)

    @api.model
    def read_group(self, domain, fields, groupby, offset=0, limit=None, orderby=False, lazy=True):
        # Kiểm tra nếu domain chứa 'last_month'
        for index, arg in enumerate(domain):
            if isinstance(arg, (list, tuple)) and len(arg) == 3 and arg[0] == 'start' and arg[2] == 'last_month':
                # Thay thế với domain tháng trước
                domain[index:index + 1] = self._get_last_month_domain()
            if isinstance(arg, (list, tuple)) and len(arg) == 3 and arg[0] == 'start' and arg[2] == 'current_month':
                # Thay thế với domain tháng trước
                domain[index:index + 1] = self._get_current_month_domain()
        return super(CalendarEvent, self).read_group(domain, fields, groupby, offset, limit, orderby, lazy)

    def action_confirm_past_booking(self):
        for event in self:
            allow_day = int(event.calendar_config_id.allow_time_confirm_past_booking)
            now = fields.Datetime.now() + timedelta(hours=7)
            create_date = event.create_date + timedelta(hours=7)
            allow_date = create_date.date() + timedelta(days=allow_day)
            allow_time = datetime(allow_date.year, allow_date.month, allow_date.day, 23, 59, 59)
            if now > allow_time:
                if allow_day == 0:
                    raise ValidationError('Chỉ được xác nhận lịch bù trong ngày')
                else:
                    raise ValidationError(f'Chỉ được xác nhận lịch bù sau {allow_day} ngày từ khi tạo')

            if event.event_type == 'pt' and event.is_past_booking and event.state in ['await_confirm', 'draft']:
                for att in event.attendee_ids:
                    att.write({'state': 'done', 'is_checked_in': True, 'type_checkin': 'reception_checkin', 'confirm_commission': True})
                event.write({'state': 'done'})

    def action_decline_past_booking(self):
        for event in self:
            if event.event_type == 'pt' and event.is_past_booking and event.state == 'await_confirm':
                event.write({'state': 'draft'})

    # Ghi đè function lưu trữ để của lịch để lưu trữ cả attendee đi kèm
    # Chỉ được lưu trữ lịch PT ở trạng thái Nháp hoặc Hủy
    def action_archive(self):
        for rec in self:
            if rec.event_type == 'pt':
                if rec.state not in ['draft', 'reject']:
                    raise ValidationError('Chỉ cho phép lưu trữ lịch PT ở trạng thái Nháp hoặc Hủy')
            if rec.attendee_ids:
                rec.attendee_ids.action_archive()
        return super(CalendarEvent, self).action_archive()
