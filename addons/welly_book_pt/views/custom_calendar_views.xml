<odoo>
    <record id="view_calendar_event_form_inherited" model="ir.ui.view">
        <field name="name">calendar.event.form.inherit</field>
        <field name="model">calendar.event</field>
        <field name="inherit_id" ref="calendar.view_calendar_event_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='page_invitations']" position="attributes">
                <attribute name="groups"/>
            </xpath>
            <xpath expr="//page[@name='page_invitations']" position="after">
                <page string="Đính kèm"
                      name="page_attachments"
                      attrs="{'invisible': [('event_type', '!=', 'pt')]}"
                >
                    <group>
                        <field name="attachment_ids"
                               widget="many2many_binary_sort"
                               string="Tệp đính kèm"/>
                    </group>
                    <group>
                        <field name="description_past_booking"
                               string="Mô tả"/>
                    </group>
                </page>
            </xpath>
            <xpath expr="//button[@name='action_sendmail']" position="attributes">
                <attribute name="attrs">{'invisible': [('event_type', '=', 'pt')]}</attribute>
            </xpath>
            <xpath expr="//page[@name='page_details']" position="attributes">
                <attribute name="attrs">{'invisible': True}</attribute>
            </xpath>
            <xpath expr="//page[@name='page_options']" position="attributes">
                <attribute name="attrs">{'invisible': True}</attribute>
            </xpath>
            <xpath expr="//sheet//div[@class='d-flex align-items-baseline']" position="attributes">
                <attribute name="attrs">{'invisible': [('event_type', '=', 'pt')]}</attribute>
            </xpath>
            <xpath expr="//field[@name='name']" position="attributes">
                <attribute name="attrs">{'readonly': [('event_type', '==', 'pt'), ('state', 'not in', ['draft', 'await_confirm'])]}</attribute>
            </xpath>
            <xpath expr="//div[@class='alert alert-warning o_form_header mt-2']" position="after">
                <group attrs="{'invisible': [('event_type', '!=', 'calendar')]}">
                    <group>
                        <group>
                            <field name="start_date" string="Bắt đầu vào"
                                   attrs="{'required': [('allday','=',True)], 'invisible': [('allday','=',False)]}"
                                   force_save="1"/>
                            <field name="stop_date" string="Kết thúc vào"
                                   attrs="{'required': [('allday','=',True)],'invisible': [('allday','=',False)]}"
                                   force_save="1"/>

                            <field name="start" string="Bắt đầu vào"
                                   attrs="{'required': [('allday','=',False)], 'invisible': [('allday','=',True)]}"/>
                            <field name="stop" string="Kết thúc vào" attrs="{'invisible': [('allday','=',True)]}"/>
                            <label for="duration" attrs="{'invisible': [('allday','=',True)]}"/>
                            <div attrs="{'invisible': [('allday','=',True)]}">
                                <field name="duration" widget="float_time" string="Thời lượng" class="oe_inline"
                                       attrs="{'readonly': [('id', '!=', False), ('recurrency','=',True)]}"/>
                                <span>giờ</span>
                            </div>
                            <field name="event_tz" attrs="{'invisible': [('recurrency', '=', False)]}"/>
                            <field name="user_id" widget="many2one_avatar_user"/>
                        </group>
                        <group>
                            <field name="alarm_ids" widget="many2many_tags" options="{'no_quick_create': True}"/>
                            <field name="location"/>
                            <field name="state" readonly="1"/>
                        </group>
                    </group>
                    <group>
                        <field name="description"/>
                    </group>
                </group>
            </xpath>
            <xpath expr="//div[@class='oe_title mb-3']" position="after">

                <group attrs="{'invisible': [('event_type', '!=', 'pt')]}">
                    <group>
                        <field name="exercise_id" string="Hình thức tập"
                               attrs="{'required': [('event_type', '=', 'pt')],
                                       'readonly': [('event_type', '=', 'pt'), ('state', 'not in', ['draft', 'await_confirm'])]}"/>
                        <field name="service_id" string="Dịch vụ"
                               attrs="{'required': [('event_type', '=', 'pt')],
                                       'readonly': [('event_type', '=', 'pt'), ('state', 'not in', ['draft', 'await_confirm'])]}"/>
                        <field name="location_id" string="Địa điểm trung tâm"
                               attrs="{'required': [('event_type', '=', 'pt')],
                                       'readonly': [('state', 'not in', ['draft', 'await_confirm'])],
                                       'invisible':[('event_type', '!=', 'pt')]}"/>
                        <field name="start_date"
                               attrs="{'required': [('allday', '=', True)],
                                       'invisible': [('allday', '=', False)],
                                       'readonly': [('event_type', '==', 'pt'), ('state', 'not in', ['draft', 'await_confirm'])]}"/>
                        <field name="stop_date"
                               attrs="{'required': [('allday', '=', True)],
                                       'invisible': [('allday', '=', False)],
                                       'readonly': [('event_type', '==', 'pt'), ('state', 'not in', ['draft', 'await_confirm'])]}"/>
                        <field name="start" string="Bắt đầu vào"
                               attrs="{'required': [('allday', '=', False)],
                                       'invisible': [('allday', '=', True)],
                                       'readonly': [('event_type', '==', 'pt'), ('state', 'not in', ['draft', 'await_confirm'])]}"/>
                        <field name="stop" string="Kết thúc vào"
                               attrs="{'invisible': [('allday', '=', True)],
                                       'readonly': [('event_type', '==', 'pt'), ('state', 'not in', ['draft', 'await_confirm'])]}"/>
                        <field name="duration"
                               widget="float_time"
                               attrs="{'readonly': [('event_type', '=', 'pt'), ('state', 'not in', ['draft', 'await_confirm'])],
                                           'required': [('event_type', '=', 'pt')]}"/>
                        <field name="user_id" string="Người tạo"
                               attrs="{'required': [('event_type', '=', 'pt')],
                                       'readonly': [('event_type', '=', 'pt'), ('state', 'not in', ['draft', 'await_confirm'])]}"/>
                    </group>

                    <group>
                        <field name="pt_id" string="PT chính"
                               attrs="{'required': [('event_type', '=', 'pt')],
                                       'readonly': [('event_type', '=', 'pt'), ('state', 'not in', ['draft', 'await_confirm'])]}"/>
                        <field name="pt_id_substitute" string="PT dạy thay"
                               attrs="{'readonly': [('event_type', '=', 'pt'), ('state', 'not in', ['draft', 'await_confirm'])]}"/>
                        <field name="alarm_ids" widget="many2many_tags" string="Nhắc nhở"
                               attrs="{'readonly': [('event_type', '==', 'pt'), ('state', 'not in', ['draft', 'await_confirm'])]}"/>
                        <field name="categ_ids" widget="many2many_tags" string="Thẻ"
                               attrs="{'readonly': [('event_type', '==', 'pt'), ('state', 'not in', ['draft', 'await_confirm'])]}"/>
                    </group>
                </group>
                <!--Người tham dự-->
                <group>
                    <field name="partner_ids"
                           class="oe_inline o_calendar_attendees"
                           attrs="{
                               'readonly': [
                                   ('event_type', '==', 'pt'), 
                                   '|', 
                                   '&amp;', ('is_merge_class', '=', False), ('state', 'not in', ['draft', 'await_confirm']),
                                   '&amp;', ('is_merge_class', '=', True), ('state', 'not in', ['draft', 'await_confirm', 'accepted', 'during_practice'])
                               ],
                               'required': [('event_type', '=', 'pt')],
                               'invisible': [('event_type', '!=', 'pt')]
                           }"
                           widget="many2manyattendee"
                           string="Danh sách khách hàng"
                    />
                </group>
                <!--Ghi chú-->
                <group>
                    <field name="can_edit_notes" invisible="1"/>
                    <field name="notes"
                           attrs="{
                               'invisible': [('event_type', '!=', 'pt')],
                               'readonly': [('can_edit_notes', '=', False)]}"
                    />
                </group>
            </xpath>

            <xpath expr="//field[@name='categ_ids']" position="after">
                <field name="session_type" string="Loại lớp"
                       attrs="{'readonly': [('event_type', '=', 'pt'), ('state', 'not in', ['draft', 'await_confirm'])],
                               'invisible': [('event_type', '!=', 'pt')]}"/>
                <field name="event_type" invisible="1"/>
                <label for="took_attendance" string="Checked-In"
                       attrs="{'invisible': [('event_type', '!=', 'pt')]}"/>
                <div class="o_row" attrs="{'invisible': [('event_type', '!=', 'pt')]}">
                    <div>
                        <field name="took_attendance" class="oe_inline" colspan="2"/>
                        /
                        <field name="total_attendance" class="oe_inline ms-2" colspan="2"/>
                    </div>
                </div>
                <field name="state" string="Trạng thái"
                       attrs="{'readonly': [('event_type', '==', 'pt')]}"/>
                <label for="is_past_booking"
                       class="o_inline"
                       string="Đặt lịch trong quá khứ"
                       attrs="{'invisible': [('event_type', '!=', 'pt')]}"/>
                <div class="o_row">
                    <field name="is_past_booking"
                           string="Đặt lịch trong quá khứ"
                           attrs="{'invisible': [('event_type', '!=', 'pt')],
                                   'readonly': [('id', '!=', False)]}" />
                    <button name="action_confirm_past_booking"
                            icon="fa-check-square"
                            string="XÁC NHẬN"
                            type="object"
                            class="btn btn-primary"
                            attrs="{'invisible': ['|', ('is_past_booking', '=', False), ('state', 'not in', ['await_confirm', 'draft'])]}"
                            groups="welly_base.group_receptionist,welly_base.group_admin_club"
                    />
                    <button name="action_decline_past_booking"
                            icon="fa-window-close"
                            string="TỪ CHỐI"
                            type="object"
                            class="btn btn-danger"
                            attrs="{'invisible': ['|', ('is_past_booking', '=', False), ('state', 'not in', ['await_confirm', 'draft'])]}"
                            groups="welly_base.group_receptionist,welly_base.group_admin_club"
                    />
                </div>
                <field name="calendar_config_id" invisible="1"/>
                <field name="allow_merge_class" invisible="1"/>
                <field name="is_merge_class"
                       attrs="{'invisible': ['|', ('event_type', '!=', 'pt'), ('allow_merge_class', '==', False)],
                               'readonly': [('state', 'not in', ['draft', 'accepted', 'await_confirm', 'during_practice'])]}"
                />
            </xpath>

            <!-- Chỉnh sửa trường attendee_ids -->
            <xpath expr="//field[@name='attendee_ids']" position="attributes">
                <attribute name="readonly">0</attribute>
            </xpath>

            <!--Trường attendee_ids-->
            <xpath expr="//field[@name='attendee_ids']" position="inside">
                <tree string="Invitation details" editable="true" create="false" delete="false" class="tree_attendee_ids" default_order="partner_id">
                    <field name="event_type" invisible="1"/>
                    <field name="event_state" invisible="1"/>
                    <field name="is_today_utc7" invisible="1"/>
                    <field name="is_past_booking" invisible="1"/>
                    <field name="is_deducted_session" invisible="1"/>
                    <field name="valid_contract_ids" invisible="1"/>
                    <field name="event_is_full_participants" invisible="1"/>
                    <button name="action_show_details" type="object" icon="fa-eye"
                            attrs="{'invisible': [('event_type', '!=', 'pt')]}"/>
                    <field name="contract_id" string="Hợp đồng" domain="[('id', 'in', valid_contract_ids)]"
                           options="{'no_create': True}"
                           attrs="{'column_invisible': [('parent.event_type', '!=', 'pt')]}"/>
                    <field name="partner_id" string="Khách hàng"/>
                    <field name="sale_order_template_name_print"
                           attrs="{'column_invisible': [('parent.event_type', '!=', 'pt')]}"/>
                    <field name="email" string="Email"
                           attrs="{'column_invisible': [('parent.event_type', '=', 'pt')]}"/>
                    <field name="available_session_number" readonly="1"
                           attrs="{'column_invisible': [('parent.event_type', '!=', 'pt')]}"/>
                    <field name="is_checked_in" string="Đã checkin"
                           attrs="{'column_invisible': [('parent.event_type', '!=', 'pt')]}"/>
                    <field name="phone" string="Điện thoại"/>
                    <field name="state" string="Trạng thái"/>
                    <field name="confirm_commission"
                           string="Được tính COM"
                           attrs="{'column_invisible': [('parent.event_type', '!=', 'pt')],
                                   'readonly': [('state', 'not in', ['done', 'partner_skip_session', 'refund_session'])]}"
                    />

                    <button name="confirm_button"
                            type="object"
                            string="Xác nhận"
                            icon="fa-check-square text-success"
                            groups="welly_base.group_receptionist,welly_base.group_admin_club,welly_base.group_pt"
                            attrs="{'invisible': ['|', '|', '|', ('event_type', '!=', 'pt'), ('event_state', 'not in', ['draft', 'accepted']), '&amp;', ('event_state', 'in', ['draft', 'accepted']), ('state', 'not in', ['declined']), ('is_past_booking', '=', True)]}"
                    />

                    <button name="pt_confirm_button"
                            type="object"
                            string="PT xác nhận"
                            icon="fa-check-square text-success"
                            attrs="{'invisible': ['|', '|', '|', ('event_type', '!=', 'pt'), ('event_state', 'not in', ['draft', 'accepted']), '&amp;', ('event_state', 'in', ['draft', 'accepted']), ('state', 'not in', ['needsAction', 'await_pt_confirm']), ('is_past_booking', '=', True)]}"
                    />

                    <button name="partner_confirm_button"
                            type="object"
                            string="KH xác nhận"
                            icon="fa-check-square text-success"
                            attrs="{'invisible': ['|', '|', '|', ('event_type', '!=', 'pt'), ('event_state', 'not in', ['draft', 'accepted']), '&amp;', ('event_state', 'in', ['draft', 'accepted']), ('state', 'not in', ['needsAction', 'tentative']), ('is_past_booking', '=', True)]}"
                    />
                    <!--Button chỉ cho group lễ tân-->
                    <button name="do_accept"
                            type="object"
                            string="Khách đã đến"
                            groups="welly_base.group_receptionist"
                            icon="fa-check-square text-success"
                            attrs="{'invisible': ['&amp;', ('event_type', '=', 'pt'), '|', '|', '|', '|', ('event_state', 'not in', ['draft', 'during_practice', 'accepted']), '&amp;', ('event_state', 'in', ['draft', 'accepted']), ('state', 'not in', ['tentative', 'await_pt_confirm', 'accepted']), '&amp;', ('event_state', '=', 'during_practice'), ('state', 'not in', ['tentative', 'accepted']), ('is_checked_in', '=', True), ('is_past_booking', '=', True)]}"
                    />

                    <button name="partner_decline_button"
                            type="object"
                            string="KH hủy"
                            icon="fa-times-circle text-danger"
                            attrs="{'invisible': ['|', '|', '|', '|', ('event_type', '!=', 'pt'), ('event_state', 'not in', ['draft', 'during_practice', 'accepted']), '&amp;', ('event_state', 'in', ['draft', 'accepted']), ('state', 'not in', ['needsAction', 'tentative', 'await_pt_confirm', 'accepted']), '&amp;', ('event_state', '=', 'during_practice'), ('state', 'not in', ['tentative', 'accepted']), ('is_past_booking', '=', True)]}"
                    />

                    <button name="pt_decline_button"
                            type="object"
                            string="PT hủy"
                            icon="fa-times-circle text-danger"
                            attrs="{'invisible': ['|', '|', '|', '|', ('event_type', '!=', 'pt'), ('event_state', 'not in', ['draft', 'during_practice', 'accepted']), '&amp;', ('event_state', 'in', ['draft', 'accepted']), ('state', 'not in', ['needsAction', 'await_pt_confirm', 'accepted']), '&amp;', ('event_state', '=', 'during_practice'), ('state', 'not in', ['tentative', 'accepted']), ('is_past_booking', '=', True)]}"
                    />

                    <button name="refund_session_button"
                            type="object"
                            string="Hoàn buổi tập"
                            icon="fa-times-circle text-danger"
                            attrs="{'invisible': ['|', ('event_type', '!=', 'pt'), ('is_deducted_session', '=', False)]}"
                    />

                    <button type="action"
                            name="%(welly_book_pt.action_popup_forget_checkin)d"
                            string="Quên checkin"
                            icon="fa-times-circle text-danger"
                            attrs="{'invisible': ['|', '|', '|', '|', '|', '|', ('event_type', '!=', 'pt'), ('event_state', 'not in', ['during_practice', 'done', 'partner_skip_session']), '&amp;', ('event_state', '=', 'during_practice'), ('state', 'not in', ['partner_skip_session']), '&amp;', ('event_state', '=', 'done'), ('state', 'not in', ['partner_skip_session']), '&amp;', ('event_state', '=', 'partner_skip_session'), ('state', 'not in', ['partner_skip_session']), ('is_past_booking', '=', True), ('is_today_utc7', '=', False)]}"
                    />

                    <button name="do_decline"
                            type="object"
                            string="Từ chối"
                            icon="fa-times-circle text-danger"
                            attrs="{'invisible': ['&amp;', ('event_type', '=', 'pt'), '|', '|', ('event_state', 'not in', ['draft', 'accepted']), '&amp;', ('event_state', 'in', ['draft', 'accepted']), ('state', 'not in', ['needsAction','accepted', 'tentative', 'await_pt_confirm']), ('is_past_booking', '=', True)]}"
                    />

                </tree>
            </xpath>

            <xpath expr="//button[@name='do_tentative']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

        </field>
    </record>

    <record id="view_calendar_event_calendar_inherited" model="ir.ui.view">
        <field name="name">welly_calendar.event.calendar.inherited</field>
        <field name="model">calendar.event</field>
        <field name="inherit_id" ref="calendar.view_calendar_event_calendar"/>
        <field name="arch" type="xml">
            <xpath expr="//calendar" position="attributes">
                <attribute name="mode">day</attribute>
            </xpath>
            <xpath expr="//field[@name='partner_ids']" position="attributes">
                <attribute name="widget">many2many_attendee_phone</attribute>
            </xpath>
            <xpath expr="//field[@name='partner_ids']" position="before">
                <field name="event_type" invisible="1"/>
                <field name="state" string="Trạng thái"/>
                <field name="pt_id" string="PT"
                       filters="1"
                       write_model="filters.pt" write_field="pt_id" filter_field="pt_checked"
                       widget="many2one_avatar"
                       avatar_field="avatar_128"
                       attrs="{'invisible': [('event_type', '!=', 'pt')],
                               'required': [('event_type', '=', 'pt')]}"/>
                <field name="pt_id_substitute" attrs="{'invisible': [('event_type', '!=', 'pt')]}"
                       string="PT dạy thay"/>
            </xpath>
            <xpath expr="//field[@name='partner_ids']" position="after">
                <field name="location_id" string="Địa điểm trung tâm" attrs="{'invisible': [('event_type', '!=', 'pt')]}"/>
                <field name="took_attendance" attrs="{'invisible': [('event_type', '!=', 'pt')]}" string="Đã check-in"/>
                <field name="total_attendance" attrs="{'invisible': [('event_type', '!=', 'pt')]}"
                       string="Tổng học viên"/>
            </xpath>
            <xpath expr="//field[@name='privacy']" position="attributes">
                <attribute name="attrs">{'invisible': 1}</attribute>
            </xpath>
        </field>
    </record>

    <record id="view_calendar_event_tree_inherited" model="ir.ui.view">
        <field name="name">welly_calendar.event.tree.inherited</field>
        <field name="model">calendar.event</field>
        <field name="inherit_id" ref="calendar.view_calendar_event_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_ids']" position="attributes">
                <attribute name="string">Danh sách khách hàng</attribute>
            </xpath>
            <xpath expr="//field[@name='partner_ids']" position="after">
                <field name="accepted_partner_ids" widget="many2many_tags" string="KH đồng ý tham gia"
                       attrs="{'readonly': True}" optional="hide"/>
                <field name="contract_code" string="Mã HĐ tương ứng" attrs="{'readonly': True}" optional="show"/>
                <field name="took_attendance" string="KH đã check-in" attrs="{'readonly': True}" optional="show"/>
                <field name="total_attendance" string="Tổng người được mời" attrs="{'readonly': True}" optional="show"/>
                <field name="location_id" string="Địa điểm" attrs="{'readonly': True}" optional="show"/>
                <field name="state" string="Trạng thái" attrs="{'readonly': True}" optional="show"/>
                <field name="pt_id" string="PT chính" attrs="{'readonly': True}" optional="show"/>
                <field name="pt_id_substitute" string="PT dạy thay" attrs="{'readonly': True}" optional="show"/>
            </xpath>
            <field name="location" attrs="{'invisible': 1}"/>
        </field>
    </record>

    <record id="welly_view_calendar_filter_inherit" model="ir.ui.view">
        <field name="name">welly_view_calendar_filter_inherit</field>
        <field name="model">calendar.event</field>
        <field name="inherit_id" ref="calendar.view_calendar_event_search"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='filter_start_date']" position="after">
                <separator/>
                <filter string="Bắt đầu trong tháng"
                        name="start_current_month"
                        domain="[('start', '=', 'current_month')]"/>
                <filter string="Bắt đầu tháng trước"
                        name="start_last_month"
                        domain="[('start', '=', 'last_month')]"/>
                <filter string="Lịch của tôi"
                        name="my_calendar"
                        domain="[('event_type', '=', 'pt'), '|', '&amp;', ('pt_id', '=', uid), ('pt_id_substitute', '=', False), ('pt_id_substitute', '=', uid)]"/>
            </xpath>
            <xpath expr="//field[@name='name']" position="after">
                <field name="partner_phones"/>
            </xpath>
            <xpath expr="//search//filter[@name='busy']" position="replace"/>
            <xpath expr="//search//filter[@name='free']" position="replace"/>
            <xpath expr="//search//filter[@name='public']" position="replace"/>
            <xpath expr="//search//filter[@name='private']" position="replace"/>
            <xpath expr="//search//filter[@name='confidential']" position="replace"/>
        </field>
    </record>
    <record id="calendar.action_calendar_event" model="ir.actions.act_window">
        <field name="name">Calendar</field>
        <field name="res_model">calendar.event</field>
        <field name="view_mode">calendar,tree,form</field>
        <field name="context">{'search_default_my_calendar': 1, 'slot_duration': '00:15:00'}</field>
    </record>

<!--    đánh lại sequence cho menu đặt lịch-->
    <record id="calendar.mail_menu_calendar" model="ir.ui.menu">
        <field name="sequence" eval="50"/>
    </record>
</odoo>
