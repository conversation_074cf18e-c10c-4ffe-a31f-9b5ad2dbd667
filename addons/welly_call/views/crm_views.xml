<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="welly_crm_stage_form" model="ir.ui.view">
            <field name="name">welly_crm_stage_form</field>
            <field name="model">crm.stage</field>
            <field name="inherit_id" ref="crm.crm_stage_form" />
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <xpath expr="//group" position="replace">
                    <group>
                        <group>
                            <field name="is_won" />
                            <field name="stage_type" />
                            <field name="fold" />
                            <field name="team_id" options='{"no_open": True, "no_create": True}'
                                attrs="{'invisible': [('team_count', '&lt;=', 1)]}"
                                kanban_view_ref="%(sales_team.crm_team_view_kanban)s" />
                        </group>
                        <field name="team_count" invisible="1" />
                    </group>
                </xpath>
            </field>
        </record>

        <record id="crm_lead_view_form" model="ir.ui.view">
            <field name="name">crm.lead.form.call</field>
            <field name="model">crm.lead</field>
            <field name="inherit_id" ref="crm.crm_lead_view_form" />
            <field name="arch" type="xml">
                <xpath expr="//form" position="attributes">
                    <attribute name="js_class">welly_js_form_view</attribute>
                </xpath>
                <xpath expr="//header" position="before">
                    <script>
                        function clickCall() {
                        const buttonCall = document.getElementById('superCallButton');
                        if (buttonCall !== null) {
                        buttonCall.click();
                        } else {
                        alert("cant seems to find the thing");
                        }
                        }
                    </script>
                </xpath>
                <xpath expr="//button[@name='action_set_won_rainbowman']" position="before">
                    <field name="stage_type" invisible="1" />
                    <field name="can_call" invisible="1" />
                    <button string="Call" name="call_dummy" type="object" class="oe_highlight"
                        attrs="{'invisible': ['|','|',('phone','in',(False,'')),('stage_type', '!=', 'call'),('can_call', '=', False)]}"
                        onclick="clickCall()" />
                </xpath>
                <button name="action_schedule_meeting" position="after">
                    <button class="oe_stat_button" type="object"
                        name="action_view_welly_call" icon="fa-times">
                        <field name="call_history_count" widget="statinfo" string="Call History" />
                    </button>
                </button>
            </field>
        </record>
    </data>
</odoo>