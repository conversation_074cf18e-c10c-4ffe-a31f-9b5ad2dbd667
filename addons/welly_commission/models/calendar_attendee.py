from odoo import models, fields, api, _
from odoo.addons.welly_book_pt.fields.selection import AttendeeState, EventState
import io
import base64
import xlsxwriter
from pytz import timezone


class CustomCalendarAttendee(models.Model):
    _inherit = 'calendar.attendee'

    # đây là trường char dùng để hiển thị trên view ko có mục đích tính toán com
    account_per_session_char = fields.Char(string="Số Tiền/Buổi(Lượt)", compute='_compute_account_per_session', readonly=True,
                                          store=True)
    # đây là trường number dùng để đích tính toán com nếu là số âm tức là đang không dùng
    account_per_session = fields.Monetary(string="Số Tiền/Buổi(Lượt)", compute='_compute_account_per_session', readonly=True,
                                          store=True, currency_field='currency_id')

    currency_id = fields.Many2one('res.currency', string='Currency', default=lambda self: self.env.company.currency_id)

    @api.depends('contract_id', 'gift_session_number')
    def _compute_account_per_session(self):
        for record in self:
            if record.contract_id:
                if record.gift_session_number:
                    record.account_per_session = 0
                    record.account_per_session_char = ""
                    continue
                record.account_per_session = record.contract_id.account_per_session
                record.account_per_session_char = record.contract_id.account_per_session_char
            else:
                record.account_per_session_char = None

    # Ghi đè export_data để thêm cột
    def export_data(self):
        try:
            # Tạo file Excel trong bộ nhớ
            output = io.BytesIO()
            workbook = xlsxwriter.Workbook(output)
            worksheet = workbook.add_worksheet('BÁO CÁO TỔNG HỢP LỊCH DẠY PT')

            # Định nghĩa format cho các cell
            header = workbook.add_format(
                {'bold': True, 'border': 1, 'color': 'white', 'bg_color': 'blue', 'align': 'center',
                 'valign': 'vcenter'})
            content_center = workbook.add_format({'border': 1, 'align': 'center'})
            content_left = workbook.add_format({'border': 1, 'align': 'left'})
            content_right = workbook.add_format({'border': 1, 'num_format': '#,##0', 'align': 'right'})

            # Viết header
            worksheet.write('A1', 'STT', header)
            worksheet.write('B1', 'Ngày', header)
            worksheet.write('C1', 'HLV', header)
            worksheet.write('D1', 'Khách hàng', header)
            worksheet.write('E1', 'SĐT', header)
            worksheet.write('F1', 'Gói dịch vụ PT', header)
            worksheet.write('G1', 'Số hợp đồng', header)
            worksheet.write('H1', 'Hình thức tập', header)
            worksheet.write('I1', 'Trạng thái lịch', header)
            worksheet.write('J1', 'Trạng thái khách hàng', header)
            worksheet.write('K1', 'Kiểu checkin', header)
            worksheet.write('L1', 'Thời gian checkin', header)
            worksheet.write('M1', 'Giá gói PT', header)
            worksheet.write('N1', 'Buổi chính', header)
            worksheet.write('O1', 'Buổi tặng', header)
            worksheet.write('P1', 'Phân bổ', header)
            worksheet.write('Q1', 'Hoa hồng buổi tặng', header)
            worksheet.write('R1', 'Số tiền trên buổi', header)
            worksheet.write('S1', 'Ghi chú lịch', header)
            worksheet.write('T1', 'Được tính COM', header)

            # Lấy múi giờ của người dùng hiện tại
            user_tz = self.env.user.tz or 'UTC'
            tz = timezone(user_tz)

            # Viết dữ liệu của các bản ghi
            row = 1
            for record in self:
                date = record.date.astimezone(tz).date().strftime('%d/%m/%Y')
                pt = record.pt_id.name or ''
                customer = record.partner_id.name or ''
                phone = record.phone or ''
                service = record.sale_order_template_name_print or ''
                contract = record.contract_id.name or ''
                exercise = record.exercise_id.name or ''
                event_state = record.event_state or ''
                dict_event_state = dict(EventState.selection)
                if event_state:
                    event_state = dict_event_state.get(event_state, '')
                state = record.state or ''
                dict_state = dict(AttendeeState.selection)
                if state:
                    state = dict_state.get(state, '')
                type_checkin = record.type_checkin or ''
                dict_type_checkin = dict(self.TYPE_CHECKIN)
                if type_checkin:
                    type_checkin = dict_type_checkin.get(type_checkin, '')
                time_checkin = record.time_checkin.astimezone(tz).strftime(
                    '%d/%m/%Y %H:%M:%S') if record.time_checkin else ''
                pay_amount = record.pay_amount or 0
                main_session = record.main_session_char or ''
                gift_session = record.gift_session_char or ''
                pay_amount_per_session_number = record.pay_amount_per_session_number or 0
                gift_session_commission = record.gift_session_commission or 0
                account_per_session = record.account_per_session if record.account_per_session > 0 else 0
                event_notes = record.event_notes or ''
                confirm_commission = 'Có' if record.confirm_commission else 'Không'

                worksheet.write(row, 0, row, content_center)
                worksheet.write(row, 1, date, content_center)
                worksheet.write(row, 2, pt, content_center)
                worksheet.write(row, 3, customer, content_center)
                worksheet.write(row, 4, phone, content_center)
                worksheet.write(row, 5, service, content_center)
                worksheet.write(row, 6, contract, content_center)
                worksheet.write(row, 7, exercise, content_center)
                worksheet.write(row, 8, event_state, content_center)
                worksheet.write(row, 9, state, content_center)
                worksheet.write(row, 10, type_checkin, content_center)
                worksheet.write(row, 11, time_checkin, content_center)
                worksheet.write(row, 12, pay_amount, content_right)
                worksheet.write(row, 13, main_session, content_center)
                worksheet.write(row, 14, gift_session, content_center)
                worksheet.write(row, 15, pay_amount_per_session_number, content_right)
                worksheet.write(row, 16, gift_session_commission, content_right)
                worksheet.write(row, 17, account_per_session, content_right)
                worksheet.write(row, 18, event_notes, content_left)
                worksheet.write(row, 19, confirm_commission, content_left)
                row += 1

            # Đóng file Excel
            workbook.close()

            # Lấy nội dung file từ bộ nhớ
            file_data = output.getvalue()
            output.close()

            # Mã hóa base64 cho file
            encoded_file_data = base64.b64encode(file_data)

            # Tạo attachment trong Odoo
            attachment = self.env['ir.attachment'].create({
                'name': 'Báo cáo tổng hợp lịch dạy PT.xlsx',
                'datas': encoded_file_data,
                'type': 'binary',
            })

            return {
                'type': 'ir.actions.act_url',
                'url': f'/web/content/{attachment.id}?download=true',
                'target': 'new',
            }
        except Exception as e:
            # Hiển thị thông báo lỗi cho người dùng
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Xuất dữ liệu thất bại',
                    'message': 'Có lỗi xảy ra khi xuất dữ liệu.',
                    'type': 'danger',
                }
            }
