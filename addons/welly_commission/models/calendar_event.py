from odoo import models, fields, api


class CalendarEvent(models.Model):
    _inherit = 'calendar.event'

    account_per_session = fields.Html(string="Số tiền/Buổi", compute='_compute_account_per_session', store=True,
                                      readonly=True)

    account_per_session_value = fields.Monetary(string="Tổng Số tiền/Buổi", compute='_compute_account_per_session',
                                                readonly=True,
                                                store=True, currency_field='currency_id')

    currency_id = fields.Many2one('res.currency', string='Currency', default=lambda self: self.env.company.currency_id)

    @api.depends('attendee_ids', 'attendee_ids.contract_id', 'attendee_ids.account_per_session', 'attendee_ids.contract_id.account_per_session_char')
    def _compute_account_per_session(self):
        for event in self:
            total = 0
            account_per_session = ""
            for attendee in event.attendee_ids:
                if attendee.contract_id and attendee.contract_id.account_per_session >= 0:
                    total += attendee.contract_id.account_per_session
                    account_per_session = f"{account_per_session}{attendee.partner_id.name}-{self.convert_amount_to_vi(attendee.contract_id.account_per_session)};<br>"

            event.with_user(1).write({'account_per_session_value': total, 'account_per_session': account_per_session})

    def convert_amount_to_vi(self, amount):
        formatted_amount = '{:,.0f} ₫'.format(amount)
        return formatted_amount
