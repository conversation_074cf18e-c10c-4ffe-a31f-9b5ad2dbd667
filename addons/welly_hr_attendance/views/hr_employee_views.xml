<odoo>
    <!-- Inherit from hr_employee -->
    <record id="view_employee_form_inherit" model="ir.ui.view">
        <field name="name">hr.employee.form.inherit</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='work_email']" position="after">
                <field name="employee_code" placeholder="Nhập mã nhân viên..."/>
            </xpath>
            <xpath expr="//field[@name='tz']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='tz']" position="before">
                <field name="work_shift_type"/>
                <field name="shift_configuration_id"
                       attrs="{'invisible': [('work_shift_type', '=', 'flexible')], 'required': [('work_shift_type', '=', 'fixed')]}"/>
                <label for="shift_1_start" string="Kíp 1"
                       attrs="{'invisible': [('work_shift_type', '=', 'flexible')]}"/>
                <div attrs="{'invisible': [('work_shift_type', '=', 'flexible')]}">
                    <div class="o_row">
                        <field name="shift_1_start" widget="timepicker"/>
                        <span>-</span>
                        <field name="shift_1_end" widget="timepicker"/>
                    </div>
                </div>
                <label for="shift_2_start" string="Kíp 2"
                       attrs="{'invisible': [('work_shift_type', '=', 'flexible')]}"/>
                <div attrs="{'invisible': [('work_shift_type', '=', 'flexible')]}">
                    <div class="o_row">
                        <field name="shift_2_start" widget="timepicker"/>
                        <span>-</span>
                        <field name="shift_2_end" widget="timepicker"/>
                    </div>
                </div>
                <field name="work_days" attrs="{'invisible': [('work_shift_type', '=', 'fixed')]}"/>
<!--                ngày khác-->
                <field name="working_days_other" attrs="{'invisible': [('work_shift_type', '=', 'flexible')]}"/>
                <label for="shift_1_start_other" string="Kíp 1" attrs="{'invisible': ['|', ('work_shift_type', '=', 'flexible'), ('working_days_other', '=', False)]}"/>
                <div attrs="{'invisible': ['|', ('work_shift_type', '=', 'flexible'), ('working_days_other', '=', False)]}">
                    <div class="o_row">
                        <field name="shift_1_start_other" widget="timepicker"/>
                        <span>-</span>
                        <field name="shift_1_end_other" widget="timepicker"/>
                    </div>
                </div>
                <label for="shift_2_start" string="Kíp 2" attrs="{'invisible': ['|', ('work_shift_type', '=', 'flexible'), ('working_days_other', '=', False)]}"/>
                <div attrs="{'invisible': ['|', ('work_shift_type', '=', 'flexible'), ('working_days_other', '=', False)]}">
                    <div class="o_row">
                        <field name="shift_2_start_other" widget="timepicker"/>
                        <span>-</span>
                        <field name="shift_2_end_other" widget="timepicker"/>
                    </div>
                </div>
            </xpath>
            <xpath expr="//field[@name='resource_calendar_id']" position="replace">
                <field name="resource_calendar_id" invisible="1"/>
            </xpath>
        </field>
    </record>

    <!-- Inherit from hr_employee_puplic -->
    <record id="employee_public_attendance_form" model="ir.ui.view">
        <field name="name">employee.public.attendance.form</field>
        <field name="model">hr.employee.public</field>
        <field name="inherit_id" ref="hr.hr_employee_public_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='work_email']" position="after">
                <field name="employee_code" readonly="1"/>
            </xpath>
            <xpath expr="//div[@name='button_box']" position="inside">
                <button name="action_hr_attendance"
                        type="object"
                        class="oe_stat_button"
                        icon="fa-clock-o"
                        help="Số giờ làm việc trong ngày">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value">
                            <field name="hours_today" widget="float_time"/> Giờ
                        </span>
                        <span class="o_stat_text">
                            Trong ngày
                        </span>
                    </div>
                </button>
            </xpath>
        </field>
    </record>

    <record id="view_welly_hr_employee_public_search_inherit" model="ir.ui.view">
        <field name="name">view_welly_hr_employee_public_search_inherit</field>
        <field name="model">hr.employee.public</field>
        <field name="inherit_id" ref="hr.hr_employee_public_view_search"/>  <!-- Kế thừa view gốc -->
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='my_team']" position="before">
                <filter name="me" string="Tôi" domain="[('user_id', '=', uid)]"/>
            </xpath>
        </field>
    </record>

    <record id="hr_employee_public_action_welly" model="ir.actions.act_window">
        <field name="name">Employees</field>
        <field name="res_model">hr.employee.public</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="domain">[]</field>
        <field name="context">{'search_default_me': 1, 'chat_icon': True, 'current_user_id': uid}</field>
        <field name="view_id" eval="False"/>
        <field name="search_view_id" ref="view_welly_hr_employee_public_search_inherit"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add a new employee
            </p>
            <p>
                With just a quick glance on the Odoo employee screen, you
                can easily find all the information you need for each person;
                contact data, job position, availability, etc.
            </p>
        </field>
    </record>

    <record id="view_employee_tree_inherit" model="ir.ui.view">
        <field name="name">hr.employee.tree.inherit</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="before">
                <field name="employee_code"/>
            </xpath>
        </field>
    </record>

    <record id="hr.menu_hr_employee" model="ir.ui.menu">
        <field name="active" eval="False"/>
    </record>

    <menuitem
            id="menu_hr_employee_welly"
            name="Nhân viên"
            action="hr_employee_public_action_welly"
            parent="hr.menu_hr_root"
            sequence="4"/>
</odoo>