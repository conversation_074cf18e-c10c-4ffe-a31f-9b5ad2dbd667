from dateutil.relativedelta import relativedelta

from odoo import Command, api, fields, models, _
from datetime import datetime, timedelta, timezone, date
from odoo.exceptions import ValidationError
from odoo.tools import float_round

# Bảng quan hệ giữa hr.employee và res.users (ngườ<PERSON>)
class HrEmployeeLeaveManagerRel(models.Model):
    _name = 'hr.employee.leave.manager.rel'
    _description = 'Relation between <PERSON><PERSON>loyee and Leave Manager'
    _order = 'sequence'
    _table = 'hr_employee_leave_manager_rel'
    _log_access = False  # Loại bỏ các trường mặc định như create_date, write_date

    employee_id = fields.Many2one(
        'hr.employee',
        string='Employee',
        required=True,
        ondelete='cascade'
    )
    user_id = fields.Many2one(
        'res.users',
        string='User',
        required=True,
        ondelete='cascade'
    )
    sequence = fields.Integer(string='Sequence')

    @api.model
    def get_sequence(self, employee_id, user_id):
        self.env.cr.execute("SELECT sequence FROM hr_employee_leave_manager_rel WHERE employee_id = %s AND user_id = %s", (employee_id, user_id))
        sequence = self.env.cr.fetchone()
        return sequence[0] if sequence else 0

    @api.model
    def set_sequence(self, employee_id, user_id, sequence):
        self.env.cr.execute("UPDATE hr_employee_leave_manager_rel SET sequence = %s WHERE employee_id = %s AND user_id = %s", (sequence, employee_id, user_id))
        leave_request = self.env['hr.leave.request'].search([('employee_id', '=', employee_id), ('state', 'not in', ('validate', 'cancel', 'reject', 'overdue'))])
        leave_request._compute_leave_manager_ids()
        leave_request._compute_is_leave_manager_approved()
        leave_request._compute_responsible_ids()
        leave_request._compute_is_responsible_approved()
        leave_request._compute_current_approver_ids()

    @api.model
    def get_root_sequence(self, employee_id):
        # Kiểm tra nếu employee_id là NewId hoặc không hợp lệ
        if not employee_id or not isinstance(employee_id, int):
            return []

        # Nếu employee_id hợp lệ, phần code còn lại sẽ được thực thi bình thường.
        self.env.cr.execute("SELECT user_id,sequence FROM hr_employee_leave_manager_rel WHERE employee_id = %s",
                            (employee_id,))
        sequences = self.env.cr.fetchall()
        return sequences

class HrEmployeeBase(models.AbstractModel):
    _inherit = "hr.employee.base"

    def _compute_allocation_remaining_display(self):
        allocations = self.env['hr.leave.allocation'].search([('employee_id', 'in', self.ids)])
        leaves_taken = allocations.holiday_status_id.sudo()._get_employees_days_per_allocation(self.ids)
        for employee in self:
            employee_remaining_leaves = 0
            for leave_type in leaves_taken[employee.id]:
                for allocation in leaves_taken[employee.id][leave_type]:
                    if allocation:
                        virtual_remaining_leaves = leaves_taken[employee.id][leave_type][allocation]['virtual_remaining_leaves']
                        employee_remaining_leaves += virtual_remaining_leaves
            employee.allocation_remaining_display = ('%.2f' % employee_remaining_leaves).rstrip('0').rstrip('.')

    # Trường cũ để tương thích ngược
    leave_manager_id = fields.Many2one(
        'res.users', string='Time Off',
        compute='_compute_leave_manager', store=True, readonly=False,
        domain="[('share', '=', False), ('company_ids', 'in', company_id)]",
        tracking=True,
        help='Select the user responsible for approving "Time Off" of this employee.\n'
             'If empty, the approval is done by an Administrator or Approver (determined in settings/users).')

    # Trường mới cho phép chọn nhiều người phê duyệt và đánh sequence
    leave_manager_ids = fields.Many2many(
        'res.users',
        relation='hr_employee_leave_manager_rel',
        column1='employee_id',
        column2='user_id',
        string='Người phê duyệt',
        domain="[('share', '=', False), ('company_ids', 'in', company_id)]",
        tracking=True,
        help='Chọn người phê duyệt đơn nghỉ phép của nhân viên này.\n'
             'Nếu để trống, việc phê duyệt sẽ được thực hiện bởi Quản trị viên hoặc Người phê duyệt (được xác định trong cài đặt/người dùng).')

    def _compute_allocation_count(self):
        # Don't get allocations that are expired
        current_date = date.today()
        data = self.env['hr.leave.allocation']._read_group([
            ('employee_id', 'in', self.ids),
            ('state', '=', 'validate'),
            ('date_from', '<=', current_date),
            '|',
            ('date_to', '=', False),
            ('date_to', '>=', current_date),
        ], ['number_of_days:sum', 'employee_id'], ['employee_id'])
        rg_results = dict((d['employee_id'][0], {"employee_id_count": d['employee_id_count'], "number_of_days": d['number_of_days']}) for d in data)
        for employee in self:
            result = rg_results.get(employee.id)
            employee.allocation_count = float_round(result['number_of_days'], precision_digits=2) if result else 0.0
            employee.allocation_display = "%g" % employee.allocation_count
            employee.allocations_count = result['employee_id_count'] if result else 0.0

    def write(self, values):
        # Nếu cập nhật leave_manager_ids, đồng bộ với leave_manager_id
        if 'leave_manager_ids' in values:
            # Thêm quyền cho tất cả leave_manager_ids
            if values['leave_manager_ids']:
                # Lấy danh sách user_id từ values['leave_manager_ids']
                user_ids = []
                for command in values['leave_manager_ids']:
                    if command[0] == 4:  # LINK
                        user_ids.append(command[1])
                    elif command[0] == 6:  # SET
                        user_ids.extend(command[2])

                # Thêm quyền cho tất cả user_ids
                for user_id in user_ids:
                    self.env.ref('hr_holidays.group_hr_holidays_user').users = [Command.link(user_id)]

        # Xử lý cập nhật leave_manager_id (giữ lại để tương thích ngược)
        if 'leave_manager_id' in values:
            # Kiểm tra xem self.leave_manager_id có còn quản lý nhân viên nào không
            # nếu không thì loại bỏ khỏi nhóm hr_holidays.group_hr_holidays_user
            if self.leave_manager_id:
                exist_leave_manager = self.env['hr.employee'].search_count([('leave_manager_id', '=', self.leave_manager_id.id)], limit=1)
                if not exist_leave_manager and \
                    not self.leave_manager_id.has_group('hr_holidays.group_hr_holidays_manager'):
                    self.env.ref('hr_holidays.group_hr_holidays_user').users = [Command.unlink(self.leave_manager_id.id)]

            # Thêm quyền cho leave_manager_id
            if values['leave_manager_id']:
                self.env.ref('hr_holidays.group_hr_holidays_user').users = [Command.link(values['leave_manager_id'])]

            # Đồng bộ leave_manager_id với leave_manager_ids
            # Nếu leave_manager_id không nằm trong leave_manager_ids, thêm vào
            if values['leave_manager_id'] and 'leave_manager_ids' not in values:
                for record in self:
                    if values['leave_manager_id'] not in record.leave_manager_ids.ids:
                        record.leave_manager_ids = [Command.link(values['leave_manager_id'])]

        if 'parent_id' in values:
            # Kiểm tra xem self.parent_id có còn quản lý nhân viên nào không
            # nếu không thì loại bỏ khỏi nhóm hr_holidays.group_hr_holidays_user
            if self.parent_id:
                exist_leave_manager = self.env['hr.employee'].search_count([('parent_id', '=', self.parent_id.id)], limit=1)
                if not exist_leave_manager and \
                    not self.parent_id.user_id.has_group('hr_holidays.group_hr_holidays_manager'):
                    self.env.ref('hr_holidays.group_hr_holidays_user').users = [Command.unlink(self.parent_id.user_id.id)]

            # Thêm quyền cho parent_id
            employee_parent = self.env['hr.employee'].browse(values['parent_id'])
        
            if not employee_parent.user_id:            
                raise ValidationError(_('Người quản lý chưa có người dùng liên kết. Vui lòng liên kết người dùng trước khi cập nhật.'))
            
            # Thêm quyền cho người dùng cho quản lý
            self.env.ref('hr_holidays.group_hr_holidays_user').users = [Command.link(employee_parent.user_id.id)]

        return super().write(values)

class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    # override _update_work_shift_ids
    @api.constrains('shift_configuration_id')
    def _update_work_shift_ids(self):
        # Lấy ngày hiện tại
        date = datetime.now().astimezone(tz=timezone(timedelta(hours=7))).replace(tzinfo=None).date()

        # Kiểm tra đã có ngày nghỉ phép nào được tạo có trạng thái khác huỷ và nháp không
        hr_leave_ids = self.env['hr.leave.request'].search([('state', 'not in', ['draft', 'reject', 'cancel']), ('employee_id', 'in', self.ids), ('date_from', '>=', date)])
        if hr_leave_ids:
            # Lấy tên nhân viên
            employee_names = ', '.join(set([hr_leave.employee_id.name for hr_leave in hr_leave_ids]))
            # Báo lỗi và không tạo ca làm việc
            raise ValidationError(_('Có request nghỉ phép đang trong quá trình xử lý, hoặc đã chấp thuận của nhân viên: %s', employee_names))

        # Gọi phương thức cha nếu không có lỗi
        super(HrEmployee, self)._update_work_shift_ids()

    def write(self, values):
        # Kiểm tra nếu nhân viên đang được lưu trữ (active từ True thành False)
        if 'active' in values and values['active'] is False:
            for employee in self:
                if employee.active:
                    # Lấy ngày hiện tại làm ngày kết thúc hợp đồng
                    departure_date = employee.departure_date
                    
                    # Tìm tất cả đề xuất nghỉ phép có ngày hiệu lực >= ngày lưu trữ
                    leave_requests = self.env['hr.leave.request'].search([
                        ('employee_id', '=', employee.id),
                        ('date_from', '>=', departure_date),
                        ('state', 'not in', ['cancel', 'reject'])
                    ])
                    
                    if leave_requests:
                        # Log thông tin về việc hủy đề xuất ngày công
                        employee.message_post(
                            body=_("Hủy %s đề xuất ngày công do nhân viên được lưu trữ.") % len(leave_requests),
                            subtype_xmlid='mail.mt_note'
                        )
                        
                        # Hủy từng đề xuất nghỉ phép và hoàn trả số ngày nghỉ phép
                        for leave in leave_requests:
                            
                            # Tạo thông báo trong chatter của đề xuất nghỉ phép
                            leave.message_post(
                                body=_("Đề xuất này được tự động hủy do nhân viên %s đã được lưu trữ vào ngày %s.") % 
                                     (employee.name, departure_date.strftime('%d/%m/%Y')),
                                subtype_xmlid='mail.mt_note'
                            )
                            
                            # Cập nhật trạng thái thành hủy
                            leave.state = 'cancel'
                            
                            # Log the action
                            action_description = _('Tự động hủy do nhân viên được lưu trữ')
                            leave.message_post(
                                body=action_description,
                                subtype_xmlid='mail.mt_note',
                                message_type='notification'
                            )
        
        # Gọi phương thức cha để tiếp tục xử lý
        return super(HrEmployee, self).write(values)

    @api.model
    def get_public_holidays_data(self, date_start, date_end):
        self = self._get_contextual_employee()
        public_holidays = self._get_public_holidays(date_start, date_end).sorted('date_from')
        return list(map(lambda bh: {
            'id': -bh.id,
            'colorIndex': bh.color,
            'end': datetime.combine(bh.date_to, datetime.max.time()).isoformat(),
            'endType': "datetime",
            'isAllDay': True,
            'start': datetime.combine(bh.date_from, datetime.min.time()).isoformat(),
            'startType': "datetime",
            'title': bh.name,
        }, public_holidays))

    @api.constrains('leave_manager_id', 'parent_id')
    def _notify_manager_leave_request(self):
        for record in self:
            leave_request = self.env['hr.leave.request'].search([('employee_id', '=', record.id), ('state', '=', 'approve'), ('leave_validation_type', 'in', ['manager', 'both', 'manager_hr_coo'])])
            leave_request.create_activity()

    def action_time_off_dashboard(self):
        if self.env.user.has_group('hr_holidays.group_hr_holidays_manager') or self.id == self.env.user.employee_id.id:
            return {
                'name': _('Time Off Dashboard'),
                'type': 'ir.actions.act_window',
                'res_model': 'hr.leave.request',
                'views': [[self.env.ref('welly_hr_timeoff.hr_leave_employee_view_dashboard').id, 'calendar']],
                'context': {
                    'search_default_employee_id': self.id,
                    'employee_id': self.id,
                }
            }

    def get_holidays(self, start_date, end_date):
        """
        Lấy danh sách các ngày nghỉ lễ trong khoảng thời gian từ start_date đến end_date
        và trả về dưới dạng dictionary với key là ngày và value là màu sắc
        """
        all_days = {}

        self = self or self.env.user.employee_id

        holidays = self._get_holidays(start_date, end_date)
        for holiday in holidays:
            num_days = (holiday.date_to - holiday.date_from).days
            for d in range(num_days + 1):
                all_days[str(holiday.date_from + relativedelta(days=d))] = holiday.color

        return all_days

    def _get_holidays(self, start_date, end_date):
        """
        Lấy danh sách các ngày nghỉ lễ trong khoảng thời gian từ start_date đến end_date
        """
        domain = [
            ('date_from', '<=', end_date),
            ('date_to', '>=', start_date),
            ('company_id', 'in', self.env.companies.ids),
            '|',
            ('calendar_id', '=', False),
            ('calendar_id', '=', self.resource_calendar_id.id),
        ]

        return self.env['resource.calendar.leaves'].search(domain)