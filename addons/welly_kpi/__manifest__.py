# -*- coding: utf-8 -*-
{
    'name': "Welly KPI",
    'author': "WellyTech",
    'website': "https://wellytech.vn",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/16.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'Welly',
    'version': '16.0.3.4',

    'depends': ['base', 'welly_base', 'hr', 'welly_book_pt', 'welly_commission'],

    'data': [
        # 'data/sale_rate.xml',
        'security/group.xml',
        'security/ir.model.access.csv',
        'views/meta_data.xml',
        'views/menu_items.xml',
        'views/account_move.xml',
        'views/account_payment.xml',
        'views/hr_department_sales_target_views.xml',
        'views/hr_employee_sales_target_views.xml',
        'views/hr_employee_booking_pt_views.xml',
        'views/hr_employee_sales_line_views.xml',
        'report/report_sale_rate_payment_view.xml',
        'views/welly_product_template_views.xml',
        'views/welly_contract_views.xml',
        'views/hr_job_views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': True,
}
