from odoo import models, fields, api


class CalendarEvent(models.Model):
    _inherit = 'calendar.event'

    # Thông tin pt được tính hoa hồng
    pt_commission_id = fields.Many2one('hr.employee', compute="_compute_pt_commission_id", string='PT Commission',
                                       store=True)

    @api.depends('pt_id', 'pt_id.employee_id', 'pt_id_substitute', 'pt_id_substitute.employee_id')
    def _compute_pt_commission_id(self):
        for record in self:
            if record.event_type == 'pt':
                # Ki<PERSON>m tra nếu có PT dạy thay thì Pt được tính com sẽ là PT dạy thay không thì là PT dạy chính
                record.pt_commission_id = record.pt_id_substitute.employee_id or record.pt_id.employee_id

    # Thông tin booking pt
    hr_employee_booking_pt_id = fields.Many2one('hr.employee.booking.pt', string='Booking PT',
                                                compute='_compute_hr_employee_booking_pt_id', store=True)

    @api.depends('start', 'pt_commission_id', 'state')
    def _compute_hr_employee_booking_pt_id(self):
        for record in self:
            # Nếu event không phải là pt hoặc không ở trạng thái done hoặc confirm_commission thì không cần tính booking
            if record.event_type != 'pt' or record.state not in ['done', 'confirm_commission']:
                record.hr_employee_booking_pt_id = False
                continue
            # Tim booking pt theo tháng và năm và employee_id của hr.employee.booking.pt
            booking_pt = self.env['hr.employee.booking.pt'].search([
                ('month', '=', str(record.start.month).zfill(2)),
                ('year', '=', record.start.year),
                ('employee_id', '=', record.pt_commission_id.id)
            ], limit=1)

            # Nếu không tìm thấy booking pt thì tạo mới
            if not booking_pt:
                # Nếu không tìm thấy booking pt thì tạo mới
                booking_pt = self.env['hr.employee.booking.pt'].create({
                    'year': record.start.year,
                    'month': str(record.start.month).zfill(2),
                    'employee_id': record.pt_commission_id.id,
                })
            record.hr_employee_booking_pt_id = booking_pt

    # Thông tin số tiền được tính hoa hồng cho PT
    pt_revenue = fields.Float(string='PT Revenue', compute='_compute_pt_revenue', store=True)

    @api.depends('account_per_session_value')
    def _compute_pt_revenue(self):
        for record in self:
            record.pt_revenue = record.account_per_session_value