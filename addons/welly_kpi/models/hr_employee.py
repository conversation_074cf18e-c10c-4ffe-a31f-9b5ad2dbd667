# inherit hr.department
from odoo import models, fields, api, exceptions
from ..fields import selection

class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    job_id = fields.Many2one(comodel_name='hr.job', string='Chức vụ', tracking=True, required=True)
    sales_target_ids = fields.One2many('hr.employee.sales.target', 'employee_id', string='Sales Target')
    sales_target_count = fields.Integer(string='Sales Target Count', compute='_compute_sales_target_count', store=True)

    # Tính toán số lượng sales target
    @api.depends('sales_target_ids')
    def _compute_sales_target_count(self):
        for record in self:
            record.sales_target_count = len(record.sales_target_ids)

    # Hiển thị sales target của nhân viên
    def action_view_employee_sales_target(self):
        self.ensure_one()
        department_sales_target_id = self.env['hr.department.sales.target'].search([('department_id', '=', self.department_id.id), ('year', '=', fields.Date.today().year), ('month', '=', fields.Date.today().month)], limit=1)

        return {
            'type': 'ir.actions.act_window',
            'name': 'Sales Target',
            'res_model': 'hr.employee.sales.target',
            'view_mode': 'tree,form',
            'domain': [('employee_id', '=', self.id)],
            'context': {
                'default_employee_id': self.id,
                'default_department_id': self.department_id.id,
                'default_department_sales_target_id': department_sales_target_id.id,
                'default_company_id': self.company_id.id,
            }
        }
    
    # Số buổi dạy của nhân viên pt
    hr_employee_booking_pt_ids = fields.One2many('hr.employee.booking.pt', 'employee_id', string='Employee Booking PT')
    booking_pt_count = fields.Integer(string='Tổng số tháng dạy', compute="_compute_booking_pt_count", store=True)
    @api.depends('hr_employee_booking_pt_ids')
    def _compute_booking_pt_count(self):
        for record in self:
            record.booking_pt_count = len(record.hr_employee_booking_pt_ids)
            
    # Hiển thị Booking PT của nhân viên
    def action_view_employee_booking_pt(self):
        self.ensure_one()

        return {
            'type': 'ir.actions.act_window',
            'name': 'Booking PT',
            'res_model': 'hr.employee.booking.pt',
            'view_mode': 'tree,form',
            'domain': [('employee_id', '=', self.id)],
            'context': {
                'default_employee_id': self.id,
                'default_department_id': self.department_id.id,
            }
        }