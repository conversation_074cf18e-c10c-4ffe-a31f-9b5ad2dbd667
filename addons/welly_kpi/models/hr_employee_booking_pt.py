from odoo import models, fields, api


class HrEmployeeBookingPt(models.Model):
    _name = 'hr.employee.booking.pt'
    _description = 'Nhân viên booking PT'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    # Hiển thị list view theo thứ tự giảm dần của năm và tháng
    _order = 'year desc, month desc'

    # Unique constraint for employee_id, year, month
    _sql_constraints = [
        ('unique_employee_id_year_month', 'unique(employee_id, year, month)',
         'Booking PT cho nhân viên này trong tháng này đã tồn tại')
    ]

    name = fields.Char(string='Tên', compute='_compute_name', store=True)

    @api.depends('year', 'month')
    def _compute_name(self):
        for record in self:
            record.name = f'BPT/{record.month}/{record.year}'

    # Thông tin thời gian
    year = fields.Integer(string='Năm', required=True, default=str(fields.Date.today().year), tracking=True)

    month = fields.Selection(selection=[
        ('01', 'Tháng 01'), ('02', 'Tháng 02'), ('03', 'Tháng 03'), ('04', 'Tháng 04'),
        ('05', '<PERSON>háng 05'), ('06', 'Tháng 06'), ('07', 'Tháng 07'), ('08', 'Tháng 08'),
        ('09', 'Tháng 09'), ('10', 'Tháng 10'), ('11', 'Tháng 11'), ('12', 'Tháng 12'),
    ], string='Tháng', required=True, tracking=True, default=str(fields.Date.today().month).zfill(2))

    date = fields.Date(string='Ngày', compute='_compute_date', store=True)

    @api.depends('year', 'month')
    def _compute_date(self):
        for record in self:
            record.date = fields.Date.to_date(f'{record.year}-{record.month}-01')

    # Thông tin phòng ban
    department_id = fields.Many2one('hr.department', compute='_compute_department_id', string='Phòng ban', store=True,
                                    readonly=True)

    @api.depends('employee_id.department_id')
    def _compute_department_id(self):
        today = fields.Date.today()
        for record in self:
            if today.year == record.year and str(today.month).zfill(2) == record.month:
                record.department_id = record.employee_id.department_id

    # Thông tin nhân viên
    employee_id = fields.Many2one('hr.employee', string='Nhân viên')
    email = fields.Char(string='Email', related='employee_id.work_email', store=True, readonly=True)

    # Thông tin chức vụ
    job_id = fields.Many2one('hr.job', string='Chức vụ', compute='_compute_job_id', store=True, readonly=True)

    @api.depends('employee_id.job_id')
    def _compute_job_id(self):
        today = fields.Date.today()
        for record in self:
            if today.year == record.year and str(today.month).zfill(2) == record.month:
                record.job_id = record.employee_id.job_id

    # Đơn vị tiền tệ
    currency_id = fields.Many2one('res.currency', string='Đơn vị tiền tệ',
                                  related='department_id.company_id.currency_id', store=True)

    # Thông tin buổi dạy
    calendar_event_ids = fields.One2many('calendar.event', 'hr_employee_booking_pt_id', string='Booking PT')

    # Thông tin khách hàng
    calendar_attendee_ids = fields.One2many(comodel_name='calendar.attendee', inverse_name='hr_employee_booking_pt_id',
                                            string='Booking PT')

    # Tính toán số buổi dạy
    num_booking_pt = fields.Integer(string='Tổng số buổi dạy', compute="_compute_num_booking_pt", store=True)

    @api.depends('calendar_attendee_ids', 'calendar_attendee_ids.confirm_commission')
    def _compute_num_booking_pt(self):
        for record in self:
            # Đếm số event của calendar_attendee_ids không trùng nhau của các attendee có confirm_commission = True
            record.num_booking_pt = len({
                attendee.event_id
                for attendee in record.calendar_attendee_ids
                if attendee.confirm_commission and attendee.event_id
            })

    # Tính toán tổng tiền dạy của attendee được đánh dấu tính COM
    revenue = fields.Float(string='Tổng tiền dạy', compute='_compute_revenue', store=True)

    @api.depends('calendar_attendee_ids', 'calendar_attendee_ids.confirm_commission', 'calendar_attendee_ids.session_revenue')
    def _compute_revenue(self):
        for record in self:
            record.revenue = sum(
                attendee.session_revenue if attendee.session_revenue else 0
                for attendee in record.calendar_attendee_ids
                if attendee.confirm_commission
            )

    # Tính toán lương buổi tặng
    gift_session_commission_salary = fields.Monetary(string='Lương buổi tặng tính COM',
                                                     compute='_compute_gift_session_commission_salary', store=True)

    @api.depends('calendar_attendee_ids', 'calendar_attendee_ids.gift_session_commission', 'calendar_attendee_ids.confirm_commission')
    def _compute_gift_session_commission_salary(self):
        for record in self:
            record.gift_session_commission_salary = sum(
                attendee.gift_session_commission if attendee.gift_session_commission else 0
                for attendee in record.calendar_attendee_ids
                if attendee.confirm_commission
            )

