from odoo import models, fields, api, exceptions
from ..fields import selection
import json



class HrEmployeeSalesTarget(models.Model):
    _name = 'hr.employee.sales.target'
    _description = '<PERSON><PERSON>h số nhân viên'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string='Tên', compute='_compute_name', store=True)
    @api.depends('year', 'month')
    def _compute_name(self):
        for record in self:
            record.name = f'EST/{record.month}/{record.year}'
    
    # Thông tin thời gian
    year = fields.Integer(string='Năm', related='department_sales_target_id.year', store=True, readonly=True)
    month = fields.Selection(string='Tháng', related='department_sales_target_id.month', store=True, readonly=True)
    date = fields.Datetime(string='Ngày', compute='_compute_date', store=True)

    # Tính ngày cho báo cáo là ngày đầu tháng
    @api.depends('year', 'month')
    def _compute_date(self):
        for record in self:
            record.date = fields.Datetime.to_datetime(f'{record.year}-{record.month}-01 00:00:00')
    
    # Thông tin phòng ban
    department_id = fields.Many2one('hr.department', related='employee_id.department_id', string='Phòng ban hiện tại', store=True, readonly=True)
    department_sales_target_id = fields.Many2one('hr.department.sales.target', string='Target phòng ban', store=True, required=True, readonly=True)
            
    # Đây là phòng ban của target, không phải phòng ban hiện tại của nhân viên được thêm vào để sử dụng sql để lên báo cáo
    sales_target_department_id = fields.Many2one('hr.department', string='Phòng ban', related='department_sales_target_id.department_id', store=True, readonly=True)
    sales_target_department_type = fields.Selection(related='sales_target_department_id.department_type', store=True, readonly=True)
    
    # Thông tin nhân viên
    employee_id = fields.Many2one('hr.employee', string='Nhân viên', required=True, default=lambda self: self._context.get('active_id'))
    # Field này hỗ trợ cho việc tạo domain cho field employee_id
    employee_name_rel = fields.Char(string='Tên nhân viên', related='employee_id.name')
    # Compute domain
    @api.onchange('employee_name_rel')
    def _compute_employee_id_domain(self):
        self.ensure_one()
        # Lấy ra danh sách các nhân viên đã có trong target phòng ban
        exist_employee_ids = self.department_sales_target_id.personal_sales_target_ids.employee_id.ids
        
        # Trả về domain cho field employee_id
        return {
            'domain': {
                'employee_id': [('department_id', '=', self.sales_target_department_id.id), ('id', 'not in', exist_employee_ids), ('active', '=', True)]
            }
        }
    job_id = fields.Many2one('hr.job', string='Chức vụ', compute='_compute_job_id', store=True, readonly=True)

    @api.depends('employee_id')
    def _compute_job_id(self):
        for record in self:
            record.job_id = record.employee_id.job_id

    email = fields.Char(string='Email', related='employee_id.work_email', store=True, readonly=True)
    job_title = fields.Char(string='Chức danh công việc', related='employee_id.job_title', store=True, readonly=True)
        
    # Thông tin target
    target = fields.Float(string='Target', default=0.0, required=True, tracking=True)
    target_reached = fields.Float(string='Tiến độ', compute='_compute_target', store=True)
    
    
    # Thông tin doanh số
    sales_line_ids = fields.One2many('hr.employee.sales.line', 'employee_sales_target_id', string='Doanh số cá nhân')
    revenue = fields.Float(string='Doanh số thực tế', compute='_compute_target', store=True)

    revenue_display = fields.Float(
        string='Doanh số thực tế',
        compute='_compute_revenue_display',
        digits=(16, 0),
        store=False,
        help='Làm tròn dữ liệu Doanh số thực tế'
    )

    @api.depends('revenue')
    def _compute_revenue_display(self):
        for rec in self:
            rec.revenue_display = round(rec.revenue or 0)
    # Đơn vị tiền tệ
    currency_id = fields.Many2one('res.currency', string='Đơn vị tiền tệ', related='department_sales_target_id.currency_id', store=True)
    # Trạng thái
    state = fields.Selection(selection=selection.State._selection, string='Trạng thái', related='department_sales_target_id.state', store=True, readonly=True, tracking=True)
    
    # Hiển thị list view theo thứ tự giảm dần của năm và tháng
    _order = 'year desc, month desc'
    
    # Unique constraint for employee_id, year, month
    _sql_constraints = [
        ('unique_employee_id_year_month', 'unique(employee_id, year, month)', 'Mục tiêu doanh số cho nhân viên này trong tháng này đã tồn tại')
    ]

    # Tính toán doanh số và tiến độ
    @api.depends('target', 'sales_line_ids', 'sales_line_ids.revenue')
    def _compute_target(self):
        for record in self:
            record.revenue = sum(line.revenue for line in record.sales_line_ids)
            record.target_reached = (record.revenue / record.target) * 100 if record.target else 0

    # Action Xác nhận
    def action_confirm(self):
        for record in self:
            record.state = selection.State.POSTED
    
    # Không cho phép xoá khi còn dòng doanh số    
    def unlink(self):
        for record in self:
            if record.sales_line_ids:
                raise exceptions.UserError("Không thể xóa mục tiêu doanh số có các dòng doanh số liên quan.")
        return super(HrEmployeeSalesTarget, self).unlink()
