<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form View -->
    <record id="view_hr_employee_booking_pt_form" model="ir.ui.view">
        <field name="name">hr.employee.booking.pt.form</field>
        <field name="model">hr.employee.booking.pt</field>
        <field name="arch" type="xml">
            <form string="Booking PT">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="employee_id" attrs="{'readonly': True}"/>
                            <field name="month" attrs="{'readonly': True}"/>
                            <field name="year" attrs="{'readonly': True}" widget="raw_number"/>
                            <field name="job_id"/>
                            <field name="department_id"/>
                        </group>
                        <group>
                            <field name="currency_id" invisible="1"/>
                            <field name="num_booking_pt"/>
                            <field name="revenue" widget="monetary"/>
                            <field name="gift_session_commission_salary"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Booking PT" name="booking_details">
                            <field name="calendar_attendee_ids">
                                <tree create="0" delete="0" edit="0" limit="10">
                                    <field name="currency_id" invisible="1"/>
                                    <field name="date" optional="show"/>
                                    <field name="event_id" widget="many2one_tags" string="Tên lịch" optional="hide"/>
                                    <field name="partner_id" string="Khách hàng" widget="many2one_tags" optional="show"/>
                                    <field name="contract_id" string="Hợp đồng" widget="many2one_tags" optional="show"/>
                                    <field name="session_revenue" string="Số tiền/buổi" sum="Tổng số tiền/buổi" optional="show"/>
                                    <field name="gift_session_commission" string="Hoa hồng buổi tặng" sum="Tổng hoa hồng buổi tặng" optional="show"/>
                                    <field name="state" string="Trạng thái KH" optional="show"/>
                                    <field name="main_session_char" string="Buổi chính số" optional="show"/>
                                    <field name="gift_session_char" string="Buổi tặng số" optional="show"/>
                                    <field name="confirm_commission" string="Được tính COM" optional="hide"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Tree View -->
    <record id="view_hr_employee_booking_pt_tree" model="ir.ui.view">
        <field name="name">hr.employee.booking.pt.tree</field>
        <field name="model">hr.employee.booking.pt</field>
        <field name="arch" type="xml">
            <tree string="Booking PT">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="department_id"/>
                <field name="job_id"/>
                <field name="num_booking_pt"/>
                <field name="revenue" widget="monetary"/>
                <field name="gift_session_commission_salary" widget="monetary"/>
                <field name="currency_id" invisible="1"/>
            </tree>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_hr_employee_booking_pt_search" model="ir.ui.view">
        <field name="name">hr.employee.booking.pt.search</field>
        <field name="model">hr.employee.booking.pt</field>
        <field name="arch" type="xml">
            <search string="Tìm kiếm Booking PT">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="department_id"/>
                <field name="job_id"/>
                <field name="year"/>
                <field name="month"/>
                <separator/>
                <filter string="Năm hiện tại" name="year_current" domain="[('year', '=', datetime.datetime.now().year)]"/>
                <filter string="Tháng hiện tại" name="month_current" domain="[('month', '=', datetime.datetime.now().strftime('%m'))]"/>
                <group expand="0" string="Group By">
                    <filter string="Nhân viên" name="employee" context="{'group_by': 'employee_id'}"/>
                    <filter string="Phòng ban" name="department" context="{'group_by': 'department_id'}"/>
                    <filter string="Chức vụ" name="job" context="{'group_by': 'job_id'}"/>
                    <filter string="Năm" name="year" context="{'group_by': 'year'}"/>
                    <filter string="Tháng" name="month" context="{'group_by': 'month'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_hr_employee_booking_pt" model="ir.actions.act_window">
        <field name="name">Booking PT</field>
        <field name="res_model">hr.employee.booking.pt</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_year_current': 1, 'search_default_month_current': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Tạo booking PT đầu tiên!
            </p>
        </field>
    </record>

    <!-- inherit hr.view_employee_form -->
    <record id="view_employee_form_booking_pt_smartbutton" model="ir.ui.view">
        <field name="name">view.employee.form.booking.pt.smartbutton</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form" />
        <field name="priority" eval="121" />
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <field name="booking_pt_count" invisible="1" />
                <button name="action_view_employee_booking_pt" class="oe_stat_button"
                    icon="fa-list-ul" type="object">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value">
                            <field name="booking_pt_count" />
                        </span>
                        <span class="o_stat_text">Booking PT</span>
                    </div>
                </button>
            </div>
        </field>
    </record>
</odoo>