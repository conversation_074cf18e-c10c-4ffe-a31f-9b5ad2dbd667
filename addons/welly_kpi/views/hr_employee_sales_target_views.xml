<!-- hr_employee_sales_target_views.xml -->
<odoo>
    <record id="view_hr_employee_sales_target_form" model="ir.ui.view">
        <field name="name">hr.employee.sales.target.form</field>
        <field name="model">hr.employee.sales.target</field>
        <field name="arch" type="xml">
            <form string="Employee Sales Target">
                <header>
                    <field name="state" widget="statusbar" statusbar_visible="draft,posted" />
                </header>
                <sheet>
                    <group>
                        <group>
                            <group>
                                <field name="month" readonly="1" />
                                <field name="year" readonly="1" widget="raw_number" />
                                <field name="sales_target_department_id" readonly="1"
                                    groups="welly_kpi.sales_target_manager" />
                                <field name="department_sales_target_id" readonly="1"
                                    groups="welly_kpi.sales_target_manager" />
                            </group>
                        </group>
                        <group>
                            <group>
                                <field name="target" widget="monetary"
                                    options="{'currency_field': 'currency_id'}" readonly="1" />
                                <field name="revenue_display" widget="monetary"
                                    options="{'currency_field': 'currency_id'}" readonly="1" />
                                <field name="target_reached" widget="progressbar2" readonly="1" />
                            </group>
                        </group>
                    </group>
                    <notebook>
                        <page string="Doanh số cá nhân">
                            <field name="sales_line_ids" readonly="1">
                                <tree editable="bottom">
                                    <field name="payment_id"
                                        attrs="{'invisible': [('payment_state', '!=', 'posted')]}" />
                                    <field name="move_id" string="Hoá đơn"
                                        attrs="{'invisible': [('payment_state', '!=', 'posted')]}" />
                                    <field name="payment_create_date"
                                        attrs="{'invisible': [('payment_state', '!=', 'posted')]}" />
                                    <field name="payment_date"
                                        attrs="{'invisible': [('payment_state', '!=', 'posted')]}" />
                                    <field name="journal_id" string="Phương thức thanh toán"
                                        attrs="{'invisible': [('payment_state', '!=', 'posted')]}" />
                                    <field name="partner_id"
                                        attrs="{'invisible': [('payment_state', '!=', 'posted')]}" />
                                    <field name="rate"
                                        attrs="{'invisible': [('payment_state', '!=', 'posted')]}" />
                                    <field name='currency_id' invisible="1" />
                                    <field name="revenue" widget="monetary"
                                        options="{'currency_field': 'currency_id'}"
                                        attrs="{'invisible': [('payment_state', '!=', 'posted')]}" />
                                    <field name="amount" widget="monetary"
                                        options="{'currency_field': 'currency_id'}"
                                        attrs="{'invisible': [('payment_state', '!=', 'posted')]}" />
                                    <field name="payment_state" string="Trạng thái"
                                        attrs="{'invisible': [('payment_state', '!=', 'posted')]}" />
                                    <field name="invoice_state" string="Trạng thái hoá đơn"
                                        attrs="{'invisible': [('payment_state', '!=', 'posted')]}" />
                                    <field name="invoice_approved_state" string="Trạng thái phê duyệt"
                                        attrs="{'invisible': [('payment_state', '!=', 'posted')]}" />
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <!-- Message_ids -->
                <div class="oe_chatter">
                    <field name="message_ids" />
                    <field name="message_follower_ids" groups="base.group_user" />
                    <field name="activity_ids" />
                </div>
            </form>
        </field>
    </record>
    <!-- hr.employee.sales.target tree view -->
    <record id="view_hr_employee_sales_target_tree" model="ir.ui.view">
        <field name="name">hr.employee.sales.target.tree</field>
        <field name="model">hr.employee.sales.target</field>
        <field name="arch" type="xml">
            <tree string="Employee Sales Target" create="false">
                <field name="currency_id" invisible="1" />
                <field name="name" readonly="1" />
                <field name="employee_id"/>
                <field name="month" readonly="1" />
                <field name="year" readonly="1" widget="raw_number" />
                <field name="sales_target_department_id" readonly="1" />
                <field name="target" widget="monetary" options="{'currency_field': 'currency_id'}" readonly="1" />
                <field name="revenue_display" readonly="1" />
                <field name="target_reached" widget="progressbar2" readonly="1" />
            </tree>
        </field>
    </record>

    <record id="action_hr_employee_sales_target" model="ir.actions.act_window">
        <field name="name">Employee Sales Target</field>
        <field name="res_model">hr.employee.sales.target</field>
        <field name="view_mode">tree,form</field>
    </record>

    <!-- inherit hr.view_employee_form -->
    <record id="view_employee_form_sales_target_smartbutton" model="ir.ui.view">
        <field name="name">view.employee.form.sales.target.smartbutton</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form" />
        <field name="priority" eval="1000" />
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <field name="sales_target_count" invisible="1" />
                <button name="action_view_employee_sales_target" class="oe_stat_button"
                    icon="fa-list-ul" type="object">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value">
                            <field name="sales_target_count" />
                        </span>
                        <span class="o_stat_text">Sales Target</span>
                    </div>
                </button>
            </div>
        </field>
    </record>
</odoo>
