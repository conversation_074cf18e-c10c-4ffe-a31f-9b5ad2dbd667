# -*- coding: utf-8 -*-
from odoo import models, fields, api, _, Command
from odoo.exceptions import ValidationError
from dateutil.relativedelta import relativedelta
import logging

_logger = logging.getLogger(__name__)

class DeviceSyncFaceWizard(models.TransientModel):
    _name = 'device.sync.face.wizard'
    _description = 'Đồng bộ khuôn mặt sang thiết bị khác'

    source_device_id = fields.Many2one('device.device', string='Thiết bị nguồn', readonly=True)
    target_device_ids = fields.Many2many(
        'device.device',
        string='Thiết bị đích',
        domain="[('status', '=', 'online'), ('id', '!=', source_device_id)]",
        required=True
    )

    @api.model
    def default_get(self, fields_list):
        """
        Thiết lập giá trị mặc định cho các trường khi mở wizard
        """
        res = super(DeviceSyncFaceWizard, self).default_get(fields_list)
        active_id = self.env.context.get('active_id')
        if active_id:
            res['source_device_id'] = active_id
        return res

    def sync_face_job_chunk(self, device_ids, partner_ids, job_index=0, total_jobs=1, total_partners=0):
        """
        Hàm thực hiện công việc đồng bộ khuôn mặt trong queue job cho một chunk nhỏ
        
        :param device_ids: Danh sách ID của thiết bị đích
        :param partner_ids: Danh sách ID của đối tác cần đồng bộ
        :param job_index: Chỉ số của job hiện tại (để hiển thị thông báo tiến trình)
        :param total_jobs: Tổng số jobs
        :param total_partners: Tổng số đối tác cần đồng bộ
        """
        # Lấy thông tin các thiết bị đích
        devices = self.env['device.device'].browse(device_ids)

        # Kiểm tra trạng thái online của các thiết bị đích
        for device in devices:
            try:
                device.check_online()
            except Exception as e:
                _logger.warning(f"Thiết bị {device.name} không online: {e}")
                # Tiếp tục với thiết bị khác

        # Lấy danh sách partner
        partners = self.env['res.partner'].browse(partner_ids)

        # Kiểm tra danh sách partner
        if not partners:
            return True

        # Đếm số lượng khuôn mặt được sync thành công
        count_success = 0
        # Đếm số lượng khuôn mặt được sync
        count_total = 0
        
        # Đồng bộ khuôn mặt cho từng thiết bị
        for device in devices:
            _logger.info(f"Chunk {job_index+1}/{total_jobs}: Bắt đầu sync khuôn mặt cho thiết bị {device.name}")
            
            # Lấy thông tin cũ từ device.sync
            device_sync_ids = device.device_sync_ids.filtered(
                lambda ds: ds.sync_type == 'face' and ds.state == 'success'
            ).mapped('enroll_id')
            
            # Sync khuôn mặt cho từng đối tác trong chunk này
            for partner in partners.filtered(lambda p: p.id not in device_sync_ids and p.face_image_sign_up):
                count_total += 1
                state = 'failed'
                failure_reason = False
                
                try:
                    data = device.set_user_face(partner.id, partner.name, partner.face_image_sign_up.decode())
                    if data.get('result'):
                        count_success += 1
                        _logger.info(f"Chunk {job_index+1}/{total_jobs}: {count_total}. Đã sync {partner.name} khuôn mặt thành công trên thiết bị {device.name}")
                        state = 'success'
                        partner.device_sign_up = [Command.link(device.id)]
                    else:
                        msg = data.get('msg', 'Không xác định')
                        _logger.info(f"Chunk {job_index+1}/{total_jobs}: {count_total}. Lỗi sync {partner.name} khuôn mặt thất bại trên thiết bị {device.name}: {msg}")
                        failure_reason = msg
                except Exception as e:
                    error_msg = str(e)
                    _logger.info(f"Chunk {job_index+1}/{total_jobs}: {count_total}. Lỗi sync {partner.name} khuôn mặt thất bại trên thiết bị {device.name}: {error_msg}")
                    failure_reason = error_msg
                finally:
                    # Lưu lại thông tin đồng bộ
                    sync_vals = {
                        'device_id': device.id,
                        'enroll_id': partner.id,
                        'sync_type': 'face',
                        'state': state,
                    }
                    
                    # Thêm lý do thất bại nếu có
                    if state == 'failed' and failure_reason:
                        sync_vals['failure_reason'] = failure_reason
                        
                    self.env['device.sync'].with_context(no_reset_password=True).create(sync_vals)
                    # Commit để khi retry job không xử lý lại các record đã xử lý đã đồng bộ
                    self.env.cr.commit()

        # Thông báo tiến trình hoàn thành chunk
        _logger.info(f"Chunk {job_index+1}/{total_jobs} hoàn thành: {count_success}/{count_total} khuôn mặt thành công")
        return {
            'success': count_success,
            'total': count_total
        }

    def sync_face_job_summary(self, chunk_results, device_ids):
        """
        Tổng hợp kết quả từ các chunks và hiển thị thông báo kết quả

        :param chunk_results: Danh sách kết quả từ các chunks
        :param device_ids: Danh sách ID của thiết bị đích
        """
        _logger.info(f"Bắt đầu tổng hợp kết quả từ {len(chunk_results)} chunks")
        
        # Tính tổng số lượng khuôn mặt đã được đồng bộ thành công và tổng số
        total_success = 0
        total_processed = 0
        
        for result in chunk_results:
            job_uuid = result.get('job_uuid')
            try:
                # Lấy kết quả từ job
                job = self.env['queue.job'].sudo().search([('uuid', '=', job_uuid)], limit=1)
                if job and job.result:
                    chunk_result = job.result
                    if isinstance(chunk_result, dict):
                        total_success += chunk_result.get('success', 0)
                        total_processed += chunk_result.get('total', 0)
            except Exception as e:
                _logger.error(f"Lỗi khi lấy kết quả từ job {job_uuid}: {e}")
        
        _logger.info(f"Kết quả tổng hợp: {total_success}/{total_processed} khuôn mặt thành công")
        
        # Tạo action để mở device.sync
        action = {
            'type': 'ir.actions.act_window',
            'name': _('Lịch sử đồng bộ khuôn mặt'),
            'res_model': 'device.sync',
            'views': [[False,'tree']],
            'domain': [('device_id', 'in', device_ids)],
            'context': {
                'create': False,
                'edit': False,
                'search_default_device_id': device_ids,
                'search_default_sync_type': 'face',
            },
        }

        # Gửi thông báo cho người dùng
        self.send_notify(
            title=_("Đồng bộ dữ liệu khuôn mặt hoàn tất"),
            message=_("Đã đồng bộ %s/%s khuôn mặt thành công") % (total_success, total_processed),
            user_ids=self.env.user.ids,
            type='success',
            action=action
        )
        
        return {
            'success': total_success,
            'total': total_processed
        }

    def sync_face_job(self):
        """
        Hàm thực hiện công việc đồng bộ khuôn mặt trong queue job (được giữ lại để tương thích với mã cũ)
        """
        # Kiểm tra trạng thái online của các thiết bị đích
        for device in self.target_device_ids:
            device.check_online()

        # Lấy danh sách partner đã đăng ký khuôn mặt trên thiết bị nguồn
        source_device = self.source_device_id
        partners = source_device.partner_ids.filtered(lambda p: p.face_image_sign_up)

        # Kiểm tra danh sách partner
        if not partners:
            return True

        # Đếm số lượng khuôn mặt được sync thành công
        count_success = 0
        # Đếm số lượng khuôn mặt được sync
        count_total = 0
        
        # Đồng bộ khuôn mặt cho từng thiết bị
        for device in self.target_device_ids:
            _logger.info(f"Bắt đầu sync khuôn mặt cho thiết bị {device.name}")
            
            # Lấy thông tin cũ từ device.sync
            device_sync_ids = device.device_sync_ids.filtered(
                lambda ds: ds.sync_type == 'face' and ds.state == 'success'
            ).mapped('enroll_id')
            
            # Sync khuôn mặt cho từng đối tác
            for partner in partners.filtered(lambda p: p.id not in device_sync_ids and p.face_image_sign_up):
                count_total += 1
                state = 'failed'
                failure_reason = False
                
                try:
                    data = device.set_user_face(partner.id, partner.name, partner.face_image_sign_up.decode())
                    if data.get('result'):
                        count_success += 1
                        _logger.info(f"{count_total}. Đã sync {partner.name} khuôn mặt thành công trên thiết bị {device.name}")
                        state = 'success'
                        partner.device_sign_up = [Command.link(device.id)]
                    else:
                        msg = data.get('msg', 'Không xác định')
                        _logger.info(f"{count_total}. Lỗi sync {partner.name} khuôn mặt thất bại trên thiết bị {device.name}: {msg}")
                        failure_reason = msg
                except Exception as e:
                    error_msg = str(e)
                    _logger.info(f"{count_total}. Lỗi sync {partner.name} khuôn mặt thất bại trên thiết bị {device.name}: {error_msg}")
                    failure_reason = error_msg
                finally:
                    # Lưu lại thông tin đồng bộ
                    sync_vals = {
                        'device_id': device.id,
                        'enroll_id': partner.id,
                        'sync_type': 'face',
                        'state': state,
                    }
                    
                    # Thêm lý do thất bại nếu có
                    if state == 'failed' and failure_reason:
                        sync_vals['failure_reason'] = failure_reason
                        
                    self.env['device.sync'].with_context(no_reset_password=True).create(sync_vals)
                    # Commit để khi retry job không xử lý lại các record đã xử lý đã đồng bộ
                    self.env.cr.commit()
        
        # Tạo action để mở device.sync
        action = {
            'type': 'ir.actions.act_window',
            'name': _('Lịch sử đồng bộ khuôn mặt'),
            'res_model': 'device.sync',
            'views': [[False,'tree']],
            'domain': [('device_id', 'in', self.target_device_ids.ids)],
            'context': {
                'create': False,
                'edit': False,
                'search_default_device_id': self.target_device_ids.ids,
                'search_default_sync_type': 'face',
                'search_default_state': 'success',
            },
        }

        # Gửi thông báo cho người dùng
        self.send_notify(
            title=_("Đồng bộ dữ liệu khuôn mặt hoàn tất"),
            message=_("Đã đồng bộ %s/%s khuôn mặt thành công") % (count_success, count_total),
            user_ids=self.env.user.ids,
            type='success',
            action=action
        )

    def action_sync_face(self):
        """
        Tạo queue job để thực hiện đồng bộ khuôn mặt theo chunks
        """
        self.ensure_one()
        # Kiểm tra xem có thiết bị nào được chọn không
        if not self.target_device_ids:
            raise ValidationError(_("Vui lòng chọn ít nhất một thiết bị đích để đồng bộ"))
        
        # Lấy danh sách partner đã đăng ký khuôn mặt trên thiết bị nguồn
        source_device = self.source_device_id
        partners = source_device.partner_ids.filtered(lambda p: p.face_image_sign_up)
        
        # Kiểm tra danh sách partner
        if not partners:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Thông báo',
                    'message': 'Không có khuôn mặt nào cần đồng bộ',
                    'type': 'warning',
                    'sticky': False,
                }
            }
            
        # Lấy danh sách ID của thiết bị đích
        target_device_ids = self.target_device_ids.ids
        
        # Chia thành các chunk nhỏ, mỗi chunk tối đa 50 partner
        chunk_size = 50
        partner_ids = partners.ids
        total_partners = len(partner_ids)
        chunks = [partner_ids[i:i+chunk_size] for i in range(0, total_partners, chunk_size)]
        total_chunks = len(chunks)
         
        _logger.info(f"Tạo {total_chunks} chunks để đồng bộ {total_partners} khuôn mặt, mỗi chunk tối đa {chunk_size} khuôn mặt")        # Tạo các queue job để xử lý từng chunk
        chunk_job_ids = []
        for i, chunk in enumerate(chunks):
            job = self.with_delay(priority=1, max_retries=3, channel='face_sync').sync_face_job_chunk(
                target_device_ids, 
                chunk,
                job_index=i,
                total_jobs=total_chunks,
                total_partners=total_partners
            )
            chunk_job_ids.append(job.uuid)
          # Tạo job tổng hợp kết quả và gửi thông báo, không phụ thuộc vào các job chunk
        now = fields.Datetime.now()
        self.with_delay(
            priority=20, 
            max_retries=1, 
            channel='face_sync', 
        ).sync_face_job_summary(
            [{'job_uuid': uuid} for uuid in chunk_job_ids], 
            target_device_ids
        )
        
        # Hiển thị thông báo và đóng wizard
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
            'title': 'Thông báo', 
            'message': f'Đã tạo {total_chunks} công việc đồng bộ khuôn mặt và đưa vào hàng đợi xử lý',
            'type': 'success',
            'sticky': False,
            'next': {'type': 'ir.actions.act_window_close'}
            }
        }
