# Manual Testcase cho Task #4986

## <PERSON><PERSON> tả
Test các tính năng cải thiện UX cho tab cấu hình trong hr.job:
- Validation tính liên tục của các khoảng giá trị
- Auto-sorting theo gi<PERSON> trị bắt đầu tăng dần

## Chuẩn bị
1. <PERSON><PERSON><PERSON> nhập vào hệ thống Odoo với quyền HR Manager
2. Truy cập menu: <PERSON><PERSON><PERSON> viên > C<PERSON><PERSON> hình > Chức vụ

## Testcase 1: Validation tính liên tục - <PERSON><PERSON> chế hoa hồng bán

### Bước 1.1: Tạo chức vụ mới
- [ ] T<PERSON><PERSON> chức vụ mới với tên "Test Commission Sale"
- [ ] Chuyển đến tab "<PERSON><PERSON> chế hoa hồng bán"
- [ ] Chọn "Loại chỉ tiêu hoa hồng bán" = "<PERSON><PERSON>h số"
- [ ] Chọn "Kiểu dữ liệu hoa hồng bán" = "Tỷ Lệ %"

### Bước 1.2: <PERSON> khoảng giá trị không liên tục
- [ ] Thêm dòng 1:
  - Chỉ tiêu: "Mức 1"
  - Toán tử bắt đầu: ">="
  - Giá trị bắt đầu: 0
  - Toán tử kết thúc: "<"
  - Giá trị kết thúc: 50
  - Commission Bán: 5
- [ ] Thêm dòng 2:
  - Chỉ tiêu: "Mức 2"
  - Toán tử bắt đầu: ">="
  - Giá trị bắt đầu: 100
  - Toán tử kết thúc: "<"
  - Giá trị kết thúc: 150
  - Commission Bán: 10
- [ ] Nhấn "Lưu"
- [ ] **Kết quả mong đợi:** Hệ thống báo lỗi "Có khoảng trống từ 50 đến 100. Các giá trị cấu hình phải liên tục."

### Bước 1.3: Sửa để tạo khoảng liên tục
- [ ] Sửa dòng 2:
  - Giá trị bắt đầu: 50 (thay vì 100)
- [ ] Nhấn "Lưu"
- [ ] **Kết quả mong đợi:** Hệ thống lưu thành công, không báo lỗi

## Testcase 2: Auto-sorting - Cơ chế hoa hồng dạy

### Bước 2.1: Tạo chức vụ mới
- [ ] Tạo chức vụ mới với tên "Test Commission PT"
- [ ] Chuyển đến tab "Cơ chế hoa hồng dạy"
- [ ] Chọn "Loại chỉ tiêu hoa hồng dạy" = "Doanh số"
- [ ] Chọn "Kiểu dữ liệu hoa hồng dạy" = "Tỷ Lệ %"

### Bước 2.2: Thêm các dòng không theo thứ tự
- [ ] Thêm dòng 1:
  - Chỉ tiêu: "Mức cao"
  - Giá trị bắt đầu: 100, Giá trị kết thúc: 150
  - Commission Dạy: 15
- [ ] Thêm dòng 2:
  - Chỉ tiêu: "Mức thấp"
  - Giá trị bắt đầu: 0, Giá trị kết thúc: 50
  - Commission Dạy: 5
- [ ] Thêm dòng 3:
  - Chỉ tiêu: "Mức trung"
  - Giá trị bắt đầu: 50, Giá trị kết thúc: 100
  - Commission Dạy: 10
- [ ] Nhấn "Lưu"

### Bước 2.3: Kiểm tra auto-sorting
- [ ] Refresh trang hoặc mở lại record
- [ ] **Kết quả mong đợi:** Các dòng được sắp xếp theo thứ tự:
  1. "Mức thấp" (0-50)
  2. "Mức trung" (50-100)
  3. "Mức cao" (100-150)

## Testcase 3: Validation tính liên tục - Hệ số lương K

### Bước 3.1: Tạo chức vụ mới
- [ ] Tạo chức vụ mới với tên "Test K Coefficient"
- [ ] Chuyển đến tab "Hệ số lương K"
- [ ] Chọn "Loại chỉ tiêu HS lương" = "Doanh số"

### Bước 3.2: Test khoảng giá trị có khoảng trống
- [ ] Thêm dòng 1:
  - Chỉ tiêu: "K1"
  - Toán tử bắt đầu: ">="
  - Giá trị bắt đầu: 0
  - Toán tử kết thúc: "<="
  - Giá trị kết thúc: 30
  - Hệ số lương: 1.0
- [ ] Thêm dòng 2:
  - Chỉ tiêu: "K2"
  - Toán tử bắt đầu: ">="
  - Giá trị bắt đầu: 50
  - Toán tử kết thúc: "<="
  - Giá trị kết thúc: 80
  - Hệ số lương: 1.5
- [ ] Nhấn "Lưu"
- [ ] **Kết quả mong đợi:** Hệ thống báo lỗi về khoảng trống từ 31 đến 49

### Bước 3.3: Sửa để tạo khoảng liên tục
- [ ] Sửa dòng 2:
  - Giá trị bắt đầu: 31 (thay vì 50)
- [ ] Nhấn "Lưu"
- [ ] **Kết quả mong đợi:** Hệ thống lưu thành công

## Testcase 4: Edge Cases

### Bước 4.1: Test với chỉ 1 dòng cấu hình
- [ ] Tạo chức vụ mới "Test Single Line"
- [ ] Thêm chỉ 1 dòng cấu hình trong bất kỳ tab nào
- [ ] Nhấn "Lưu"
- [ ] **Kết quả mong đợi:** Lưu thành công (không cần kiểm tra tính liên tục)

### Bước 4.2: Test với giá trị 0
- [ ] Tạo dòng cấu hình với giá trị bắt đầu = 0
- [ ] **Kết quả mong đợi:** Hệ thống chấp nhận giá trị 0

### Bước 4.3: Test xóa dòng giữa
- [ ] Tạo 3 dòng liên tục: 0-50, 50-100, 100-150
- [ ] Xóa dòng giữa (50-100)
- [ ] Nhấn "Lưu"
- [ ] **Kết quả mong đợi:** Hệ thống báo lỗi về khoảng trống

### Bước 4.4: Test với khoảng chồng lấn
- [ ] Tạo 2 dòng: 0-60 và 50-100
- [ ] Nhấn "Lưu"
- [ ] **Kết quả mong đợi:** Hệ thống báo lỗi về chồng lấn

## Testcase 5: Performance Test

### Bước 5.1: Test với nhiều dòng cấu hình
- [ ] Tạo 10 dòng cấu hình liên tục
- [ ] Thêm dòng mới ở giữa với thứ tự không đúng
- [ ] Nhấn "Lưu"
- [ ] **Kết quả mong đợi:** Auto-sorting hoạt động nhanh chóng

## Kết quả Test

### Testcase 1: Validation tính liên tục - Cơ chế hoa hồng bán
- [ ] 1.1: Tạo chức vụ mới - PASS/FAIL
- [ ] 1.2: Test khoảng không liên tục - PASS/FAIL
- [ ] 1.3: Sửa thành khoảng liên tục - PASS/FAIL

### Testcase 2: Auto-sorting - Cơ chế hoa hồng dạy
- [ ] 2.1: Tạo chức vụ mới - PASS/FAIL
- [ ] 2.2: Thêm dòng không theo thứ tự - PASS/FAIL
- [ ] 2.3: Kiểm tra auto-sorting - PASS/FAIL

### Testcase 3: Validation tính liên tục - Hệ số lương K
- [ ] 3.1: Tạo chức vụ mới - PASS/FAIL
- [ ] 3.2: Test khoảng có khoảng trống - PASS/FAIL
- [ ] 3.3: Sửa thành khoảng liên tục - PASS/FAIL

### Testcase 4: Edge Cases
- [ ] 4.1: Test với 1 dòng - PASS/FAIL
- [ ] 4.2: Test với giá trị 0 - PASS/FAIL
- [ ] 4.3: Test xóa dòng giữa - PASS/FAIL
- [ ] 4.4: Test chồng lấn - PASS/FAIL

### Testcase 5: Performance Test
- [ ] 5.1: Test với nhiều dòng - PASS/FAIL

## Ghi chú
- Nếu có lỗi, ghi rõ thông báo lỗi thực tế
- Chụp ảnh màn hình nếu cần thiết
- Ghi chú thời gian thực hiện test
