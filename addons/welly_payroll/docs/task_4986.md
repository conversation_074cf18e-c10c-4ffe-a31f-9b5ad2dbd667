# Task #4986

## Description
**Chứ<PERSON> năng:** <PERSON><PERSON><PERSON> viên\\ Cấu hình\\ Chứ<PERSON> vụ (Model hr.job)

**<PERSON><PERSON><PERSON> cầu cho các tab cấu hình:** <PERSON><PERSON> chế hoa hồ<PERSON> b<PERSON>, <PERSON><PERSON> chế hoa hồng <PERSON>, <PERSON><PERSON> số lương K

### <PERSON><PERSON><PERSON> c<PERSON>u chính:
1. **<PERSON><PERSON><PERSON> giá trị cấu hình phải liên tục** (<PERSON>ện tại cho phép nhập ngắt quãng)
   - <PERSON><PERSON> dụ liên tục:
     - Từ 0 - dưới 50
     - Từ 50 - dưới 100  
     - Từ 100 - dưới 150
     - Trên 150
   - <PERSON><PERSON> dụ không liên tục (cần tránh):
     - Từ 0 - dưới 50
     - Từ 100 - dưới 150
     - Trên 150

2. **<PERSON>u khi nhập giá trị chỉ tiêu và lư<PERSON> lạ<PERSON>:** Nếu không vi phạm điều kiện gì thì tự động sắp xếp các bản ghi theo **<PERSON><PERSON><PERSON> trị bắt đầu** tăng dần
   - Hi<PERSON>n tại: Nếu nhập các chỉ tiêu không đúng thứ tự sẽ thông báo lỗi trùng nhau

## Analysis

### Hiện trạng:
1. **Models liên quan:**
   - `hr.job.sale.commission` - Cơ chế hoa hồng bán
   - `hr.job.pt.commission` - Cơ chế hoa hồng dạy  
   - `hr.job.k.coefficient` - Hệ số lương K

2. **Cấu trúc hiện tại của các model:**
   - `begin_op`, `begin_value` - Toán tử và giá trị bắt đầu
   - `end_op`, `end_value` - Toán tử và giá trị kết thúc
   - Phương thức `check_commission_range()` để kiểm tra giá trị nằm trong khoảng

3. **Validation hiện tại:**
   - Kiểm tra không trùng khoảng giá trị
   - Kiểm tra tính hợp lệ của biểu thức
   - Không kiểm tra tính liên tục

### Vấn đề cần giải quyết:
1. **Validation tính liên tục:** Cần thêm validation để đảm bảo các khoảng giá trị liên tục không có khoảng trống
2. **Auto-sorting:** Tự động sắp xếp các bản ghi theo giá trị bắt đầu tăng dần sau khi lưu
3. **UX cải thiện:** Thay vì báo lỗi trùng nhau khi nhập không đúng thứ tự, hệ thống sẽ tự động sắp xếp

## Task Breakdown Checklist

### 1. Phân tích và thiết kế giải pháp
- [x] 1.1: Phân tích logic validation tính liên tục cho các khoảng giá trị
- [x] 1.2: Thiết kế thuật toán auto-sorting theo begin_value
- [x] 1.3: Xác định các trường hợp edge case cần xử lý

### 2. Cải thiện model hr.job.sale.commission
- [x] 2.1: Thêm method validate_continuity() để kiểm tra tính liên tục
- [x] 2.2: Thêm method auto_sort_lines() để tự động sắp xếp
- [x] 2.3: Cập nhật constraint _check_hr_job_sale_commission_line_ids()
- [x] 2.4: Thêm method write() override để trigger auto-sort

### 3. Cải thiện model hr.job.pt.commission
- [x] 3.1: Thêm method validate_continuity() để kiểm tra tính liên tục
- [x] 3.2: Thêm method auto_sort_lines() để tự động sắp xếp
- [x] 3.3: Cập nhật constraint _check_hr_job_pt_commission_line_ids()
- [x] 3.4: Thêm method write() override để trigger auto-sort

### 4. Cải thiện model hr.job.k.coefficient
- [x] 4.1: Thêm method validate_continuity() để kiểm tra tính liên tục
- [x] 4.2: Thêm method auto_sort_lines() để tự động sắp xếp
- [x] 4.3: Cập nhật constraint _check_k_coefficient_line_ids()
- [x] 4.4: Thêm method write() override để trigger auto-sort

### 5. Testing và validation
- [ ] 5.1: Tạo test case cho validation tính liên tục
- [ ] 5.2: Tạo test case cho auto-sorting functionality
- [ ] 5.3: Test integration với existing functionality
- [ ] 5.4: Test edge cases và error handling

## Manual Testcase

### Testcase 1: Validation tính liên tục - Cơ chế hoa hồng bán
- [ ] 1.1: Tạo chức vụ mới và thêm tab "Cơ chế hoa hồng bán"
- [ ] 1.2: Thêm dòng 1: Từ 0 >= đến 50 <
- [ ] 1.3: Thêm dòng 2: Từ 100 >= đến 150 < (tạo khoảng trống 50-100)
- [ ] 1.4: Lưu và kiểm tra hệ thống báo lỗi về tính không liên tục
- [ ] 1.5: Sửa dòng 2 thành: Từ 50 >= đến 100 <
- [ ] 1.6: Lưu và kiểm tra hệ thống chấp nhận (liên tục)

### Testcase 2: Auto-sorting - Cơ chế hoa hồng dạy  
- [ ] 2.1: Tạo chức vụ mới và thêm tab "Cơ chế hoa hồng dạy"
- [ ] 2.2: Thêm dòng 1: Từ 100 >= đến 150 <
- [ ] 2.3: Thêm dòng 2: Từ 0 >= đến 50 <
- [ ] 2.4: Thêm dòng 3: Từ 50 >= đến 100 <
- [ ] 2.5: Lưu và kiểm tra hệ thống tự động sắp xếp theo thứ tự: 0-50, 50-100, 100-150

### Testcase 3: Validation tính liên tục - Hệ số lương K
- [ ] 3.1: Tạo chức vụ mới và thêm tab "Hệ số lương K"
- [ ] 3.2: Thêm dòng 1: Từ 0 >= đến 30 <=
- [ ] 3.3: Thêm dòng 2: Từ 50 >= đến 80 <= (tạo khoảng trống 31-49)
- [ ] 3.4: Lưu và kiểm tra hệ thống báo lỗi về tính không liên tục
- [ ] 3.5: Sửa dòng 2 thành: Từ 31 >= đến 80 <=
- [ ] 3.6: Lưu và kiểm tra hệ thống chấp nhận (liên tục)

### Testcase 4: Edge cases
- [ ] 4.1: Test với khoảng giá trị âm
- [ ] 4.2: Test với khoảng giá trị 0
- [ ] 4.3: Test với chỉ 1 dòng cấu hình
- [ ] 4.4: Test với nhiều dòng cấu hình phức tạp

## Implementation Plan

### Phase 1: Core Logic Development
1. Phát triển thuật toán validation tính liên tục
2. Phát triển thuật toán auto-sorting
3. Tạo helper methods chung có thể tái sử dụng

### Phase 2: Model Updates
1. Cập nhật từng model một cách tuần tự
2. Đảm bảo backward compatibility
3. Test từng model riêng biệt

### Phase 3: Integration & Testing
1. Test tích hợp giữa các models
2. Test performance với large datasets
3. User acceptance testing

## Dependencies
- Model hr.job (base model)
- Existing validation logic trong các constraint methods
- UI views cho các tab cấu hình

## Notes
- Cần đảm bảo không ảnh hưởng đến logic tính toán hoa hồng hiện tại
- Auto-sorting chỉ thực hiện khi validation pass
- Cần xử lý trường hợp user xóa dòng giữa (có thể tạo khoảng trống)
- Cần cân nhắc performance khi có nhiều dòng cấu hình
