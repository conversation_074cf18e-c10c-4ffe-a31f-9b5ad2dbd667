from odoo import models, fields, api


class HrEmployeeBookingPt(models.Model):
    _inherit = 'hr.employee.booking.pt'

    sales_target_ratio = fields.Float(string='Tỷ lệ đạt doanh số', compute='_compute_sales_target_ratio')

    @api.depends('employee_id', 'month', 'year')
    def _compute_sales_target_ratio(self):
        for record in self:
            target = self.env['hr.employee.sales.target'].search([
                ('employee_id', '=', record.employee_id.id),
                ('month', '=', record.month),
                ('year', '=', record.year)
            ], limit=1)
            record.sales_target_ratio = target.target_reached / 100 if target else 0

    pt_salary_ratio = fields.Float(string='Tỷ lệ lương dạy', compute='_compute_pt_salary_ratio')

    @api.depends('sales_target_ratio', 'num_booking_pt', 'employee_id', 'employee_id.job_id',
                 'employee_id.job_id.pt_commission_type', 'employee_id.job_id.pt_commission_data_type', 'employee_id.job_id.hr_job_pt_commission_line_ids')
    def _compute_pt_salary_ratio(self):
        for record in self:

            record.pt_salary_ratio = 0
            job = record.employee_id.job_id

            if not record.employee_id or not job:
                continue

            pt_commissions = job.hr_job_pt_commission_line_ids
            if pt_commissions:
                if job.pt_commission_type == 'kpi':
                    if job.pt_commission_data_type == 'percent':
                        for line in pt_commissions:
                            # Kiểm tra giá trị nằm trong khoảng hoa hồng của cấu hình nếu đúng thì gán hoa hồng cho record
                            if line.check_commission_range(record.sales_target_ratio * 100):
                                record.pt_salary_ratio = line.pt_commission
                                break
                    elif job.pt_commission_data_type == 'revenue':
                        # Lấy ra doanh số thực tế của nhân viên
                        target = self.env['hr.employee.sales.target'].search([
                            ('employee_id', '=', record.employee_id.id),
                            ('month', '=', record.month),
                            ('year', '=', record.year)
                        ], limit=1)
                        if target:
                            for line in pt_commissions:
                                # Kiểm tra giá trị nằm trong khoảng hoa hồng của cấu hình nếu đúng thì gán hoa hồng cho record
                                if line.check_commission_range(target.revenue):
                                    record.pt_salary_ratio = line.pt_commission
                                    break
                elif job.pt_commission_type == 'booking':
                    for line in pt_commissions:
                        # Kiểm tra giá trị tổng số buổi dạy nằm trong khoảng của chỉ tiêu nào thì gán tỉ lệ lương dạy theo commission dạy của chỉ tiêu đó
                        if line.check_commission_range(record.num_booking_pt):
                            record.pt_salary_ratio = line.pt_commission
                            break

    # Thành tiền
    total_amount = fields.Monetary(string='Thành tiền', compute='_compute_total_amount', store=True)

    @api.depends('pt_salary_ratio', 'revenue', 'gift_session_commission_salary')
    def _compute_total_amount(self):
        for record in self:
            record.total_amount = record.pt_salary_ratio * record.revenue + record.gift_session_commission_salary
