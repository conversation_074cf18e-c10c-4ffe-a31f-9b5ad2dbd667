from odoo import models, fields, api, exceptions


class HrEmployeeSalesTarget(models.Model):
    _inherit = "hr.employee.sales.target"
    
    # Commission bán
    sale_commission = fields.Float(string="Commission Bán", compute='_compute_sale_commission')

    # Trường company_id liên kết từ department_sales_target_id để dễ dàng truy cập thông tin công ty
    company_id = fields.Many2one('res.company', string='Công ty', related='department_sales_target_id.company_id', store=True, readonly=True)

    @api.depends('job_id', 'job_id.sale_commission_type', 'job_id.sale_commission_data_type', 'job_id.hr_job_sale_commission_line_ids',
                 'revenue', 'target_reached')
    def _compute_sale_commission(self):
        for record in self:
            # Reset hoa hồng bán về 0 để tính lại
            record.sale_commission = 0

            # Nếu không có job_id thì bỏ qua
            if not record.employee_id.job_id:
                continue

            job = record.employee_id.job_id
            target_reached = record.target_reached
            revenue = record.revenue
            if record.calculate_salary_by_club_revenue:
                target_reached = record.club_target_reached
                revenue = record.club_revenue

            sale_commissions = job.hr_job_sale_commission_line_ids

            # Kiểm tra nếu loại chỉ tiêu là doanh số và có tồn tại chỉ tiêu của cơ chế hoa hồng bán
            if job.sale_commission_type == 'kpi' and sale_commissions:

                if job.sale_commission_data_type == 'percent':
                    for line in sale_commissions:
                        if line.check_commission_range(target_reached):
                            record.sale_commission = line.sale_commission
                            break

                elif job.sale_commission_data_type == 'revenue':
                    for line in sale_commissions:
                        if line.check_commission_range(revenue):
                            record.sale_commission = line.sale_commission
                            break

    # Doanh số bị giữ lại vì hoá đơn chưa được thanh toán hết
    target_hold = fields.Monetary("Doanh số bị keep", currency_field="currency_id", tracking=True, compute='_compute_target_hold', store=True)

    @api.depends('revenue', 'sales_line_ids', 'sales_line_ids.invoice_state')
    def _compute_target_hold(self):
        for record in self:
            # Tính toán doanh số giữ lại
            record.target_hold = 0
            for line in record.sales_line_ids:
                if line.invoice_state != 'paid':
                    record.target_hold += line.revenue
                    
    # Thêm default value cho target của nhân viên theo job_id
    @api.constrains('state', 'employee_id')
    def _compute_target_value_by_job_id(self):
        for rec in self:
            if rec.state == 'draft' and rec.target <= 0:
                rec.target = rec.employee_id.job_id.sale_target

    calculate_salary_by_club_revenue = fields.Boolean(string='Tính lương theo doanh thu CLB', related='job_id.calculate_salary_by_club_revenue', store=True)

    club_revenue = fields.Monetary("Doanh số thực tế CLB", currency_field="currency_id", tracking=True, compute='_compute_club_target', store=True)
    club_actual_target = fields.Monetary("Target CLB", currency_field="currency_id", tracking=True, compute='_compute_club_target', store=True)
    club_target_reached = fields.Float(string='Tiến độ CLB', compute='_compute_target_reached', store=True)

    # Lấy tổng doanh số của các phòng ban của tháng, năm thuộc CLB
    # Phụ thuộc vào năm, tháng và các thay đổi trong doanh số/mục tiêu của phòng ban trong cùng công ty
    @api.depends('year', 'month', 'company_id.department_sales_target_ids.actual_target', 'company_id.department_sales_target_ids.revenue')
    def _compute_club_target(self):
        for record in self:
            record.club_revenue = 0
            record.club_actual_target = 0
            if not record.job_id.calculate_salary_by_club_revenue:
                continue
            # Tìm kiếm các mục tiêu doanh số phòng ban theo năm, tháng và công ty hiện tại
            department_sales_target_ids = self.env['hr.department.sales.target'].search([
                ('year', '=', record.year),
                ('month', '=', record.month),
                ('company_id', '=', record.company_id.id),
                ('state', '=', 'posted')
            ])
            if department_sales_target_ids:
                record.club_actual_target = sum(department_sales_target_ids.mapped('actual_target'))
                record.club_revenue = sum(department_sales_target_ids.mapped('revenue'))

    @api.depends('club_revenue', 'club_actual_target')
    def _compute_target_reached(self):
        for record in self:
            record.club_target_reached = (record.club_revenue / record.club_actual_target) * 100 if record.club_actual_target else 0
