from odoo import api,models,fields, _
from odoo.exceptions import ValidationError


class HrJob(models.Model):
    _inherit = "hr.job"
    
    currency_id = fields.Many2one('res.currency', related="company_id.currency_id")
    department_id = fields.Many2one(tracking=True)
    contract_type_id = fields.Many2one(tracking=True)
    
    sale_target = fields.Monetary("Mục tiêu <PERSON>h <PERSON>ố", currency_field="currency_id", tracking=True)
    
    # Cấu hình hoa hồng dạy
    pt_commission_type = fields.Selection(
        string="Loại chỉ tiêu hoa hồng dạy",
        selection=[("kpi", "Doanh số"), ("booking", "Số buổi dạy")],
        default="kpi",
        required=True,
        tracking=True,
        copy=True
    )

    pt_commission_data_type = fields.Selection(
        string="Kiểu dữ liệu hoa hồng dạy",
        selection=[("percent", "Tỷ Lệ %"), ("revenue", "Số Tiền")],
        tracking=True,
        copy=True
    )

    hr_job_pt_commission_line_ids = fields.One2many(
        string="Cơ chế hoa hồng dạy",
        comodel_name='hr.job.pt.commission',
        inverse_name='hr_job_id')

    # Cấu hình hoa hồng bán
    sale_commission_type = fields.Selection(
        string="Loại chỉ tiêu hoa hồng bán",
        selection=[("kpi", "Doanh số")],
        default="kpi",
        required=True,
        tracking=True,
        copy=True
    )

    sale_commission_data_type = fields.Selection(
        string="Kiểu dữ liệu hoa hồng bán",
        selection=[("percent", "Tỷ Lệ %"), ("revenue", "Số Tiền")],
        required=True,
        tracking=True,
        copy=True
    )

    hr_job_sale_commission_line_ids = fields.One2many(
        string="Cơ chế hoa hồng bán",
        comodel_name='hr.job.sale.commission',
        inverse_name='hr_job_id')

    # Cấu hình hệ số lương K
    k_coefficient_type = fields.Selection(
        string="Loại chỉ tiêu hệ số lương",
        selection=[("kpi", "Doanh số"), ("booking", "Số giờ dạy"), ("constant", "Hằng số")],
        default="kpi",
        required=True,
        tracking=True,
        copy=True
    )

    # Check các dòng cơ chế hoa hồng không được trùng khoảng giá trị với nhau
    @api.constrains('hr_job_sale_commission_line_ids')
    def _check_hr_job_sale_commission_line_ids(self):
        for job in self:
            # Tạo data test
            vals = job.hr_job_sale_commission_line_ids.mapped('begin_value') + job.hr_job_sale_commission_line_ids.mapped('end_value')
            test_data = []
            for val in vals:
                test_data.append(val + 1)
                test_data.append(val - 1)
                test_data.append(val)
            vals = list(set(test_data))
            
            # Kiểm tra nếu có giá trị trùng nhau
            for val in vals:
                count = job.hr_job_sale_commission_line_ids.filtered(lambda x: x.check_commission_range(val))
                if len(count) > 1:
                    raise ValidationError(_("Khoảng giá trị của các dòng cơ chế hoa hồng bán không được trùng nhau"))

    # Check các dòng cơ chế hoa hồng không được trùng khoảng giá trị với nhau
    @api.constrains('hr_job_pt_commission_line_ids')
    def _check_hr_job_pt_commission_line_ids(self):
        for job in self:
            # Tạo data test
            vals = job.hr_job_pt_commission_line_ids.mapped('begin_value') + job.hr_job_pt_commission_line_ids.mapped(
                'end_value')
            test_data = []
            for val in vals:
                test_data.append(val + 1)
                test_data.append(val - 1)
                test_data.append(val)
            vals = list(set(test_data))

            # Kiểm tra nếu có giá trị trùng nhau
            for val in vals:
                count = job.hr_job_pt_commission_line_ids.filtered(lambda x: x.check_commission_range(val))
                if len(count) > 1:
                    raise ValidationError(_("Khoảng giá trị của các dòng cơ chế hoa hồng dạy không được trùng nhau"))
