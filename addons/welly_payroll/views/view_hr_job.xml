<odoo>
    <!-- inherit lại view id: hr.view_hr_job_form -->
    <record id="view_hr_job_form" model="ir.ui.view">
        <field name="name">hr.job.form</field>
        <field name="model">hr.job</field>
        <field name="inherit_id" ref="hr.view_hr_job_form"/>
        <field name="arch" type="xml">
            <xpath expr="//label[@for='no_of_recruitment']" position="before">
                <field name="currency_id" invisible="1"/>
                <field name="sale_target" widget="monetary"/>
            </xpath>
            <xpath expr="//div[@name='recruitment_target']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//label[@for='no_of_recruitment']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//page[@name='recruitment_page']" position="after">
                <page string="Cơ chế hoa hồng dạy" name="pt_commission_config">
                    <group>
                        <group name="pt_commission_config">
                            <field name="pt_commission_type" string="Loại chỉ tiêu HH dạy"/>
                        </group>
                        <group name="pt_commission_config2">
                            <!-- Chỉ hiển thị và required khi Loại Chỉ Tiêu là Doanh số -->
                            <field name="pt_commission_data_type"
                                   string="Kiểu dữ liệu HH dạy"
                                   attrs="{'invisible':[('pt_commission_type', '!=', 'kpi')],
                             'required':[('pt_commission_type', '=', 'kpi')]}"/>
                        </group>
                    </group>
                    <field name="hr_job_pt_commission_line_ids">
                        <tree editable="bottom">
                            <field name="name"/>
                            <field name="begin_op"/>
                            <field name="begin_value"/>
                            <field name="end_op"/>
                            <field name="end_value"/>
                            <field name="pt_commission" widget="percentage"/>
                        </tree>
                    </field>
                </page>
                <page string="Cơ chế hoa hồng bán" name="sale_commission_config">
                    <group>
                        <group name="sale_commission_config">
                            <field name="sale_commission_type" string="Loại chỉ tiêu HH bán"/>
                        </group>
                        <group name="sale_commission_config2">
                            <field name="sale_commission_data_type" string="Kiểu dữ liệu HH bán"/>
                        </group>
                    </group>
                    <field name="hr_job_sale_commission_line_ids">
                        <tree editable="bottom">
                            <field name="name"/>
                            <field name="begin_op"/>
                            <field name="begin_value"/>
                            <field name="end_op"/>
                            <field name="end_value"/>
                            <field name="sale_commission" widget="percentage"/>
                        </tree>
                    </field>
                </page>
            </xpath>
            <xpath expr="//page[@name='job_description_page']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='contract_type_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='department_id']" position="attributes">
                <attribute name="string">Phòng ban</attribute>
            </xpath>
        </field>
    </record>
    <!-- Thêm trường sale_target vào tree view -->
    <record id="view_hr_job_tree" model="ir.ui.view">
        <field name="name">hr.job.tree</field>
        <field name="model">hr.job</field>
        <field name="inherit_id" ref="hr.view_hr_job_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='department_id']" position="attributes">
                <attribute name="string">Phòng ban</attribute>
            </xpath>
            <xpath expr="//field[@name='department_id']" position="after">
                <field name="currency_id" invisible="1"/>
                <field name="sale_target" widget="monetary" sum="Tổng mục tiêu doanh số"/>
                <field name="company_id" string="Công ty"/>
            </xpath>
            <xpath expr="//field[@name='no_of_recruitment']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>
</odoo>
