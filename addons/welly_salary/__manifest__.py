# -*- coding: utf-8 -*-
{
    'name': "Welly Salary",

    'summary': """
        <PERSON><PERSON><PERSON> c<PERSON>u hình, t<PERSON>h lương cho nhân viên
    """,

    'description': """
        <PERSON><PERSON><PERSON> c<PERSON>u hình, t<PERSON>h lương cho nhân viên
    """,

    'author': "WellyTech",
    'website': "https://wellytech.vn",
    'category': 'Welly',
    'version': '0.1',

    'depends': ['base', 'welly_kpi', 'welly_payroll', 'welly_book_pt', 'welly_commission', 'welly_hr_attendance'],

    'data': [
        'security/ir.model.access.csv',
        'data/data_hr_apply_for_option.xml',
        'data/hr_salary_rule_param.xml',
        'data/cron.xml',
        'data/ir_sequence_data.xml',
        'data/email_template.xml',
        'views/hr_job_k_coefficient_views.xml',
        'views/hr_employee_sales_target_views.xml',
        'views/hr_employee_booking_pt_views.xml',
        'views/hr_payroll_views.xml',
        'views/menu_items.xml',
        'views/view_hr_job.xml',
        'views/hr_salary_component_views.xml',
        'views/hr_level_views.xml',
        'views/welly_hr_contract_type_views.xml',
        'views/meta_data_views.xml',
        'views/hr_salary_config_views.xml',
        'views/hr_salary_history_views.xml',
        'views/hr_employee_views.xml',
        'views/hr_payroll_payslip_views.xml',
        'views/hr_salary_period_views.xml',
        'views/hr_salary_rule_param_views.xml',
        'views/hr_payslip_structure_views.xml',
        'wizard/hr_salary_adjust_wizard_views.xml',
        'wizard/hr_payroll_payslip_wizard_views.xml',
        'wizard/action_refuse_payslip.xml',
        'wizard/payroll_reset_draft_wizard.xml',
        'wizard/hr_payroll_payslip_status_wizard.xml',
    ],
    'assets': {
        "web.assets_backend": [
            "welly_salary/static/src/js/*.js",
            "welly_salary/static/src/fields/*.js",
            "welly_salary/static/src/fields/*.xml",
            "welly_salary/static/src/fields/*.scss",
            "welly_salary/static/css/css.css",
        ]
    },
    'license': 'AGPL-3',
    'installable': True,
    'auto_install': False,
    'application': True,
    'post_init_hook': '_post_init_hook',
}
