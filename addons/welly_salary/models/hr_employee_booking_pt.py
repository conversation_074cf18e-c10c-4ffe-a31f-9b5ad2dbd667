from odoo import models, fields, api


class HrEmployeeBookingPt(models.Model):
    _inherit = 'hr.employee.booking.pt'

    # @api.model
    # def search(self, domain, offset=0, limit=None, order=None, count=False):
    #     if self.env.context.get('is_action_menu_booking_pt'):
    #         user = self.env.user
    #         if not (user.has_group('welly_base.group_welly_account') or user.has_group('welly_base.group_coo')):
    #             domain += [('employee_id.user_id', '=', user.id)]
    #     return super(HrEmployeeBookingPt, self).search(domain, offset, limit, order, count)
    #
    # @api.model
    # def read_group(self, domain, fields, groupby, offset=0, limit=None, orderby=False, lazy=True):
    #     if self.env.context.get('is_action_menu_booking_pt'):
    #         user = self.env.user
    #         if not (user.has_group('welly_base.group_welly_account') or user.has_group('welly_base.group_coo')):
    #             domain += [('employee_id.user_id', '=', user.id)]
    #     return super(HrEmployeeBookingPt, self).read_group(domain, fields, groupby, offset, limit, orderby, lazy)
