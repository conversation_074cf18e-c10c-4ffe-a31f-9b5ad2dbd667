from odoo import models, fields, api


class HrEmployeeSalesTarget(models.Model):
    _inherit = "hr.employee.sales.target"
    
    salary_config_id = fields.Many2one(comodel_name='hr.salary.config', string='<PERSON><PERSON><PERSON> hình lươ<PERSON>', related='job_id.salary_config_id', store=True)

    target_salary = fields.Monetary("Lương theo doanh số", currency_field="currency_id", tracking=True, compute='_compute_target_salary', store=True)

    @api.depends('sale_commission', 'revenue', 'salary_config_id', 'salary_config_id.value', 'salary_config_id.data_type', 'salary_config_id.apply_for_ids', 'calculate_salary_by_club_revenue', 'club_revenue')
    def _compute_target_salary(self):
        for record in self:
            salary_config = record.salary_config_id
            revenue = record.club_revenue if record.calculate_salary_by_club_revenue else record.revenue
            if salary_config and salary_config.apply_for_ids.filtered(lambda rec: rec.name == '<PERSON><PERSON><PERSON><PERSON> do<PERSON>h số'):
                if salary_config.data_type == 'percent':
                    record.target_salary = revenue * record.sale_commission - revenue * record.sale_commission * salary_config.value / 100
                elif salary_config.data_type == 'revenue':
                    record.target_salary = revenue * record.sale_commission - salary_config.value
            else:
                record.target_salary = revenue * record.sale_commission

    # @api.model
    # def search(self, domain, offset=0, limit=None, order=None, count=False):
    #     if self.env.context.get('is_action_menu_sales_target'):
    #         user = self.env.user
    #         if not (user.has_group('welly_base.group_welly_account') or user.has_group('welly_base.group_coo')):
    #             domain += [('employee_id.user_id', '=', user.id)]
    #     return super(HrEmployeeSalesTarget, self).search(domain, offset, limit, order, count)
    #
    # @api.model
    # def read_group(self, domain, fields, groupby, offset=0, limit=None, orderby=False, lazy=True):
    #     if self.env.context.get('is_action_menu_sales_target'):
    #         user = self.env.user
    #         if not (user.has_group('welly_base.group_welly_account') or user.has_group('welly_base.group_coo')):
    #             domain += [('employee_id.user_id', '=', user.id)]
    #     return super(HrEmployeeSalesTarget, self).read_group(domain, fields, groupby, offset, limit, orderby, lazy)
