import logging

from dateutil.relativedelta import relativedelta

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

ALLOWED_TYPES = [
    'boolean', 'integer', 'float', 'char', 'date',
    'datetime', 'many2one', 'many2many', 'selection', 'tags',
]
_logger = logging.getLogger(__name__)

class HrPayroll(models.Model):
    _name = 'hr.payroll'
    _description = 'Bảng lương'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id desc'

    # Kỳ lương
    salary_period_id = fields.Many2one(
        'hr.salary.period',
        string='<PERSON><PERSON> lương',
        required=True
    )

    name = fields.Char(string='Tên', related='salary_period_id.name', store=True)

    # Tháng
    month = fields.Selection(
        related='salary_period_id.month',
        string='Tháng',
        readonly=True,
        store=True
    )
    year = fields.Selection(
        related='salary_period_id.year',
        string='Năm',
        readonly=True,
        store=True
    )
    # chu kỳ
    date_from = fields.Date(
        related='salary_period_id.date_from',
        string='Từ ngày',
        readonly=True,
        store=False
    )
    date_to = fields.Date(
        related='salary_period_id.date_to',
        string='Đến ngày',
        readonly=True,
        store=False
    )
    company_id = fields.Many2one(
        related='salary_period_id.company_id',
        string='Công ty',
        readonly=True,
        store=True
    )

    #  Trạng thái bảng lương
    state = fields.Selection([
        ('draft', 'Dự thảo'),
        ('reviewed', 'Review bảng lương'),
        ('approved', 'Đã phê duyệt'),
        ('sent', 'Đã gửi phiếu lương'),
    ], string='Trạng thái', default='draft', tracking=True)

    # Các line chi tiết lương từng nv
    payroll_line_ids = fields.One2many(
        'hr.payroll.line',
        'payroll_id',
        string='Chi tiết lương nhân viên'
    )

    hr_payroll_payslip_ids = fields.One2many(
        'hr.payroll.payslip',
        'payroll_id',
        string='Chi tiết phiếu lương nhân viên'
    )

    payslip_sent_count = fields.Integer(
        string='Đã phát hành phiếu lương',
        compute='_compute_payslip_sent_counts',
        store=True
    )
    payslip_unsent_count = fields.Integer(
        string='Chưa phát hành phiếu lương',
        compute='_compute_payslip_sent_counts',
        store=True
    )

    @api.depends('hr_payroll_payslip_ids')
    def _compute_payslip_sent_counts(self):
        """Tính toán số lượng phiếu lương đã phát hành và đã gửi cho nhân viên"""
        for rec in self:
            all_employees = rec.payroll_line_ids.mapped('employee_id')
            payslips = self.hr_payroll_payslip_ids

            payslip_create_count = payslips.mapped('employee_id')
            rec.payslip_sent_count = len(payslip_create_count)
            rec.payslip_unsent_count = len(all_employees - payslip_create_count)

    payslip_email_sent_count = fields.Integer(
        compute='_compute_payslip_sent_email_counts',
        string='Đã gửi email phiếu lương',
        store=True
    )
    payslip_email_unsent_count = fields.Integer(
        compute='_compute_payslip_sent_email_counts',
        string='Chưa gửi email phiếu lương',
        store=True
    )

    @api.depends('hr_payroll_payslip_ids.email_sent')
    def _compute_payslip_sent_email_counts(self):
        for rec in self:
            all_employees = rec.payroll_line_ids.mapped('employee_id')
            payslips = self.hr_payroll_payslip_ids

            sent_email_employees = payslips.filtered(lambda p: p.email_sent).mapped('employee_id')
            rec.payslip_email_sent_count = len(sent_email_employees)
            rec.payslip_email_unsent_count = len(all_employees - sent_email_employees)
            if rec.payslip_email_unsent_count == 0:
                rec.state = 'sent'

    payslip_approved_count = fields.Integer(
        compute='_compute_payslip_approval_counts',
        string='Đã xác nhận phiếu lương',
        store=True
    )

    payslip_unapproved_count = fields.Integer(
        compute='_compute_payslip_approval_counts',
        string='Chưa xác nhận phiếu lương',
        store=True
    )

    @api.depends('hr_payroll_payslip_ids.state')
    def _compute_payslip_approval_counts(self):
        for rec in self:
            payslips = rec.hr_payroll_payslip_ids

            approved_count = len(payslips.filtered(lambda p: p.state == 'approved'))
            total_issued = len(payslips)

            rec.payslip_approved_count = approved_count
            rec.payslip_unapproved_count = total_issued - approved_count

    salary_properties_definition = fields.PropertiesDefinition(
        string='Định nghĩa tham số tính lương',
        compute='_compute_salary_properties_definition',
        store=True
    )

    @api.depends('salary_period_id')
    def _compute_salary_properties_definition(self):
        Param = self.env['hr.salary.rule.param']
        for rec in self:
            # Lấy toàn bộ các tham số có display_order > 0 (sử dụng được)
            params = Param.search([('display_order', '>', 0), ('active', '=', True)], order='display_order')

            # Tạo dict định nghĩa cho fields.PropertiesDefinition
            definitions = []
            for param in params:
                # Chuyển đổi output_type về field type tương ứng
                field_type = param.output_type
                if field_type not in ALLOWED_TYPES:
                    continue  # Bỏ qua những gì không hỗ trợ
                view_in_kanban = not param.display_payroll
                definition = {
                    'name': param.code,
                    'type': field_type,
                    'string': param.name,
                    'view_in_kanban': view_in_kanban
                }
                # fix riêng cho tham số selection tax_policy
                if param.code == 'tax_policy':
                    employee_model = self.env['hr.employee']
                    tax_policy_field = employee_model._fields.get('tax_policy')
                    selection = []
                    if tax_policy_field and tax_policy_field.selection:
                        # Trường hợp selection là callable (function), cần gọi để lấy giá trị
                        raw_selection = tax_policy_field.selection
                        if callable(raw_selection):
                            raw_selection = raw_selection(employee_model)
                        # Chuyển thành dạng list [ [key, label], ... ]
                        selection = [[key, label] for key, label in raw_selection]
                    definition = {
                        'name': param.code,
                        'type': field_type,
                        'string': param.name,
                        'view_in_kanban': view_in_kanban,
                        'selection': selection
                    }
                definitions.append(definition)
            # Set definition cho salary_properties_definition
            rec.salary_properties_definition = definitions

    @api.model_create_multi
    def create(self, vals_list):
        records = super().create(vals_list)
        for record in records:
            record.generate_payroll_line()
        return records

    def generate_payroll_line(self):
        """Tạo thông tin bảng lương cho 1 kỳ lương theo từng nhân viên"""
        self.ensure_one()
        # Lọc nhân viên còn hoạt động và đã nhận việc trước hoặc bằng ngày kết thúc kỳ lương
        employees = self.env['hr.employee'].search([
            ('active', '=', True),
            ('start_working_date', '<=', self.date_to),
        ])
        for employee in employees:
            self.env['hr.payroll.line'].create({
                'payroll_id': self.id,
                'employee_id': employee.id,
            })

    def action_recompute_payroll_lines(self):
        for rec in self:
            for line in rec.payroll_line_ids:
                line._compute_salary_values()

    def action_recompute_payroll_lines_formula_only(self):
        for rec in self:
            for line in rec.payroll_line_ids:
                line._compute_salary_values(just_formula=True)

    # 7. Button chuyển trạng thái
    def action_draft(self):
        for rec in self:
            if rec.state == 'reviewed':
                rec.state = 'draft'
                continue

            if rec.state == 'approved':
                if rec.payslip_sent_count > 0:
                    return {
                        'type': 'ir.actions.act_window',
                        'name': _('Xác nhận đặt lại dự thảo'),
                        'res_model': 'payroll.reset.draft.wizard',
                        'view_mode': 'form',
                        'target': 'new',
                        'context': {
                            'default_payroll_id': rec.id
                        }
                    }
                else:
                    rec.state = 'draft'
                    continue

    def action_review(self):
        for rec in self:
            if rec.state != 'draft':
                raise ValidationError(_('Chỉ có thể gửi review khi ở trạng thái Dự thảo.'))
            rec.state = 'reviewed'

    def action_approve(self):
        for rec in self:
            if rec.state != 'reviewed':
                raise ValidationError(_('Chỉ có thể phê duyệt khi ở trạng thái Review bảng lương.'))
            rec.state = 'approved'

    def action_create_payslip(self):
        for rec in self:
            if rec.state != 'approved':
                raise ValidationError(_('Chỉ có thể tạo phiếu lương khi đã phê duyệt.'))
            return {
                'name': _('Phát hành phiếu lương'),
                'type': 'ir.actions.act_window',
                'res_model': 'hr.payroll.payslip.wizard',
                'view_mode': 'form',
                'target': 'new',
                'context': {
                    'default_payroll_id': self.id,
                }
            }

    def action_send_email_bulk(self):
        self.ensure_one()

        check_email_send_slip = self.env['hr.salary.general.config'].search([
            ('company_id', '=', self.company_id.id)
        ], limit=1)
        if not check_email_send_slip or not check_email_send_slip.email_send_payslip:
            raise ValidationError(_('Chưa cấu hình Email gửi phiếu lương.'))

        template = self.env.ref('welly_salary.mail_template_hr_payslip_summary', raise_if_not_found=False)
        if not template:
            raise ValidationError(_('Không tìm thấy template email phiếu lương.'))

        payslips = self.env['hr.payroll.payslip'].search([
            ('payroll_id', '=', self.id),
            ('email_sent', '=', False)
        ])
        sent_count = 0
        created_employees_names = []
        for payslip in payslips:
            if not payslip.employee_id.work_email:
                continue
            payslip.state = 'waiting'
            template.with_context(allow_mail_send=True).send_mail(payslip.id, force_send=True)
            payslip.email_sent = True
            sent_count += 1
            created_employees_names.append(payslip.employee_id.name)

        if sent_count == 0:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Gửi Email Phiếu Lương'),
                    'message': _('Bạn cần phát hành thêm phiếu lương mới có dữ liệu để gửi phiếu lương.'),
                    'type': 'danger',
                    'sticky': False,
                }
            }

            # Ghi log action vào bảng lương

        if created_employees_names:
            self.env['mail.message'].create({
                'model': 'hr.payroll',
                'res_id': self.id,
                'message_type': 'comment',
                'body': _('Đã gửi phiếu lương cho: %s.') % ', '.join(created_employees_names),
            })

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Gửi Email Phiếu Lương'),
                'message': _('Đã gửi %d phiếu lương.') % (sent_count),
                'type': 'success',
                'sticky': False,
            }
        }

    def action_view_payslip_status(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('Trạng thái Phiếu lương'),
            'res_model': 'hr.payroll.payslip.status.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_payroll_id': self.id},
            'domain': [],
        }

    def unlink(self):
        for rec in self:
            if rec.state not in ('draft', 'reviewed'):
                raise ValidationError(
                    _("Chỉ được phép xóa Bảng lương khi ở trạng thái Dự thảo hoặc Review bảng lương."))
        return super().unlink()


class HrPayrollLine(models.Model):
    _name = 'hr.payroll.line'
    _description = 'Chi tiết bảng lương nhân viên'
    _order = 'employee_id'

    payroll_id = fields.Many2one('hr.payroll', string='Bảng lương', ondelete='cascade', readonly=True)
    payroll_state = fields.Selection(related='payroll_id.state', store=False, readonly=True)
    employee_id = fields.Many2one('hr.employee', string='Nhân viên', required=True, readonly=True)
    employee_code = fields.Char(string='Mã nhân viên', related='employee_id.employee_code', readonly=True)
    employee_department = fields.Many2one('hr.department', string='Phòng ban', related='employee_id.department_id', readonly=True)
    employee_job = fields.Many2one('hr.job', string='Mã nhân viên', related='employee_id.job_id', readonly=True)

    salary_values = fields.Properties(
        string="Tham số tính lương",
        definition_record='payroll_id',
        definition_record_field='salary_properties_definition',
        copy=True,
        store=True,
        readonly=False
    )

    @api.model_create_multi
    def create(self, vals_list):
        records = super().create(vals_list)
        for record in records:
            record.with_context(skip_salary_log=True)._compute_salary_values()
        return records

    def write(self, vals):
        skip_log = self.env.context.get('skip_salary_log', False)
        for rec in self:
            message = ''
            if not skip_log and 'salary_values' in vals and rec.salary_values:
                # Chuyển danh sách dict thành map {code: value}
                before = {prop['name']: prop.get('value') for prop in rec.salary_values}
                after = {prop['name']: prop.get('value') for prop in vals['salary_values']}

                # Map để lấy string hiển thị
                definition_map = {
                    defn['name']: defn['string']
                    for defn in rec.payroll_id.salary_properties_definition or []
                }

                # So sánh giá trị cũ và mới
                changed_lines = []
                for code, old_val in before.items():
                    if code in after:
                        new_val = after[code]
                        if old_val != new_val:
                            label = definition_map.get(code, code)
                            changed_lines.append(f"<br>- {label}: {old_val} → {new_val}")

                if changed_lines:
                    employee_name = rec.employee_id.name
                    message = f"<strong>Nhân viên {employee_name}:</strong>" + ''.join(changed_lines)

                    # Gửi log ngược về bảng lương
                    self.env['mail.message'].create({
                        'model': 'hr.payroll',
                        'res_id': rec.payroll_id.id,
                        'message_type': 'comment',
                        'subtype_id': self.env.ref('mail.mt_note').id,
                        'body': message,
                    })

        return super().write(vals)

    def _compute_salary_values(self, just_formula=None):
        """Tính toán các giá trị tham số lương kiểu công thức và tự động"""
        self.ensure_one()
        employee = self.employee_id
        payroll = self.payroll_id
        period = payroll.salary_period_id

        # Lấy tất cả các tham số để biết thứ tự tính toán
        all_params = self.env['hr.salary.rule.param'].search([
            ('active', '=', True),
            ('display_order', '>', 0)
        ], order='compute_order')

        new_values = []

        # Dictionary để lưu trữ các giá trị đã tính toán (để dùng trong formula)
        computed_values = {}
        # Map: code -> prop_dict từ self.salary_values
        code_to_prop = {prop['name']: dict(prop) for prop in self.salary_values}

        for param in all_params:
            param_code = param.code
            prop_dict = code_to_prop.get(param_code, {
                'name': param_code,
                'type': param.output_type,
                'value': 0
            })

            # Xử lý theo param_type
            if param.param_type == 'auto' and not just_formula:
                prop_dict['value'] = self._compute_auto_param_value(param_code, employee, period)

            elif param.param_type == 'formula' or just_formula:
                # Tính toán theo công thức
                if param.formula:
                    try:
                        # Tạo context cho việc thực thi công thức
                        formula_context = computed_values.copy()

                        # Thêm các biến đặc biệt có thể cần trong công thức
                        formula_context.update({
                            'employee': employee,
                            'period': period,
                            'payroll': payroll,
                        })

                        # Thực thi công thức
                        result = self._execute_formula(param.formula, formula_context)
                        if param.output_type == 'float':
                            result = round(result, 2)
                        prop_dict['value'] = result

                    except Exception as e:
                        # Log lỗi và gán giá trị mặc định
                        _logger.warning(f"Error executing formula for {param_code}: {str(e)}")
                        prop_dict['value'] = 0.0 if param.output_type == 'float' else ''
            elif param.param_type == 'text' and not just_formula:
                if param.description:
                    prop_dict['value'] = param.description
                else:
                    prop_dict['value'] = ""

            # Lưu giá trị đã tính vào computed_values để dùng cho các formula khác
            computed_values[param_code] = prop_dict.get('value', 0)
            new_values.append(prop_dict)

        # Gán lại toàn bộ danh sách đã tính giá trị
        self.salary_values = new_values

    def _compute_auto_param_value(self, param_code, employee, period):
        """Tính toán giá trị cho các param_type = 'auto'"""
        if param_code == 'cap_bac':
            return employee.hr_level_id.name if employee.hr_level_id else False

        elif param_code == 'khoi_nhan_su':
            return employee.hr_business_unit_id.name if employee.hr_business_unit_id else False

        elif param_code == 'loai_viec_lam':
            return employee.hr_contract_type_id.name if employee.hr_contract_type_id else False

        elif param_code == 'old_salary':
            salary_histories = employee.salary_history_ids.sorted(key=lambda x: x.date_from)
            current_period = (period.date_from, period.date_to)

            value = 0
            for idx, hist in enumerate(salary_histories):
                # Nếu kỳ lương nằm trong bản ghi lịch sử hiện tại
                if hist.date_from <= current_period[1] and (
                        not hist.date_to or hist.date_to >= current_period[0]):
                    # Nếu có bản ghi trước đó
                    if idx > 0:
                        value = salary_histories[idx - 1].contract_salary
                    else:
                        value = 0
            return value

        elif param_code == 'salary':
            return employee.contract_salary if employee.contract_salary else 0.0

        elif param_code == 'old_basic_salary':
            salary_histories = employee.salary_history_ids.sorted(key=lambda x: x.date_from)
            current_period = (period.date_from, period.date_to)

            value = 0
            for idx, hist in enumerate(salary_histories):
                # Nếu kỳ lương nằm trong bản ghi lịch sử hiện tại
                if hist.date_from <= current_period[1] and (
                        not hist.date_to or hist.date_to >= current_period[0]):
                    # Nếu có bản ghi trước đó
                    if idx > 0:
                        value = salary_histories[idx - 1].base_salary
                    else:
                        value = 0
            return value

        elif param_code == 'basic_salary':
            return employee.base_salary if employee.base_salary else 0.0

        elif param_code == 'old_salary_day':
            salary_histories = employee.salary_history_ids.sorted(key=lambda x: x.date_from)
            current_period = (period.date_from, period.date_to)

            range_start, range_end = None, None
            for idx, hist in enumerate(salary_histories):
                if hist.date_from <= current_period[1] and (
                        not hist.date_to or hist.date_to >= current_period[0]):
                    if idx > 0:
                        prev_hist = salary_histories[idx - 1]
                        range_start = max(current_period[0], prev_hist.date_from)
                        range_end = min(current_period[1], prev_hist.date_to)

            if range_start and range_end:
                shifts = self.env['hr.work.shift'].search([
                    ('employee_id', '=', employee.id),
                    ('start_time', '>=', range_start),
                    ('end_time', '<=', range_end),
                ])
                return sum(shift.work_day_actual for shift in shifts)
            else:
                return 0

        elif param_code == 'salary_day':
            # Lấy lịch sử lương gần nhất có hiệu lực với kỳ lương này
            salary_histories = employee.salary_history_ids.filtered(
                lambda s: s.date_from and s.date_from <= period.date_to
            ).sorted(key=lambda x: x.date_from, reverse=True)

            hist_start_date = salary_histories[0].date_from if salary_histories else period.date_from

            # Giới hạn ngày công: nằm trong kỳ lương và >= ngày bắt đầu lịch sử lương
            shifts = self.env['hr.work.shift'].search([
                ('employee_id', '=', employee.id),
                ('start_time', '>=', max(period.date_from, hist_start_date)),
                ('end_time', '<=', period.date_to),
            ])

            return sum(shift.work_day_actual for shift in shifts)

        elif param_code == 'coefficient_k':
            job = employee.job_id
            value = 1.0  # mặc định nếu không match

            if job:
                if job.k_coefficient_type == 'constant':
                    value = job.k_coefficient_constant
                elif job.k_coefficient_type == 'kpi':
                    # Ưu tiên logic doanh số nếu có chỉ tiêu
                    target = self.env['hr.employee.sales.target'].search([
                        ('employee_id', '=', employee.id),
                        ('month', '=', period.month),
                        ('year', '=', period.date_from.year),
                    ], limit=1)

                    # Nếu có tiến độ đạt được → dùng theo cấu hình hệ số K loại doanh số
                    if target and target.target_reached:
                        for line in job.k_coefficient_line_ids:
                            if line.check_commission_range(target.target_reached):
                                value = line.salary_coefficient
                                break
                elif job.k_coefficient_type == 'booking':
                    pt = self.env['hr.employee.booking.pt'].search([
                        ('employee_id', '=', employee.id),
                        ('month', '=', period.month),
                        ('year', '=', period.date_from.year),
                    ], limit=1)

                    if pt:
                        #Tỷ lệ hoàn thành ca dạy = Số buổi dạy (model hr.employee.booking.pt) / Chỉ tiêu (số ca/tháng) x 100
                        complete_booking_rate = pt.num_booking_pt / job.k_coefficient_target * 100
                        for line in job.k_coefficient_line_ids:
                            if line.check_commission_range(complete_booking_rate):
                                value = line.salary_coefficient
                                break

            return value
        elif param_code == 'is_social_insurance_applied':
            return bool(employee.is_social_insurance_applied)

        elif param_code == 'tax_policy':
            return employee.tax_policy

        elif param_code == 'dependent_count':
            return employee.dependent_count

        elif param_code == 'target_reached':
            record = self.env['hr.employee.sales.target'].search([
                ('employee_id', '=', employee.id),
                ('month', '=', period.month),
                ('year', '=', period.date_from.year),
            ], limit=1)
            return record.target_reached if record else 0

        elif param_code == 'sale_commission_rate':
            record = self.env['hr.employee.sales.target'].search([
                ('employee_id', '=', employee.id),
                ('month', '=', period.month),
                ('year', '=', period.date_from.year),
            ], limit=1)
            return record.sale_commission * 100 if record else 0

        elif param_code == 'pt_commission_rate':
            record = self.env['hr.employee.booking.pt'].search([
                ('employee_id', '=', employee.id),
                ('month', '=', period.month),
                ('year', '=', period.date_from.year),
            ], limit=1)
            return record.pt_salary_ratio * 100 if record else 0

        elif param_code == 'sale_salary':
            record = self.env['hr.employee.sales.target'].search([
                ('employee_id', '=', employee.id),
                ('month', '=', period.month),
                ('year', '=', period.date_from.year),
            ], limit=1)
            return record.target_salary if record else 0

        elif param_code == 'teaching_salary':
            record = self.env['hr.employee.booking.pt'].search([
                ('employee_id', '=', employee.id),
                ('month', '=', period.month),
                ('year', '=', period.date_from.year),
            ], limit=1)
            return record.total_amount if record else 0

        elif param_code == 'standard_work_days':
            if employee.work_shift_type == 'flexible':
                return employee.work_days or 0.0
            else:
                from_date = period.date_from
                to_date = period.date_to
                total = 0
                while from_date <= to_date:
                    if from_date.weekday() < 5:
                        total += 1
                    from_date += relativedelta(days=1)
                return total

        elif param_code == 'old_effective_salary':
            salary_histories = employee.salary_history_ids.sorted(key=lambda x: x.date_from)
            current_period = (period.date_from, period.date_to)

            value = 0
            for idx, hist in enumerate(salary_histories):
                # Nếu kỳ lương nằm trong bản ghi lịch sử hiện tại
                if hist.date_from <= current_period[1] and (
                        not hist.date_to or hist.date_to >= current_period[0]):
                    # Nếu có bản ghi trước đó
                    if idx > 0:
                        value = salary_histories[idx - 1].effective_salary
                    else:
                        value = 0
            return value

        elif param_code == 'effective_salary':
            return employee.effective_salary if employee.effective_salary else 0.0

        # các trường hợp khác lấy từ thành phần lương
        # Xử lý các tham số lương động: component hiện tại (từ cấp bậc & job) của employee
        else:
            # --- Dynamic param_code từ phụ cấp trong hoặc ngoài lương ---
            value = self._get_property_value(employee.all_allowance_inside, param_code)
            if value is None:
                value = self._get_property_value(employee.all_allowance_outside, param_code)
            if value is not None:
                return value

            # --- Nếu là old_<code>, lấy từ salary_history ---
            elif param_code.startswith('old_'):
                real_code = param_code[4:]
                salary_histories = employee.salary_history_ids.sorted(key=lambda x: x.date_from)
                current_period = (period.date_from, period.date_to)
                for idx, hist in enumerate(salary_histories):
                    # Nếu kỳ lương nằm trong bản ghi lịch sử hiện tại
                    if hist.date_from <= current_period[1] and (
                            not hist.date_to or hist.date_to >= current_period[0]):
                        # Nếu có bản ghi trước đó
                        if idx > 0:
                            value = self._get_property_value(hist.all_allowance_inside, real_code)
                            if value is not None:
                                return value
                            value = self._get_property_value(hist.all_allowance_outside, real_code)
                            if value is not None:
                                return value
                        else:
                            return 0
        return 0

    def _get_property_value(self, properties_list, code):
        """Tìm giá trị của một param_code trong list[dict] kiểu Properties"""
        for prop in properties_list or []:
            if prop.get('name') == code:
                return prop.get('value', 0)
        return None

    def _execute_formula(self, formula, context):
        """Thực thi công thức một cách an toàn"""
        import ast
        import operator

        # Các operator được phép sử dụng
        ALLOWED_OPERATORS = {
            ast.Add: operator.add,
            ast.Sub: operator.sub,
            ast.Mult: operator.mul,
            ast.Div: operator.truediv,
            ast.FloorDiv: operator.floordiv,
            ast.Mod: operator.mod,
            ast.Pow: operator.pow,
            ast.Eq: operator.eq,
            ast.NotEq: operator.ne,
            ast.Lt: operator.lt,
            ast.LtE: operator.le,
            ast.Gt: operator.gt,
            ast.GtE: operator.ge,
            ast.And: operator.and_,
            ast.Or: operator.or_,
            ast.Not: operator.not_,
        }

        class FormulaEvaluator(ast.NodeVisitor):
            def __init__(self, context):
                self.context = context

            def visit_Num(self, node):  # Python < 3.8
                return node.n

            def visit_Constant(self, node):  # Python >= 3.8
                return node.value

            def visit_Str(self, node):
                return node.s

            def visit_Name(self, node):
                if node.id in self.context:
                    return self.context[node.id]
                else:
                    raise NameError(f"Name '{node.id}' is not defined")

            def visit_BinOp(self, node):
                left = self.visit(node.left)
                right = self.visit(node.right)
                op = ALLOWED_OPERATORS.get(type(node.op))
                if op:
                    return op(left, right)
                else:
                    raise TypeError(f"Unsupported operation: {type(node.op)}")

            def visit_Compare(self, node):
                left = self.visit(node.left)
                result = True
                for op, comparator in zip(node.ops, node.comparators):
                    right = self.visit(comparator)
                    op_func = ALLOWED_OPERATORS.get(type(op))
                    if op_func:
                        result = result and op_func(left, right)
                        left = right  # For chained comparisons
                    else:
                        raise TypeError(f"Unsupported comparison: {type(op)}")
                return result

            def visit_BoolOp(self, node):
                op = ALLOWED_OPERATORS.get(type(node.op))
                if not op:
                    raise TypeError(f"Unsupported boolean operation: {type(node.op)}")

                values = [self.visit(value) for value in node.values]
                if isinstance(node.op, ast.And):
                    return all(values)
                elif isinstance(node.op, ast.Or):
                    return any(values)

            def visit_UnaryOp(self, node):
                operand = self.visit(node.operand)
                op = ALLOWED_OPERATORS.get(type(node.op))
                if op:
                    return op(operand)
                else:
                    raise TypeError(f"Unsupported unary operation: {type(node.op)}")

            def visit_IfExp(self, node):
                test = self.visit(node.test)
                if test:
                    return self.visit(node.body)
                else:
                    return self.visit(node.orelse)

            def visit_If(self, node):
                test = self.visit(node.test)
                if test:
                    return self._visit_statements(node.body)
                elif node.orelse:
                    return self._visit_statements(node.orelse)
                return None

            def visit_Return(self, node):
                if node.value:
                    return self.visit(node.value)
                return None

            def _visit_statements(self, statements):
                result = None
                for stmt in statements:
                    result = self.visit(stmt)
                    if isinstance(stmt, ast.Return):
                        return result
                return result

            def visit_Module(self, node):
                return self._visit_statements(node.body)

            def generic_visit(self, node):
                raise TypeError(f"Unsupported node type: {type(node)}")

        # Parse và thực thi công thức
        try:
            tree = ast.parse(formula, mode='exec')
            evaluator = FormulaEvaluator(context)
            result = evaluator.visit(tree)
            return result if result is not None else 0.0
        except Exception as e:
            raise ValueError(f"Formula execution error: {str(e)}")
