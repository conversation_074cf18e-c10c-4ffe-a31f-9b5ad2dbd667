from datetime import datetime, timedelta

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
from markupsafe import escape

class HrPayrollPayslip(models.Model):
    _name = 'hr.payroll.payslip'
    _description = 'Phiếu lương'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'

    name = fields.Char(string='Tên phiếu lươ<PERSON>', default='New', readonly=True, required=True)

    # K<PERSON> lương
    salary_period_id = fields.Many2one('hr.salary.period', string='Kỳ lương', required=True)

    payroll_id = fields.Many2one('hr.payroll', string='Bảng lương', required=True)

    employee_id = fields.Many2one('hr.employee', string='Nhân viên', required=True, readonly=True)

    payroll_line = fields.Many2one('hr.payroll.line', string='Dòng chi tiết lương', required=True, readonly=True)

    hr_payslip_structure_id = fields.Many2one(comodel_name='hr.payslip.structure', string='<PERSON><PERSON><PERSON> trúc phiếu lươ<PERSON>', related='employee_id.hr_payslip_structure_id', store=True)

    salary_properties = fields.Properties(
        string='Chi tiết lương',
        definition_record='hr_payslip_structure_id',
        definition_record_field='salary_properties_definition',
        copy=True
    )

    state = fields.Selection([
        ('draft', 'Dự thảo'),
        ('waiting', 'Chờ xác nhận'),
        ('approved', 'Đã xác nhận')
    ], string='Trạng thái', default='draft', tracking=True)

    waiting_time = fields.Datetime(
        string='Thời điểm gửi chờ xác nhận',
        compute='_compute_waiting_time',
        store=True
    )

    date_deadline = fields.Char(string="Ngày phản hồi phiếu lương", compute='_compute_waiting_time', store=True)

    @api.depends('state')
    def _compute_waiting_time(self):
        for rec in self:
            if rec.state == 'waiting':
                rec.waiting_time = fields.Datetime.now()
                config = self.env['hr.salary.general.config'].search([
                    ('company_id', '=', rec.company_id.id)
                ], limit=1)
                delta = timedelta()
                if config.duration_type == 'days':
                    delta = timedelta(days=config.auto_approve_duration) + timedelta(hours=7)
                elif config.duration_type == 'hours':
                    delta = timedelta(hours=config.auto_approve_duration) + timedelta(hours=7)
                elif config.duration_type == 'minutes':
                    delta = timedelta(minutes=config.auto_approve_duration) + timedelta(hours=7)
                future_date = datetime.now() + delta
                rec.date_deadline = future_date.strftime('%H:%M:%S %d/%m/%Y')
            else:
                rec.date_deadline = False
                rec.waiting_time = False

    email_sent = fields.Boolean(
        string='Đã gửi email',
        default=False,
        tracking=True,
        store=True
    )

    date_from = fields.Date(
        related='salary_period_id.date_from',
        string='Từ ngày',
        readonly=True,
        store=True
    )
    date_to = fields.Date(
        related='salary_period_id.date_to',
        string='Đến ngày',
        readonly=True,
        store=True
    )
    company_id = fields.Many2one(
        related='salary_period_id.company_id',
        string='Công ty',
        readonly=True,
        store=True
    )

    payslip_body = fields.Text(string="Nội dung bảng lương", help="Bảng chi tiết lương được format sẵn",
                               compute='_compute_payslip_body', store=True)


    @api.depends('salary_properties')
    def _compute_payslip_body(self):
        for rec in self:
            row_lines = []
            count = 0
            for index, prop in enumerate(rec.salary_properties or [], start=1):
                label = escape(prop.get('string') or '')
                value = prop.get('value')
                type = prop.get('type')
                if isinstance(value, bool):
                    value_str = '✔' if value else '✘'
                elif value in (None, False, ''):
                    value_str = '-'
                elif type == 'integer':
                    value_str = f"{value:,.0f}".replace(",", "X").replace(".", ",").replace("X", ".")
                elif type == 'float':
                    value_str = f"{value:,.2f}".replace(",", "X").replace(".", ",").replace("X", ".")
                else:
                    value_str = escape(str(value))

                count += 1

                row_html = f"""
                <tr style="border-bottom: 1px solid #e1e1e1;">
                    <td style="padding: 8px 12px; background-color: #f4f4f4; text-align: center; white-space: nowrap;">
                        {index}
                    </td>
                    <td style="padding: 8px 12px; background-color: #f4f4f4; font-weight: bold; white-space: nowrap; text-align: left;">
                        {label}
                    </td>
                    <td style="padding: 8px 12px; background-color: #f4f4f4; white-space: nowrap; text-align: right;">
                        {value_str}
                    </td>
                </tr>
            """
                row_lines.append(row_html.strip())
            # Bọc bảng
            table_html = f"""
                <table width="100%" cellpadding="0" cellspacing="0" border="0" style="border-collapse: collapse; font-family: Verdana, Arial, sans-serif; font-size: 13px; color: #454748;">
                    {''.join(row_lines)}
                </table>
            """
            rec.payslip_body = table_html.strip()

    url_action_form = fields.Char(string="Link phiếu lương", compute='_compute_url_action', store=False)

    @api.depends('name')
    def _compute_url_action(self):
        for rec in self:
            if rec.id:
                base_url = self.env['ir.config_parameter'].sudo().get_param('mywelly_host')
                if not base_url:
                    base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')

                rec.url_action_form = f'{base_url}/web#id={rec.id}&model=hr.payroll.payslip&view_type=form'

    # 7. Button chuyển trạng thái
    def action_waiting(self):
        for rec in self:
            if rec.state != 'draft':
                raise ValidationError(_('Chỉ có thể gửi Chờ xác nhận khi ở trạng thái Dự thảo.'))

            check_email_send_slip = self.env['hr.salary.general.config'].search([
                    ('company_id', '=', self.company_id.id)
            ], limit=1)
            if not check_email_send_slip or not check_email_send_slip.email_send_payslip:
                raise ValidationError(_('Chưa cấu hình Email gửi phiếu lương.'))

            template = self.env.ref('welly_salary.mail_template_hr_payslip_summary', raise_if_not_found=False)
            if not template:
                raise ValidationError(_('Không tìm thấy template email phiếu lương.'))
            rec.state = 'waiting'
            rec.waiting_time = fields.Datetime.now()
            template.with_context(allow_mail_send=True).send_mail(self.id, force_send=True)
            rec.email_sent = True

    def action_approve(self):
        for rec in self:
            if rec.state != 'waiting':
                raise ValidationError(_('Chỉ có thể Xác nhận khi ở trạng thái Chờ xác nhận.'))
            rec.state = 'approved'

    def action_refuse(self):
        if self.state != 'waiting':
            raise ValidationError(_('Chỉ có thể Từ chối khi ở trạng thái Chờ xác nhận.'))
        # direct đến wizard
        return {
            'name': 'Từ chối phiếu lương',
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'refuse.payslip',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'context': {},
        }

    @api.model
    def action_send_payslip_bulk(self):
        check_email_send_slip = self.env['hr.salary.general.config'].search([
            ('company_id', '=', self.company_id.id)
        ], limit=1)
        if not check_email_send_slip or not check_email_send_slip.email_send_payslip:
            raise ValidationError(_('Chưa cấu hình Email gửi phiếu lương.'))
        template = self.env.ref('welly_salary.mail_template_hr_payslip_summary', raise_if_not_found=False)
        if not template:
            raise ValidationError(_('Không tìm thấy template email phiếu lương.'))
        active_ids = self.env.context.get('active_ids', [])
        payslips = self.env['hr.payroll.payslip'].browse(active_ids)
        sent_count = 0
        for payslip in payslips.filtered(lambda p: not p.email_sent and p.employee_id.work_email):
            payslip.state = 'waiting'
            template.with_context(allow_mail_send=True).send_mail(payslip.id, force_send=True)
            payslip.email_sent = True
            sent_count += 1

        skipped = len(payslips) - sent_count

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Gửi Email Phiếu Lương'),
                'message': _('Đã gửi %d phiếu lương. Bỏ qua %d phiếu đã gửi hoặc thiếu email.') % (sent_count, skipped),
                'type': 'success',
                'sticky': False,
            }
        }

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            # thêm sequence cho tên phiếu lương
            if vals.get('name', _("New")) == _("New"):
                vals['name'] = self.env['ir.sequence'].next_by_code('hr.payroll.payslip') or _("New")
        records = super().create(vals_list)
        for record in records:
            record._compute_salary_properties()
        return records

    def unlink(self):
        if self.env.context.get('force_unlink'):
            return super().unlink()

        for rec in self:
            if rec.state != 'draft':
                raise ValidationError(_('Chỉ có thể xóa Phiếu Lương khi ở trạng thái Nháp'))
        return super().unlink()

    def _compute_salary_properties(self):
        """Tính toán các giá trị cho Chi tiết lương"""
        # lấy ra salary_values của payroll_line tương ứng sau đó lọc lại các key có name của salary_properties để lấy value từ salary_values
        self.ensure_one()
        if not self.payroll_line or not self.hr_payslip_structure_id:
            return

        # Lấy các tham số từ cấu trúc phiếu lương
        defined_fields = self.salary_properties
        result = []
        for field in defined_fields:
            name = field['name']
            match = next((item for item in self.payroll_line.salary_values if item['name'] == name), None)
            if match:
                result.append({
                    'name': match['name'],
                    'type': match['type'],
                    'string': match['string'],
                    'value': match.get('value')
                })
            else:
                # Nếu không có giá trị, vẫn gán vào để hiển thị
                result.append({
                    'name': name,
                    'type': field['type'],
                    'string': field['string'],
                    'value': False
                })
        self.salary_properties = result

    @api.model
    def auto_approve_due_payslips(self):
        """(cron job) tự động xác nhận:
        Phiếu lương có trạng thái = Chờ xác nhận
        và Thời gian hiện tại - Thời điểm chuyển sang trạng thái Chờ xác nhận >= Thời gian yêu cầu xác nhận phiếu lương trong mục cấu hình"""
        now = fields.Datetime.now()
        config = self.env['hr.salary.general.config'].search([
            ('company_id', '=', 1)
        ], limit=1)
        if not config:
            return

        delta = timedelta()
        if config.duration_type == 'days':
            delta = timedelta(days=config.auto_approve_duration)
        elif config.duration_type == 'hours':
            delta = timedelta(hours=config.auto_approve_duration)
        elif config.duration_type == 'minutes':
            delta = timedelta(minutes=config.auto_approve_duration)

        deadline = now - delta

        payslips = self.search([
            ('state', '=', 'waiting'),
            ('waiting_time', '<=', deadline)
        ])

        for payslip in payslips:
            # cập nhật log activities
            self.env['mail.message'].create({
                'model': 'hr.payroll.payslip',
                'res_id': payslip.id,
                'message_type': 'comment',
                'body': f'Tự động xác nhận phiếu lương',
            })
            payslip.state = 'approved'
