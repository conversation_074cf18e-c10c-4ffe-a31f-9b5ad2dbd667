from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class HrPayslipStructure(models.Model):
    _name = 'hr.payslip.structure'
    _description = '<PERSON><PERSON><PERSON> trúc phiếu lươ<PERSON>'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(
        string='Tên cấu trúc',
        required=True,
        tracking=True
    )

    description = fields.Char(
        string='Mô tả',
        tracking=True
    )

    salary_param_ids = fields.Many2many(
        comodel_name='hr.salary.rule.param',
        string='Chi tiết lương',
        domain=[('active', '=', True)],
        required=True,
        tracking=True,
        help='Chỉ được chọn các thành phần trong chức năng Tham số tính lương'
    )

    salary_properties_definition = fields.PropertiesDefinition(
        string='Định nghĩa chi tiết lươ<PERSON>',
        compute='_compute_salary_properties_definition',
        store=True
    )

    @api.depends('salary_param_ids')
    def _compute_salary_properties_definition(self):
        for rec in self:
            definitions = []
            # Sắp xếp salary_param_ids theo display_order
            ordered_params = sorted(
                rec.salary_param_ids,
                key=lambda param: param.display_order or 0
            )
            for param in ordered_params:
                field_type = param.output_type
                if not field_type:
                    continue
                definitions.append({
                    'name': param.code,
                    'type': field_type,
                    'string': param.name
                })
            rec.salary_properties_definition = definitions

    employee_ids = fields.One2many(
        'hr.employee',
        'hr_payslip_structure_id',
        string='Nhân viên'
    )

    active = fields.Boolean(
        default=True,
        tracking=True
    )

    def unlink(self):
        raise ValidationError(_('Không thể xóa Cấu trúc phiếu lương. Bạn có thể lưu trữ bản ghi nếu không sử dụng.'))

    def action_archive(self):
        res = super().action_archive()
        return res

    @api.model_create_multi
    def create(self, vals_list):
        records = super().create(vals_list)
        for record in records:
            if not record.salary_param_ids:
                raise ValidationError(_('Chưa cấu hình chi tiết lương'))
        return records

    def write(self, vals):
        res = super(HrPayslipStructure, self).write(vals)
        for record in self:
            if not record.salary_param_ids:
                raise ValidationError(_('Chưa cấu hình chi tiết lương'))
        return res
