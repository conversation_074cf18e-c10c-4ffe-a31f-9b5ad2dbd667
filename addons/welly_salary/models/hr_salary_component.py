from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import re


class HrSalaryComponent(models.Model):
    _name = 'hr.salary.component'
    _description = 'Thành phần lương'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    _sql_constraints = [
        ('unique_code', 'UNIQUE (code)', 'Mã thành phần lương phải là duy nhất')
    ]

    name = fields.Char(string='Tên', required=True)

    code = fields.Char(string='Mã thành phần', required=True,
                       help='Viết liền không dấu, chỉ cho phép nhập dấu gạch dưới', tracking=True)

    is_allowance = fields.Boolean(string='Phụ cấp', default=True, tracking=True)

    is_outside_salary = fields.Boolean(string='Ngoài lương', default=False, tracking=True)

    calculation_type = fields.Selection(
        selection=[('workday', '<PERSON> ngà<PERSON> công'), ('monthly', 'Tròn tháng')],
        string='Quy cách tính',
        required=True,
        tracking=True
    )

    apply_for = fields.Selection(
        selection=[('employee', 'Nhân viên'), ('job', 'Chức vụ'), ('level', 'Cấp bậc')],
        string='Áp dụng theo',
        required=True,
        tracking=True
    )

    hr_employee_ids = fields.Many2many(
        comodel_name='hr.employee',
        relation='hr_salary_component_hr_employee_rel',
        column1='hr_salary_component_id',
        column2='hr_employee_id',
        string='Nhân viên',
        tracking=True
    )

    hr_job_ids = fields.Many2many(
        comodel_name='hr.job',
        relation='hr_salary_component_hr_job_rel',
        column1='hr_salary_component_id',
        column2='hr_job_id',
        string='Chức vụ',
        tracking=True
    )

    hr_level_ids = fields.Many2many(
        comodel_name='hr.level',
        relation='hr_salary_component_hr_level_rel',
        column1='hr_salary_component_id',
        column2='hr_level_id',
        string='Cấp bậc',
        tracking=True
    )

    description = fields.Text(string='Mô tả', tracking=True)

    active = fields.Boolean(default=True, tracking=True)

    @api.constrains('code')
    def _check_code(self):
        for record in self:
            if not re.match(pattern=r'^[A-Za-z0-9_]+$', string=record.code):
                raise ValidationError(_('Mã chỉ được viết liền không dấu và có thể chứa dấu gạch dưới'))

    @api.onchange('apply_for')
    def _onchange_apply_for(self):
        if self.apply_for == 'job':
            self.hr_level_ids = [(5, 0, 0)]
            self.hr_employee_ids = [(5, 0, 0)]
        elif self.apply_for == 'level':
            self.hr_job_ids = [(5, 0, 0)]
            self.hr_employee_ids = [(5, 0, 0)]
        elif self.apply_for == 'employee':
            self.hr_job_ids = [(5, 0, 0)]
            self.hr_level_ids = [(5, 0, 0)]

    @api.model_create_multi
    def create(self, vals_list):
        records = super().create(vals_list)
        records.invalidate_cache(['is_allowance', 'is_outside_salary'])
        Param = self.env['hr.salary.rule.param']
        # Lấy display_order lớn nhất hiện có
        max_display = Param.with_context(active_test=False).search([], order='display_order desc', limit=1).display_order or 0
        for idx, rec in enumerate(records):
            # 1. Xử lý tiêm phụ cấp
            if rec.is_allowance:
                config = self.APPLY_FOR_CONFIG[rec.apply_for]
                target_records = rec[config['field']]
                rec._update_allowance_on_records(target_records, 'add')

            # 2. Xử lý tạo tham số lương (logic gốc)
            if not Param.search([('code', '=', f'old_{rec.code}')], limit=1):
                Param.create({
                    'name': f'{rec.name} cũ',
                    'code': f'old_{rec.code}',
                    'param_type': 'auto',
                    'output_type': 'integer',
                    'display_order': max_display + idx + 1,
                    'compute_order': 1,
                    'active': True,
                })
            if not Param.search([('code', '=', rec.code)], limit=1):
                Param.create({
                    'name': rec.name,
                    'code': rec.code,
                    'param_type': 'auto',
                    'output_type': 'integer',
                    'display_order': max_display + idx + 2,
                    'compute_order': 1,
                    'active': True,
                })
        return records

    def write(self, vals):
        # Lưu lại trạng thái cũ của các record trước khi cập nhật
        # Ví dụ: {'1': {'code': 'phone_allowance', 'is_allowance': True, 'is_outside_salary': False, 'apply_for': 'job', 'related_ids': {1, 2, 3}}}
        old_state_map = {}
        for rec in self:
            was_allowance = rec.is_allowance
            was_apply_for = rec.apply_for
            old_state_map[rec.id] = {
                'code': rec.code,
                'is_allowance': was_allowance,
                'is_outside_salary': rec.is_outside_salary,
                'apply_for': was_apply_for,
                'related_ids': set(rec[self.APPLY_FOR_CONFIG[was_apply_for]['field']].ids) if was_allowance and was_apply_for else set()
            }

        res = super().write(vals)

        # Cập nhật lại danh sách phụ cấp
        for rec in self:
            old_state = old_state_map[rec.id]
            # Lấy trạng thái phụ cấp cũ và mới để so sánh
            old_status = self._get_allowance_status(old_state['is_allowance'], old_state['is_outside_salary'])
            new_status = self._get_allowance_status(rec.is_allowance, rec.is_outside_salary)
            # TRƯỜNG HỢP 1: THAY ĐỔI LOẠI PHỤ CẤP (inside <-> outside <-> none)
            if old_status != new_status:
                # Lấy danh sách record cần xử lý dựa trên trạng thái cũ
                if old_status != 'none' and old_state['apply_for']:
                    old_config = self.APPLY_FOR_CONFIG[old_state['apply_for']]
                    records_to_cleanup = self.env[old_config['model']].browse(list(old_state['related_ids']))
                    # Xóa các phụ cấp liên quan dựa vào trạng thái cũ:
                    # 1. Nếu là phụ cấp trong lương, thì xóa phụ cấp đó khỏi danh sách
                    # 2. Nếu là phụ cấp ngoài lương, thì xóa phụ cấp đó khỏi danh sách
                    if records_to_cleanup:
                        if old_status == 'inside':
                            state_to_remove = {'code': old_state['code'], 'is_outside_salary': False}
                            rec._update_allowance_on_records(records_to_cleanup, 'remove', force_state=state_to_remove)
                        elif old_status == 'outside':
                            state_to_remove = {'code': old_state['code'], 'is_outside_salary': True}
                            rec._update_allowance_on_records(records_to_cleanup, 'remove', force_state=state_to_remove)

                # Tạo phụ cấp mới
                if new_status != 'none' and rec.apply_for:
                    new_config = self.APPLY_FOR_CONFIG[rec.apply_for]
                    new_records = rec[new_config['field']]
                    if new_records:
                        rec._update_allowance_on_records(new_records, 'add')

            # TRƯỜNG HỢP 2: LOẠI PHỤ CẤP KHÔNG ĐỔI, NHƯNG THAY ĐỔI ĐỐI TƯỢNG ÁP DỤNG
            elif old_state['apply_for'] != rec.apply_for:
                if new_status != 'none':
                    # Xóa phụ cấp cho các record cũ
                    if old_state['apply_for']:
                        old_config = self.APPLY_FOR_CONFIG[old_state['apply_for']]
                        old_records = self.env[old_config['model']].browse(list(old_state['related_ids']))
                        if old_records:
                            rec._update_allowance_on_records(old_records, 'remove', force_state=old_state)

                    # Thêm phụ cấp mới cho các record mới
                    if rec.apply_for:
                        new_config = self.APPLY_FOR_CONFIG[rec.apply_for]
                        new_records = rec[new_config['field']]
                        if new_records:
                            rec._update_allowance_on_records(new_records, 'add')

            # TRƯỜNG HỢP 3: KHÔNG THAY ĐỔI LOẠI PHỤ CẤP VÀ ĐỐI TƯỢNG ÁP DỤNG, CHỈ THAY ĐỔI DANH SÁCH
            elif new_status != 'none' and rec.apply_for:
                config = self.APPLY_FOR_CONFIG[rec.apply_for]
                field_name = config['field']
                if field_name in vals:
                    old_ids = old_state['related_ids']
                    new_ids = set(rec[field_name].ids)

                    added_ids = list(new_ids - old_ids)
                    removed_ids = list(old_ids - new_ids)

                    model = self.env[config['model']]

                    # Chỉ thêm/xóa trên các record có thay đổi -> hiệu quả
                    if added_ids:
                        rec._update_allowance_on_records(model.browse(added_ids), 'add')
                    if removed_ids:
                        rec._update_allowance_on_records(model.browse(removed_ids), 'remove')

            # --- ĐỒNG BỘ VỚI hr.salary.rule.param ---
            param = self.env['hr.salary.rule.param'].search([('code', '=', rec.code)], limit=1)
            if param:
                updates = {}
                if 'name' in vals and param.name != rec.name:
                    updates['name'] = rec.name
                if 'active' in vals and param.active != rec.active:
                    updates['active'] = rec.active
                if updates:
                    param.write(updates)
            param_old = self.env['hr.salary.rule.param'].search([('code', '=', f'old_{rec.code}')], limit=1)
            if param_old:
                updates = {}
                if 'name' in vals and param_old.name != f'{rec.name} cũ':
                    updates['name'] = f'{rec.name} cũ'
                if 'active' in vals and param_old.active != rec.active:
                    updates['active'] = rec.active
                if updates:
                    param_old.write(updates)
        return res

    def unlink(self):
        raise ValidationError(_('Không thể xóa thành phần lương, bạn có thể lưu trữ thông tin.'))

    def action_archive(self):
        res = super().action_archive()
        for rec in self:
            if rec.is_allowance:
                config = self.APPLY_FOR_CONFIG[rec.apply_for]
                records = rec[config['field']]
                rec._update_allowance_on_records(records, 'remove')
        return res

    # Cấu hình ánh xạ giữa giá trị `apply_for` và các thông tin liên quan (model, tên trường)
    APPLY_FOR_CONFIG = {
        'employee': {'model': 'hr.employee', 'field': 'hr_employee_ids'},
        'job': {'model': 'hr.job', 'field': 'hr_job_ids'},
        'level': {'model': 'hr.level', 'field': 'hr_level_ids'},
    }

    def _get_allowance_field_names(self, force_is_outside=None):
        """
        Trả về tên các trường definition và property của phụ cấp.
        Dựa vào cờ `is_outside_salary` để quyết định là phụ cấp trong hay ngoài lương.

        :param force_is_outside: (Boolean) Nếu được truyền, sẽ dùng giá trị này thay vì
                                 giá trị hiện tại của record. Hữu ích khi cần xóa
                                 định nghĩa theo trạng thái cũ.
        :return: (tuple) Ví dụ: ('properties_definition_allowance_inside', 'allowance_inside')
        """
        is_outside = self.is_outside_salary if force_is_outside is None else force_is_outside
        if is_outside:
            return 'properties_definition_allowance_outside', 'allowance_outside'
        else:
            return 'properties_definition_allowance_inside', 'allowance_inside'

    def _update_allowance_on_records(self, records, action, force_state=None):
        """
        Hàm tổng quát để THÊM hoặc XÓA định nghĩa & giá trị phụ cấp
        trên một tập các record (jobs, levels, employees) được truyền vào.

        :param records: (RecordSet) Các record cần cập nhật (vd: self.env['hr.job'].browse(...)).
        :param action: (str) 'add' (thêm) hoặc 'remove' (xóa).
        :param force_state: (dict) Một dict chứa state cũ {'code', 'is_outside_salary'}
                        dùng khi cần xóa định nghĩa theo thông tin của record trước khi thay đổi.
        """
        if not records:
            return

        # Xác định các thông tin cần dùng (từ trạng thái hiện tại hoặc trạng thái cũ được truyền vào)
        state = force_state or self
        code = state.get('code') if isinstance(state, dict) else state.code
        is_outside = state.get('is_outside_salary') if isinstance(state, dict) else state.is_outside_salary
        def_field, prop_field = self._get_allowance_field_names(force_is_outside=is_outside)

        if action == 'add':
            prop_def = {"name": code, "type": "integer", "string": code, "default": 0}
            for record in records:
                current_defs = record[def_field] or []
                # Chỉ thêm nếu định nghĩa chưa tồn tại
                if not any(d.get("name") == code for d in current_defs):
                    record[def_field] = current_defs + [prop_def]

        elif action == 'remove':
            for record in records:
                # Xóa định nghĩa khỏi list
                current_defs = record[def_field] or []
                record[def_field] = [d for d in current_defs if d.get("name") != code]

                # Xóa cả giá trị của phụ cấp đó khỏi list
                current_props = record[prop_field] or []
                record[prop_field] = [p for p in current_props if p.get("name") != code]

    def _get_allowance_status(self, is_allowance, is_outside_salary):
        """
        Chuyển đổi 2 trường boolean thành một trạng thái dạng chuỗi dễ đọc và so sánh.
        :return: (str) 'none' (không phải phụ cấp), 'inside' (phụ cấp trong lương),
                 hoặc 'outside' (phụ cấp ngoài lương).
        """
        if not is_allowance:
            return 'none'
        return 'outside' if is_outside_salary else 'inside'