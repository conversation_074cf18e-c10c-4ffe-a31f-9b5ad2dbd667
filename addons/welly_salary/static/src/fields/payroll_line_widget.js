/** @odoo-module **/

import {registry} from "@web/core/registry";
import {Component, onWillStart, onWillUpdateProps, useState} from "@odoo/owl";
import {useService} from "@web/core/utils/hooks";
import {standardFieldProps} from "@web/views/fields/standard_field_props";
import {loadJS} from "@web/core/assets";

class PayrollLineWidget extends Component {
    setup() {
        this.orm = useService("orm");
        this.notification = useService("notification");
        this.state = useState({
            lines: [],
            propertyColumns: [],
            loading: true,
            editing: false,
            editedLines: new Map(), // Lưu trữ các thay đổi chưa lưu
            hasChanges: false,
            stateRecord: '',
            searchKeyword: '',
            taxPolicyOptions: [],
        });

        onWillStart(async () => {
            await this.loadData();
            await loadJS("https://cdn.sheetjs.com/xlsx-latest/package/dist/xlsx.full.min.js");
            // 👇 Load selection tax_policy từ model hr.employee
            const fields = await this.orm.call("hr.employee", "fields_get", ["tax_policy"]);
            this.state.taxPolicyOptions = fields.tax_policy.selection;
        });

        onWillUpdateProps(async (nextProps) => {
            if (nextProps.record.data.id !== this.props.record.data.id) {
                await this.loadData();
            }
            const newState = nextProps.record.data.state;
            if (newState !== this.state.stateRecord) {
                this.state.stateRecord = newState;
            }
        });
    }

    async loadData() {
        this.state.loading = true;
        this.state.stateRecord = this.props.record.data.state
        const payrollId = this.props.record.data.id;
        if (!payrollId) {
            this.state.lines = [];
            this.state.propertyColumns = [];
            this.state.loading = false;
            return;
        }

        try {
            // Lấy định nghĩa properties từ payroll
            const payrollData = await this.orm.read("hr.payroll", [payrollId],
                ["salary_properties_definition"]);

            const propertiesDefinition = payrollData[0].salary_properties_definition || [];

            // Tạo danh sách cột từ properties definition
            // Nếu propertiesDefinition là mảng object
            if (Array.isArray(propertiesDefinition)) {
                this.state.propertyColumns = propertiesDefinition.map(prop => ({
                    name: prop.name,
                    string: prop.string,
                    type: prop.type,
                    view_in_kanban: prop.view_in_kanban,
                }));
            } else {
                // Nếu là object thì convert thành array
                this.state.propertyColumns = Object.keys(propertiesDefinition).map(key => ({
                    name: key,
                    string: propertiesDefinition[key].string || key,
                    type: propertiesDefinition[key].type || 'char',
                    view_in_kanban: propertiesDefinition[key].view_in_kanban || false,
                }));
            }

            // Lấy dữ liệu payroll lines
            const lines = await this.orm.searchRead(
                "hr.payroll.line",
                [["payroll_id", "=", payrollId]],
                ["employee_id", "employee_code", "employee_department", "employee_job", "salary_values"],
                {order: "employee_id"}
            );

            this.state.lines = lines;
        } catch (error) {
            console.error("Error loading payroll line data:", error);
            this.state.lines = [];
            this.state.propertyColumns = [];
        }

        this.state.loading = false;
        this.applySearchFilter();
    }

    async recomputePayrollLines() {
        const payrollId = this.props.record.data.id;
        if (!payrollId) return;

        const confirmMsg = "Bạn có chắc chắn muốn tính lại bảng lương? Tất cả dòng lương hiện tại sẽ bị xoá.";
        if (!confirm(confirmMsg)) return;

        this.state.loading = true;

        try {
            // Gọi server-side xử lý
            await this.orm.call("hr.payroll", "action_recompute_payroll_lines", [payrollId]);

            this.notification.add("Đã tính lại bảng lương thành công!", {type: "success"});

            // Reload lại dữ liệu widget
            await this.loadData();

        } catch (error) {
            console.error("Lỗi khi tính lại bảng lương:", error);
            this.notification.add("Không thể tính lại bảng lương: " + error.message, {type: "danger"});
        }

        this.state.loading = false;
    }

    async recomputePayrollLinesFormulaOnly() {
        const payrollId = this.props.record.data.id;
        if (!payrollId) return;

        const confirmMsg = "Bạn có chắc chắn muốn tính lại bảng lương? Tất cả dòng lương hiện tại sẽ bị xoá.";
        if (!confirm(confirmMsg)) return;

        this.state.loading = true;

        try {
            // Gọi server-side xử lý
            await this.orm.call("hr.payroll", "action_recompute_payroll_lines_formula_only", [payrollId]);

            this.notification.add("Đã tính lại bảng lương với những giá trị công thức thành công!", {type: "success"});

            // Reload lại dữ liệu widget
            await this.loadData();

        } catch (error) {
            console.error("Lỗi khi tính lại bảng lương:", error);
            this.notification.add("Không thể tính lại bảng lương: " + error.message, {type: "danger"});
        }

        this.state.loading = false;
    }

    formatValue(value, type) {
        // Nếu value là false, null, undefined hoặc chuỗi rỗng thì hiển thị trống
        if (value === null || value === undefined || value === false || value === '') {
            return "";
        }

        switch (type) {
            case "float":
                return new Intl.NumberFormat('vi-VN', {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 2
                }).format(value);
            case "integer":
                return new Intl.NumberFormat('vi-VN').format(value);
            case "boolean":
                return value ? "Có" : "Không";
            case "selection":
                return value;
            case "char":
                return value.toString();
            default:
                return value.toString();
        }
    }

    getPropertyValue(salaryValues, propertyName) {
        // Kiểm tra nếu salaryValues không tồn tại hoặc không phải là mảng
        if (!salaryValues || !Array.isArray(salaryValues)) return null;

        // Tìm property theo name
        const property = salaryValues.find(prop => prop.name === propertyName);

        // Trả về value nếu tìm thấy, ngược lại trả về null
        return property ? property.value : null;
    }

    // Lấy giá trị đã chỉnh sửa hoặc giá trị gốc
    getEditedValue(lineId, propertyName) {
        const editedLine = this.state.editedLines.get(lineId);
        if (editedLine && editedLine.hasOwnProperty(propertyName)) {
            return editedLine[propertyName];
        }

        const line = this.state.lines.find(l => l.id === lineId);
        return this.getPropertyValue(line?.salary_values, propertyName);
    }

    // Cập nhật giá trị đã chỉnh sửa
    updateEditedValue(lineId, propertyName, rawValue) {
        const column = this.state.propertyColumns.find(col => col.name === propertyName);
        if (!column) return;

        const {valid, value, error} = this.validateInput(rawValue, column.type);

        if (!valid) {
            this.notification.add(`Dữ liệu không hợp lệ tại cột "${column.string}": ${error}`, {
                type: "danger",
            });
            return;
        }

        if (!this.state.editedLines.has(lineId)) {
            this.state.editedLines.set(lineId, {});
        }

        const editedLine = this.state.editedLines.get(lineId);
        editedLine[propertyName] = value;
        this.state.hasChanges = true;
    }

    // Bật/tắt chế độ chỉnh sửa
    toggleEditMode() {
        if (this.state.editing && this.state.hasChanges) {
            // Hỏi xác nhận khi có thay đổi chưa lưu
            if (confirm("Bạn có thay đổi chưa lưu. Bạn có muốn tiếp tục không?")) {
                this.cancelEdit();
            }
        } else {
            this.state.editing = !this.state.editing;
            if (!this.state.editing) {
                this.cancelEdit();
            }
        }
    }

    isCellEdited(lineId, propertyName) {
        const editedLine = this.state.editedLines.get(lineId);
        if (!editedLine) return false;

        return editedLine.hasOwnProperty(propertyName);
    }

    validateInput(value, type) {
        switch (type) {
            case "float":
                const parsedFloat = parseFloat(value);
                if (isNaN(parsedFloat)) return {valid: false, error: "Giá trị không phải số hợp lệ."};
                return {valid: true, value: parsedFloat};
            case "integer":
                const parsedInt = parseInt(value);
                if (isNaN(parsedInt)) return {valid: false, error: "Giá trị không phải số nguyên."};
                return {valid: true, value: parsedInt};
            case "boolean":
                return {valid: true, value: !!value};
            case "char":
            case "selection":
            default:
                return {valid: true, value: value};
        }
    }

    // Hủy chỉnh sửa
    cancelEdit() {
        this.state.editing = false;
        this.state.editedLines.clear();
        this.state.hasChanges = false;
    }

    // Lưu tất cả thay đổi
    async saveChanges() {
        if (!this.state.hasChanges) return;

        this.state.loading = true;

        try {
            const updates = [];

            // Tạo danh sách cập nhật
            for (const [lineId, changes] of this.state.editedLines.entries()) {
                const line = this.state.lines.find(l => l.id === lineId);
                if (!line) continue;

                // Clone salary_values để không ảnh hưởng dữ liệu gốc
                const updatedSalaryValues = [...(line.salary_values || [])];

                // Cập nhật các giá trị đã thay đổi
                for (const [propertyName, newValue] of Object.entries(changes)) {
                    const propertyIndex = updatedSalaryValues.findIndex(
                        prop => prop.name === propertyName
                    );

                    if (propertyIndex !== -1) {
                        updatedSalaryValues[propertyIndex] = {
                            ...updatedSalaryValues[propertyIndex],
                            value: newValue
                        };
                    }
                }

                updates.push({
                    id: lineId,
                    salary_values: updatedSalaryValues
                });
            }

            // Thực hiện cập nhật
            for (const update of updates) {
                await this.orm.write("hr.payroll.line", [update.id], {
                    salary_values: update.salary_values
                });
            }

            this.notification.add("Đã lưu thay đổi thành công!", {type: "success"});

            // Reset trạng thái và reload dữ liệu
            this.state.editing = false;
            this.state.editedLines.clear();
            this.state.hasChanges = false;

            await this.loadData();

        } catch (error) {
            console.error("Error saving changes:", error);
            this.notification.add("Lỗi khi lưu thay đổi: " + error.message, {type: "danger"});
        }

        this.state.loading = false;
    }

    async onEditLine(lineId) {
        const action = {
            name: "Chi Tiết",
            type: "ir.actions.act_window",
            res_model: "hr.payroll.line",
            res_id: lineId,
            views: [[false, "form"]],
            target: "new",
        };

        const result = await this.env.services.action.doAction(action, {
            onClose: async () => {
                // Form vừa đóng → reload lại dữ liệu
                await this.loadData();
            },
        });
    }

    async onAddLine() {
        const payrollId = this.props.record.data.id;
        if (!payrollId) return;

        const action = {
            type: "ir.actions.act_window",
            res_model: "hr.payroll.line",
            views: [[false, "form"]],
            target: "new",
            context: {
                default_payroll_id: payrollId,
            },
        };

        await this.env.services.action.doAction(action);
        // Reload data sau khi thêm
        await this.loadData();
    }

    applySearchFilter() {
        const keyword = (this.state.searchKeyword || '').toLowerCase().trim();
        if (!keyword) {
            this.state.filteredLines = this.state.lines;
            return;
        }

        this.state.filteredLines = this.state.lines.filter(line => {
            const name = line.employee_id?.[1]?.toLowerCase() || '';
            const code = line.employee_code?.toLowerCase() || '';
            return name.includes(keyword) || code.includes(keyword);
        });
    }


    onSearchInput(ev) {
        this.state.searchKeyword = ev.target.value;
        this.applySearchFilter();
    }

    async exportToExcel() {
        this.state.loading = true;

        try {
            if (!window.XLSX) {
                await this.env.services.assets.loadJS("https://cdn.sheetjs.com/xlsx-latest/package/dist/xlsx.full.min.js");
            }

            const header = ["Mã nhân viên", "Nhân viên", "Phòng ban", "Chức vụ",
                ...this.state.propertyColumns.filter(col => col.view_in_kanban === true).map(col => col.string)];
            const rows = this.state.filteredLines.map(line => {
                const row = [];

                // Các cột fix
                row.push(line.employee_code || "");
                row.push(line.employee_id?.[1] || "");
                row.push(line.employee_department[1] || "");
                row.push(line.employee_job[1] || "");

                // Các cột theo propertyColumns
                this.state.propertyColumns.forEach(col => {
                    if (col.view_in_kanban === false) return;
                    const rawValue = this.getPropertyValue(line.salary_values, col.name);

                    let parsedValue = "";
                    if (col.type === "boolean") {
                        parsedValue = rawValue === true ? "Có" : rawValue === false ? "Không" : "";
                    } else if (rawValue === null || rawValue === undefined || Number.isNaN(rawValue) || rawValue === false) {
                        parsedValue = "";
                    } else {
                        parsedValue = rawValue;
                    }

                    row.push(parsedValue);
                });

                return row;
            });

            const worksheetData = [header, ...rows];
            const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

            // 🔷 Styling header
            const headerStyle = {
                fill: {patternType: "solid", fgColor: {rgb: "5C3BBF"}},
                font: {color: {rgb: "FFFFFF"}, bold: true},
                alignment: {horizontal: "center", vertical: "center"},
            };

            header.forEach((_, index) => {
                const cellRef = XLSX.utils.encode_cell({r: 0, c: index});
                if (worksheet[cellRef]) {
                    worksheet[cellRef].s = headerStyle;
                }
            });

            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, "Bảng lương");

            const fileName = `bang_luong_${new Date().toISOString().slice(0, 10)}.xlsx`;
            XLSX.writeFile(workbook, fileName);

            this.notification.add("Đã xuất Excel thành công!", {type: "success"});

        } catch (err) {
            console.error("Export to Excel failed", err);
            this.notification.add("Lỗi khi xuất Excel: " + err.message, {type: "danger"});
        }

        this.state.loading = false;
    }

}

PayrollLineWidget.template = "PayrollLineWidgetTemplate";
PayrollLineWidget.props = {
    ...standardFieldProps,
    inputType: {type: String, optional: true},
    placeholder: {type: String, optional: true},
};
// Đăng ký widget
registry.category("fields").add("payroll_line_widget", PayrollLineWidget);

