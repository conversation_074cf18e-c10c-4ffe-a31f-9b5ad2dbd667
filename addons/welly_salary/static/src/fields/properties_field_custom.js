/** @odoo-module **/

import {PropertiesField} from "@web/views/fields/properties/properties_field";
import {registry} from "@web/core/registry";

// ""component override lại PropertiesField của odoo để không cho sửa và thêm mới giá trị properties **/

export class PropertiesFieldCustom extends PropertiesField {

    /** override hàm của component gốc để ẩn những dữ liệu có view_in_kanban == false */
    get groupedPropertiesList() {
        const columns = this.env.isSmall ? 1 : this.props.columns;
        // Lọc chỉ lấy các property có view_in_kanban === true
        const visibleProperties = this.propertiesList.filter(
            prop => !('view_in_kanban' in prop) || prop.view_in_kanban === true
        );

        // Nếu không có property nào, vẫn tạo mảng trống để hiện nút "Thêm"
        const res = [...Array(columns)].map(() => []);

        visibleProperties.forEach((val, index) => {
            res[index % columns].push(val);
        });

        return res;
    }

}

PropertiesFieldCustom.template = "welly_salary.PropertiesFieldReadonly";

registry.category("fields").add("properties_custom", PropertiesFieldCustom);
