<odoo>
    <record id="hr_employee_sales_target_view_form" model="ir.ui.view">
        <field name="name">hr_employee_sales_target_view_form</field>
        <field name="inherit_id" ref="welly_kpi.view_hr_employee_sales_target_form"/>
        <field name="model">hr.employee.sales.target</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='calculate_salary_by_club_revenue']" position="after">
                <field name="target_salary"
                       widget="monetary"
                       options="{'currency_field': 'currency_id'}"/>
            </xpath>
        </field>
    </record>

    <record id="view_hr_employee_sales_target_tree" model="ir.ui.view">
        <field name="name">view_hr_employee_sales_target_tree</field>
        <field name="inherit_id" ref="welly_kpi.view_hr_employee_sales_target_tree"/>
        <field name="model">hr.employee.sales.target</field>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="inside">
                <field name="sale_commission" widget="percentage"/>
                <field name="target_salary" widget="monetary" options="{'currency_field': 'currency_id'}"/>
            </xpath>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_hr_employee_sales_target_search" model="ir.ui.view">
        <field name="name">view_hr_employee_sales_target_search</field>
        <field name="model">hr.employee.sales.target</field>
        <field name="arch" type="xml">
            <search string="Tìm kiếm Sales Target">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="department_id"/>
                <field name="job_id"/>
                <field name="year"/>
                <field name="month"/>
                <separator/>
                <filter string="Năm hiện tại" name="year_current" domain="[('year', '=', datetime.datetime.now().year)]"/>
                <filter string="Tháng hiện tại" name="month_current" domain="[('month', '=', datetime.datetime.now().strftime('%m'))]"/>
                <group expand="0" string="Group By">
                    <filter string="Nhân viên" name="employee" context="{'group_by': 'employee_id'}"/>
                    <filter string="Phòng ban" name="department" context="{'group_by': 'department_id'}"/>
                    <filter string="Chức vụ" name="job" context="{'group_by': 'job_id'}"/>
                    <filter string="Năm" name="year" context="{'group_by': 'year'}"/>
                    <filter string="Tháng" name="month" context="{'group_by': 'month'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_hr_employee_sales_target_salary_view" model="ir.actions.act_window">
        <field name="name">Lương doanh số</field>
        <field name="res_model">hr.employee.sales.target</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'create': False, 'delete': False, 'duplicate': False}</field>
        <field name="search_view_id" ref="view_hr_employee_sales_target_search"/>
    </record>
</odoo>