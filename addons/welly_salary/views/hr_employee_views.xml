<odoo>
    <record id="welly_hr_employee_view_form_inherit" model="ir.ui.view">
        <field name="name">welly_hr_employee_view_form_inherit</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="arch" type="xml">

            <xpath expr="//notebook//page[@name='public']//group[@name='managers']" position="after">
                <group name="start_working_date" string="Nhận việc">
                    <field name="start_working_date"/>
                </group>
            </xpath>
            <xpath expr="//field[@name='company_country_code']" position="after">
                <field name="hr_level_id"/>
                <field name="hr_business_unit_id"/>
                <field name="hr_payslip_structure_id"/>
            </xpath>
            <xpath expr="//field[@name='work_phone']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='coach_id']" position="after">
                <field name="hr_contract_type_id"/>
                <label for="login"/>
                <div class="o_row">
                    <field name="login"/>
                    <button name="action_open_user_details"
                            class="btn-link"
                            type="object"
                            icon="fa-arrow-right"
                            title="Liên kết"/>
                </div>
            </xpath>
            <xpath expr="//field[@name='coach_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>


            <xpath expr="//field[@name='address_home_id']" position="after">
                <field name="address"/>
            </xpath>
            <xpath expr="//field[@name='address_home_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='children']" position="after">
                <field name="dependent_count"/>
            </xpath>
            <xpath expr="//field[@name='children']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='identification_id']" position="after">
                <field name="identification_date"/>
                <field name="identification_place"/>
            </xpath>

            <xpath expr="//notebook//page[@name='personal_information']//group[1]" position="after">
                <group string="Trình độ" name="certificate">
                    <field name="certificate_line_ids" nolabel="1" colspan="2">
                        <tree editable="bottom">
                            <field name="hr_certificate_id" options="{'no_create': True, 'no_create_edit':True}"/>
                            <field name="name"/>
                            <field name="issuing_unit"/>
                            <field name="level" widget="selection"/>
                            <field name="issue_date"/>
                            <field name="expiration_date"/>
                        </tree>
                    </field>
                </group>
            </xpath>

            <xpath expr="//notebook//page[@name='personal_information']" position="after">
                <page string="Lương" name="salary">
                    <group>
                        <group>
                            <field name="currency_id" invisible="1"/>
                            <field name="base_salary"/>
                            <field name="is_social_insurance_applied"/>
                            <field name="effective_salary"/>
                        </group>
                        <group>
                            <field name="contract_salary"/>
                            <field name="tax_policy"/>
                        </group>
                    </group>
                    <group string="Phụ cấp trong lương" name="allowance_inside">
                        <field name="self_container_id" invisible="1"/>
                        <field name="all_allowance_inside" noedit="1" nocreate="1" nolabel="1" colspan="2"/>
                    </group>
                    <group string="Phụ cấp ngoài lương" name="allowance_outside">
                        <field name="self_container_id" invisible="1"/>
                        <field name="all_allowance_outside" noedit="1" nocreate="1" nolabel="1" colspan="2"/>
                    </group>

                    <group string="Lịch sử lương" name="salary_history">
                        <field name="salary_history_ids" mode="tree" options="{'create': False, 'delete': False}" nolabel="1" colspan="2" default_order="date_to desc">
                            <tree>
                                <field name="currency_id" invisible="1"/>
                                <field name="date_from"/>
                                <field name="date_to"/>
                                <field name="contract_salary"/>
                                <field name="base_salary"/>
                                <field name="effective_salary"/>
                                <field name="description"/>
                            </tree>
                        </field>
                    </group>
                </page>
                <page string="Người phụ thuộc" name="dependents">
                    <field name="dependent_line_ids">
                        <tree editable="bottom">
                            <field name="hr_relationship_id" options="{'no_create': True, 'no_create_edit':True}"/>
                            <field name="name"/>
                            <field name="phone"/>
                            <field name="birth"/>
                            <field name="is_dependent"/>
                            <field name="start_dependent_date"/>
                            <field name="end_dependent_date"/>
                            <field name="job"/>
                            <field name="tax_code"/>
                        </tree>
                    </field>
                </page>
                <page string="Doanh số bán hàng" name="sales_target">
                    <field name="sales_target_ids"
                           options="{'create': False, 'delete': False, 'edit': False}"
                           nolabel="1"
                           default_order="year desc, month desc">
                        <tree>
                            <field name="currency_id" invisible="1"/>
                            <field name="name"/>
                            <field name="year" widget="raw_number"/>
                            <field name="month"/>
                            <field name="target" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                            <field name="revenue_display" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                            <field name="target_reached" widget="progressbar2"/>
                            <field name="sale_commission" widget="percentage"/>
                            <field name="target_salary" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                        </tree>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

    <record id="action_hr_salary_adjustment" model="ir.actions.server">
        <field name="name">Điều chỉnh lương</field>
        <field name="model_id" ref="welly_salary.model_hr_employee"/>
        <field name="binding_model_id" ref="welly_salary.model_hr_employee"/>
        <field name="binding_view_types">form</field>
        <field name="sequence">5</field>
        <field name="state">code</field>
        <field name="code">action = records.action_salary_adjustment()</field>
    </record>
</odoo>
