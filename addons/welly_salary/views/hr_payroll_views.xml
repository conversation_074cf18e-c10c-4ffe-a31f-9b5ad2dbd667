<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_payroll_view_tree" model="ir.ui.view">
        <field name="name">hr.payroll.tree</field>
        <field name="model">hr.payroll</field>
        <field name="arch" type="xml">
            <tree string="Payroll" duplicate="0" create="0">
                <field name="name"/>
                <field name="month"/>
                <field name="year"/>
                <field name="date_from"/>
                <field name="date_to"/>
                <field name="state"
                       widget="badge"
                       decoration-info="state in ('draft')"
                       decoration-warning="state in ('reviewed')"
                       decoration-success="state in ('approved', 'sent')"
                />
            </tree>
        </field>
    </record>

    <record id="hr_payroll_view_form" model="ir.ui.view">
        <field name="name">hr.payroll.form</field>
        <field name="model">hr.payroll</field>
        <field name="arch" type="xml">
            <form string="Bảng lương" class="o_form" duplicate="0">
                <header>
                    <!-- Button: Review -->
                    <button name="action_review" string="Gửi Review"
                            type="object" class="btn-primary"
                            attrs="{'invisible': [('state', '!=', 'draft')]}"/>

                    <!-- Button: Phê duyệt -->
                    <button name="action_approve" string="Phê Duyệt"
                            type="object" class="btn-primary"
                            attrs="{'invisible': [('state', '!=', 'reviewed')]}"/>

                    <!-- Button: Phát hành phiếu lương -->
                    <button name="action_create_payslip" string="Phát Hành Phiếu Lương"
                            type="object" class="btn-primary"
                            attrs="{'invisible': ['|', ('state', '!=', 'approved'), ('payslip_unsent_count', '=', 0)]}"/>

                    <!-- Button: Gửi phiếu lương -->
                    <button name="action_send_email_bulk" string="Gửi Phiếu Lương"
                            type="object" class="btn-primary"
                            attrs="{'invisible': ['|', ('state', '!=', 'approved'), ('payslip_email_unsent_count', '=', 0)]}"/>

                    <!-- Button: Đặt lại dự thảo -->
                    <button name="action_draft" string="Đặt Lại Dự Thảo"
                            type="object" class="btn-secondary"
                            attrs="{'invisible': [('state', 'in', ('sent', 'draft'))]}" />

                    <field name="state" widget="statusbar" statusbar_visible="draft,reviewed,approved,sent"/>
                </header>

                <sheet>
                    <group>
                        <group>
                            <field name="salary_period_id" readonly="1"/>
                            <label for="date_from" string="Chu kỳ"/>
                            <div>
                                <field name="date_from" class="oe_inline"/> - <field name="date_to"
                                                                                     class="oe_inline"/>
                            </div>

                            <label for="payslip_sent_count" string="Phiếu lương phát hành: " attrs="{'invisible': [('state', 'in', ('reviewed', 'draft'))]}"/>
                            <div attrs="{'invisible': [('state', 'in', ('reviewed', 'draft'))]}">
                                <span>
                                    Đã phát hành: <field name="payslip_sent_count" class="oe_inline"/> - Chưa phát hành: <field name="payslip_unsent_count" class="oe_inline"/>
                                    <a href="#" class="ml-5" type="object"
                                       name="action_view_payslip_status"
                                       style="margin-left: 5px;"
                                       title="Xem trạng thái phiếu lương">
                                        <i class="fa fa-list-alt fa-lg text-info"/>
                                    </a>
                                </span>
                            </div>

                            <label for="payslip_email_sent_count" string="Phiếu lương gửi email: " attrs="{'invisible': [('state', 'in', ('reviewed', 'draft'))]}"/>
                            <div attrs="{'invisible': [('state', 'in', ('reviewed', 'draft'))]}">
                                Đã gửi: <field name="payslip_email_sent_count" class="oe_inline"/> - Chưa gửi: <field name="payslip_email_unsent_count"
                                                                                     class="oe_inline"/>
                            </div>

                        </group>
                        <group>
                            <field name="month" widget="selection"/>
                            <field name="year" widget="selection"/>
                            <field name="company_id" readonly="1"/>

                            <label for="payslip_approved_count" string="Phiếu lương xác nhận: " attrs="{'invisible': [('state', 'in', ('reviewed', 'draft'))]}"/>
                            <div attrs="{'invisible': [('state', 'in', ('reviewed', 'draft'))]}">
                                Đã xác nhận: <field name="payslip_approved_count" class="oe_inline"/> - Chưa xác nhận: <field name="payslip_unapproved_count"
                                                                                     class="oe_inline"/>
                            </div>
                        </group>
                    </group>
                    <separator string="Phiếu lương"/>
                    <field name="payroll_line_ids" widget="payroll_line_widget" />
                </sheet>
                <div class="oe_chatter">
                    <field name="message_ids"/>
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="hr_payroll_view_search" model="ir.ui.view">
        <field name="name">hr.payroll.search</field>
        <field name="model">hr.payroll</field>
        <field name="arch" type="xml">
            <search string="Payroll">
                <field name="name"/>
                <field name="salary_period_id"/>
                <field name="state"/>
                <group expand="0" string="Group By">
                    <filter string="Kỳ lương" name="salary_period_id" domain="[]" context="{'group_by': 'salary_period_id'}"/>
                    <filter string="Trạng thái" name="state" domain="[]" context="{'group_by': 'state'}"/>
                    <filter string="Tháng" name="month" domain="[]" context="{'group_by': 'month'}"/>
                    <filter string="Năm" name="year" domain="[]" context="{'group_by': 'year'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_hr_payroll" model="ir.actions.act_window">
        <field name="name">Bảng lương</field>
        <field name="res_model">hr.payroll</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="hr_payroll_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Tạo mới Bảng lương
            </p>
        </field>
    </record>

    <!--    view hr.payroll.line-->
    <record id="hr_payroll_line_view_form" model="ir.ui.view">
        <field name="name">hr.payroll.form.line</field>
        <field name="model">hr.payroll.line</field>
        <field name="arch" type="xml">
            <form string="Bảng lương nhân viên" class="o_form">
                <sheet>
                    <group>
                        <field name="employee_id"/>
                        <field name="payroll_id"/>
                        <field name="payroll_state" invisible="1"/>
                    </group>
                    <notebook>
                        <page string="Tham số tính lương">
                            <field name="salary_values" widget="properties_custom"
                                   nolabel="1"
                                   columns="2"
                                   hideKanbanOption="1"
                                   attrs="{'readonly': [('payroll_state', '!=', 'reviewed')]}"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
</odoo> 
