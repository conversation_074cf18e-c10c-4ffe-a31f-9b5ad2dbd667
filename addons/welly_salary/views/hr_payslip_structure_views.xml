<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_hr_payslip_structure_form" model="ir.ui.view">
        <field name="name">hr.payslip.structure.form</field>
        <field name="model">hr.payslip.structure</field>
        <field name="arch" type="xml">
            <form string="Cấu trúc phiếu l<PERSON>" delete="0" duplicate="0">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="description"/>
                    </group>
                    <notebook>
                        <page string="Chi tiết lương" name="salary_param_ids">
                            <field name="salary_param_ids">
                                <tree editable="bottom">
                                    <field name="name" string="Tên tham số"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Nhân viên áp dụng" name="employee_ids">
                            <field name="employee_ids" widget="many2many_tags"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_ids"/>
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_hr_payslip_structure_tree" model="ir.ui.view">
        <field name="name">hr.payslip.structure.tree</field>
        <field name="model">hr.payslip.structure</field>
        <field name="arch" type="xml">
            <tree string="Cấu trúc phiếu lương" default_order="name" delete="0" duplicate="0">
                <field name="name"/>
                <field name="description"/>
                <field name="salary_param_ids" widget="many2many_tags"/>
            </tree>
        </field>
    </record>

    <record id="hr_payslip_structure_view_search" model="ir.ui.view">
        <field name="name">hr_payslip_structure.search</field>
        <field name="model">hr.payslip.structure</field>
        <field name="arch" type="xml">
            <search string="Cấu trúc phiếu lương">
                <field name="name"/>
                <filter name="inactive" string="Đã lưu trữ" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>

    <record id="action_hr_payslip_structure" model="ir.actions.act_window">
        <field name="name">Cấu trúc phiếu lương</field>
        <field name="res_model">hr.payslip.structure</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Tạo cấu trúc phiếu lương để xác định các tham số hiển thị.
            </p>
        </field>
    </record>

    <menuitem id="menu_hr_payslip_structure_root"
              name="Cấu trúc phiếu lương"
              parent="menu_welly_salary_configuration"
              sequence="40"
              action="action_hr_payslip_structure"/>
</odoo>
