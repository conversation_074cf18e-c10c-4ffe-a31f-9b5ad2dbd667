from odoo import fields, models


class RefusePayslip(models.TransientModel):
    _name = 'refuse.payslip'
    _description = "Nhập lý do từ chối phiếu lương"

    reason = fields.Text('Lý do từ chối', required=True)

    # thay đổi ngày bắt đầu và hết hạn của hợp đồng
    def action_reject_contract(self):
        slip = self.env['hr.payroll.payslip'].browse(self.env.context.get('active_ids'))
        # cập nhật log activities
        self.env['mail.message'].create({
            'model': 'hr.payroll.payslip',
            'res_id': slip.id,
            'message_type': 'comment',
            'body': f'Từ chối phiếu lương lý do: {self.reason}',
        })
        # cập nhật slip
        slip.update({
            'state': 'draft'
        })
