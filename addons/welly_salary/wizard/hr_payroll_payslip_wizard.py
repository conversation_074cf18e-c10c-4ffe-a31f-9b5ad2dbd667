from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class PayrollPayslipWizard(models.TransientModel):
    _name = 'hr.payroll.payslip.wizard'
    _description = 'Wizard <PERSON><PERSON><PERSON>'

    @api.model
    def default_get(self, fields):
        res = super().default_get(fields)
        payroll = self.env['hr.payroll'].browse(self.env.context.get('default_payroll_id'))
        if payroll:
            all_employees = payroll.payroll_line_ids.mapped('employee_id')
            existing = self.env['hr.payroll.payslip'].search([
                ('payroll_id', '=', payroll.id),
                ('salary_period_id', '=', payroll.salary_period_id.id),
            ]).mapped('employee_id')
            filtered_employees = all_employees - existing

            res['employee_ids'] = [(6, 0, filtered_employees.ids)]
            res['employee_ids_filter'] = [(6, 0, filtered_employees.ids)]
        return res

    employee_ids_filter = fields.Many2many(
        'hr.employee',
        'hr_payroll_payslip_wizard_employee_filter_rel',
        'wizard_id',
        'employee_id',
        string='Bộ lọc nhân viên ban đầu',
        readonly=True
    )

    payroll_id = fields.Many2one(
        'hr.payroll',
        string='Bảng lương',
        required=True,
        readonly=True
    )

    department_ids = fields.Many2many(
        'hr.department',
        string='Phòng/Ban'
    )

    employee_ids = fields.Many2many(
        'hr.employee',
        'hr_payroll_payslip_wizard_employee_rel',
        'wizard_id',
        'employee_id',
        string='Danh sách nhân viên'
    )

    @api.onchange('department_ids', 'payroll_id')
    def _onchange_department_id(self):
        if not self.payroll_id:
            self.employee_ids = [(6, 0, [])]
            return

        domain = [('id', 'in', self.payroll_id.payroll_line_ids.mapped('employee_id').ids)]

        if self.department_ids:
            domain.append(('department_id', 'in', self.department_ids.ids))

        # Nhân viên đã có phiếu lương thuộc bảng lương & kỳ lương này
        existing_emp_ids = self.env['hr.payroll.payslip'].search([
            ('payroll_id', '=', self.payroll_id.id),
            ('salary_period_id', '=', self.payroll_id.salary_period_id.id),
        ]).mapped('employee_id').ids

        domain.append(('id', 'not in', existing_emp_ids))

        employees = self.env['hr.employee'].search(domain)
        self.employee_ids = [(6, 0, employees.ids)]

    def action_create_payslips(self):
        self.ensure_one()
        if not self.employee_ids:
            raise ValidationError(_('Vui lòng chọn ít nhất một nhân viên để tạo phiếu lương.'))

        created_count = 0
        created_employees_names = []
        for employee in self.employee_ids:
            # Tìm payroll_line tương ứng của nhân viên trong bảng lương hiện tại
            payroll_line = self.payroll_id.payroll_line_ids.filtered(lambda l: l.employee_id == employee)
            if not payroll_line:
                continue  # hoặc raise nếu bạn muốn cứng hơn

            # Tạo phiếu lương
            self.env['hr.payroll.payslip'].create({
                'employee_id': employee.id,
                'salary_period_id': self.payroll_id.salary_period_id.id,
                'payroll_id': self.payroll_id.id,
                'payroll_line': payroll_line.id,
            })
            created_count += 1
            created_employees_names.append(employee.name)

        if created_count == 0:
            raise ValidationError(_('Không có phiếu lương nào được tạo. Kiểm tra lại danh sách nhân sự.'))

        # Ghi log action vào bảng lương
        if created_employees_names:
            self.env['mail.message'].create({
                'model': 'hr.payroll',
                'res_id': self.payroll_id.id,
                'message_type': 'comment',
                'body': _('Đã tạo phiếu lương cho: %s.') % ', '.join(created_employees_names),
            })

        message = _('Tạo thành công %d phiếu lương.') % created_count
        # kiểm tra nếu tất cả nhân viên đã được tạo phiếu lương thì chuyển trạng thái đã gửi phiếu lương
        self.payroll_id._compute_payslip_sent_counts()
        remaining = self.payroll_id.payslip_unsent_count  # Được compute mới nhất sau record.create()
        if remaining == 0:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Thành công'),
                    'message': _(
                        'Tạo phiếu lương thành công. Tất cả nhân sự đã được tạo phiếu lương.'),
                    'type': 'success',
                    'sticky': False,
                    'next': {'type': 'ir.actions.act_window_close'},
                }
            }
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Thành công'),
                'message': message,
                'type': 'success',
                'sticky': False,
                'next': {'type': 'ir.actions.act_window_close'},
            }
        }
