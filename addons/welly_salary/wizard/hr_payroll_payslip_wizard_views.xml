<odoo>
    <record id="view_payroll_payslip_wizard_form" model="ir.ui.view">
        <field name="name">payroll.payslip.wizard.form</field>
        <field name="model">hr.payroll.payslip.wizard</field>
        <field name="arch" type="xml">
            <form string="<PERSON><PERSON><PERSON> hành <PERSON>">
                <group>
                    <field name="payroll_id"/>
                    <field name="department_ids" widget="many2many_tags"/>
                    <field name="employee_ids_filter" invisible="1"/>
                    <field name="employee_ids" widget="many2many" context="{'default_is_active': True}" mode="tree" domain="[('id', 'in', employee_ids_filter)]"
                           options="{'no_create': True}">
                        <tree>
                            <field name="name"/>
                            <field name="work_email"/>
                            <field name="department_id"/>
                        </tree>
                    </field>
                </group>
                <footer>
                    <button string="Tạo" type="object" name="action_create_payslips" class="btn-primary"/>
                    <button string="Hủy" special="cancel" class="btn-link"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>
