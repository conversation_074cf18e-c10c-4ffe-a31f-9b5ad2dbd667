from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import timedelta


class HrSalaryAdjustWizard(models.TransientModel):
    _name = 'hr.salary.adjust.wizard'
    _description = '<PERSON><PERSON><PERSON>u chỉnh lương nhân viên'

    employee_id = fields.Many2one('hr.employee', string='<PERSON>h<PERSON> viên', required=True)

    contract_salary = fields.Monetary(string='<PERSON><PERSON><PERSON> lương theo hợp đồng', currency_field='currency_id')
    base_salary = fields.Monetary(string='Lương cơ bản', currency_field='currency_id')
    effective_salary = fields.Monetary(string='Lương hiệu quả', currency_field='currency_id')
    currency_id = fields.Many2one('res.currency', string='Tiền tệ', required=True)
    date_from = fields.Date(string='Từ ngày', required=True)
    date_to = fields.Date(string='Đến ngày')

    hr_level_id = fields.Many2one('hr.level', string='<PERSON>ấ<PERSON> bậc')
    job_id = fields.Many2one('hr.job', string='Vị trí công việc')

    all_allowance_inside = fields.Properties(
        string='<PERSON>ụ cấp trong l<PERSON>ơng',
        definition_record='employee_id',
        definition_record_field='all_properties_definition_allowance_inside',
        copy=True
    )
    all_allowance_outside = fields.Properties(
        string='Phụ cấp ngoài lương',
        definition_record='employee_id',
        definition_record_field='all_properties_definition_allowance_outside',
        copy=True
    )
    description = fields.Text(string='Mô tả')

    def _convert_properties_to_dict(self, properties_list):
        """
        Hàm trợ giúp: Chuyển đổi định dạng list của trường Properties
        thành một dict {tên_thuộc_tính: giá_trị} đơn giản.
        Ví dụ: [{'name': 'pc_job', 'type': 'integer', 'string': 'pc_job', 'value': 2000}] -> {'pc_job': 2000}
        """
        if not properties_list:
            return {}
        # Lấy ra cặp 'name' và 'value' từ mỗi phần tử trong danh sách
        return {
            prop.get('name'): prop.get('value')
            for prop in properties_list if prop.get('name')
        }

    def _get_merged_allowances(self, job, level, employee, allowance_field_name):
        """
        Gộp các phụ cấp từ job, level, và employee.
        """
        # Lấy dữ liệu từ các nguồn và chuyển đổi sang dạng dict
        job_dict = self._convert_properties_to_dict(job[allowance_field_name] if job else [])
        level_dict = self._convert_properties_to_dict(level[allowance_field_name] if level else [])
        employee_dict = self._convert_properties_to_dict(employee[allowance_field_name] if employee else [])

        # Giá trị từ dict bên phải sẽ ghi đè lên giá trị từ dict bên trái nếu có cùng key.
        merged_allowances = {**job_dict, **level_dict, **employee_dict}
        return merged_allowances

    @api.model
    def default_get(self, fields_list):
        res = super().default_get(fields_list)
        ctx = self.env.context
        # Lấy các bản ghi từ context, trả về recordset rỗng nếu không có ID
        job = self.env['hr.job'].browse(ctx.get('default_job_id'))
        level = self.env['hr.level'].browse(ctx.get('default_hr_level_id'))
        employee = self.env['hr.employee'].browse(ctx.get('default_employee_id'))

        # Sử dụng hàm trợ giúp để lấy và gộp các phụ cấp
        res['all_allowance_outside'] = self._get_merged_allowances(
            job, level, employee, 'allowance_outside'
        )
        res['all_allowance_inside'] = self._get_merged_allowances(
            job, level, employee, 'allowance_inside'
        )

        return res

    @api.constrains('date_from', 'date_to', 'employee_id')
    def _check_date_range(self):
        for record in self:
            if record.date_to and record.date_from and record.date_to < record.date_from:
                raise ValidationError(_("Từ ngày phải nhỏ hơn Đến ngày."))

            domain = [
                ('hr_employee_id', '=', record.employee_id.id),
                ('id', '!=', record._origin.id if record._origin else False)
            ]
            existing_histories = self.env['hr.salary.history'].search_read(
                domain,
                fields=['id', 'date_from', 'date_to'],
                order='date_to desc, date_from desc'
            )

            if not existing_histories:
                continue

            new_start = record.date_from
            new_end = record.date_to

            latest_history_id = existing_histories[0]['id']

            for history in existing_histories:
                existing_start = history['date_from']
                existing_end = history['date_to']

                is_latest_history = history['id'] == latest_history_id

                # Nếu đây là lịch sử cuối cùng và nó không có ngày kết thúc
                # và ngày bắt đầu mới lớn hơn ngày bắt đầu của lịch sử cuối cùng, thì bỏ qua kiểm tra chồng chéo
                if is_latest_history and not existing_end and new_start > existing_start:
                    continue

                # Check trùng chéo
                overlap = True
                if existing_end and existing_end < new_start:
                    overlap = False
                elif new_end and new_end < existing_start:
                    overlap = False

                if overlap:
                    if not is_latest_history:
                        raise ValidationError(
                            _("Đã tồn tại lịch sử lương trong khoảng thời gian này. Vui lòng kiểm tra lại."))
                    else:
                        if new_start <= existing_start:
                            raise ValidationError(
                                _("Ngày bắt đầu không thể nhỏ hơn hoặc bằng ngày bắt đầu của lịch sử lương cuối cùng."))

    def action_save(self):
        self.ensure_one()

        existing_histories = self.env['hr.salary.history'].search_read([
            ('hr_employee_id', '=', self.employee_id.id)
        ], fields=['id', 'date_from', 'date_to'], order='date_to desc, date_from desc')

        if existing_histories:
            latest_history = existing_histories[0]
            latest_history_obj = self.env['hr.salary.history'].browse(latest_history['id'])

            # Kiểm tra điều kiện cập nhật date_to của lịch sử cuối cùng
            if self.date_from <= latest_history['date_to'] or not latest_history['date_to']:
                # Cập nhật date_to = date_from mới - 1 ngày
                new_date_to = self.date_from - timedelta(days=1)
                latest_history_obj.write({'date_to': new_date_to})

        vals = {
            'hr_employee_id': self.employee_id.id,
            'contract_salary': self.contract_salary,
            'base_salary': self.base_salary,
            'effective_salary': self.effective_salary,
            'currency_id': self.currency_id.id,
            'hr_level_id': self.hr_level_id.id if self.hr_level_id else False,
            'job_id': self.job_id.id if self.job_id else False,
            'all_allowance_inside': self.all_allowance_inside,
            'all_allowance_outside': self.all_allowance_outside,
            'description': self.description,
            'date_from': self.date_from,
            'date_to': self.date_to
        }

        # Tạo record mới
        self.env['hr.salary.history'].create(vals)
        self.env['hr.salary.history'].invalidate_cache()

        return {'type': 'ir.actions.act_window_close'}