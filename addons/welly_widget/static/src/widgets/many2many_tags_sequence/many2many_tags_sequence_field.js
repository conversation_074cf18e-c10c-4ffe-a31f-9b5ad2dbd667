/** @odoo-module **/

import { _lt } from "@web/core/l10n/translation";
import {
    useActiveActions,
    useX2ManyCrud,
} from "@web/views/fields/relational_utils";
import { registry } from "@web/core/registry";
import { usePopover } from "@web/core/popover/popover_hook";
import { useService } from "@web/core/utils/hooks";

import { useRef } from "@odoo/owl";
import { Many2ManyTagsField } from "@web/views/fields/many2many_tags/many2many_tags_field";
import { Many2ManyTagsSequencePopover } from "./many2many_tags_sequence_popover";
import { TagsSequenceList } from "./tags_list";
import { ConfirmationDialog } from "@web/core/confirmation_dialog/confirmation_dialog";

/**
 * Widget Many2ManyTagsSequence
 * Mở rộng từ Many2ManyTagsField để thêm tính năng sequence cho các tags
 * Khi thay đổi thứ tự của tags, giá trị sequence sẽ được cập nhật theo
 */
export class Many2ManyTagsSequenceField extends Many2ManyTagsField {
    setup() {
        this.orm = useService("orm");
        this.previousColorsMap = {};
        this.popover = usePopover();
        this.dialog = useService("dialog");
        this.dialogClose = [];

        this.autoCompleteRef = useRef("autoComplete");

        const { saveRecord, removeRecord } = useX2ManyCrud(() => this.props.value, true);

        this.activeActions = useActiveActions({
            fieldType: "many2many",
            crudOptions: {
                create: this.props.canCreate && this.props.createDomain,
                createEdit: this.props.canCreateEdit,
                onDelete: removeRecord,
            },
            getEvalParams: (props) => {
                return {
                    evalContext: this.evalContext,
                    readonly: props.readonly,
                };
            },
        });

        this.update = (recordList) => {
            if (!recordList) {
                return;
            }
            if (Array.isArray(recordList)) {
                const resIds = recordList.map((rec) => rec.id);
                const res = saveRecord(resIds);
                this.loadSequence();
                return res;
            }
            const res = saveRecord(recordList);
            this.loadSequence();
            return res;
        };

        if (this.props.canQuickCreate) {
            this.quickCreate = async (name) => {
                const created = await this.orm.call(this.props.relation, "name_create", [name], {
                    context: this.context,
                });
                return saveRecord([created[0]]);
            };
        }
        this.props.sequences = {};
        this.loadSequence();
    }

    async loadSequence() {
        const fieldName = this.props.name;
        const rootId = this.props.record.data.id;
        const { relation } = this.props.record.activeFields[fieldName].options;
        const sequences = await this.orm.call(relation, "get_root_sequence", [rootId], {});
        const sequencesMap = {};
        sequences.forEach((sequence) => {
            sequencesMap[sequence[0]] = sequence[1];
        });
        this.props.sequences = sequencesMap;
        this.render();
    }

    getTagProps(record) {
        const props = super.getTagProps(record);
        // Chỉ gán sự kiện click khi trường không ở chế độ readonly
        props.onClick = (ev) => this.onBadgeClick(ev, record);
        if (!this.props.sequences) {
            this.props.sequences = {};
            this.loadSequence();
        }
        props.sequence = this.props.sequences[record.data.id];
        return props;
    }

    async onBadgeClick(ev, record) {
        // Nếu trường đang ở chế độ readonly, không mở popover
        if (this.props.readonly) {
            return;
        }

        const fieldName = this.props.name;
        const currentTarget = ev.currentTarget;
        let rootId = this.props.record.data.id;
        if (!rootId) {
            // hiển thị thông báo lỗi
            const confirmSave = await new Promise((resolve) => {
                this.dialog.add(ConfirmationDialog, {
                    body: this.env._t("Bạn cần lưu dữ liệu trước khi sắp xếp"),
                    cancel: () => resolve(false),
                    close: () => resolve(false),
                    confirm: () => resolve(true),
                });
            });
            if (!confirmSave) {
                return;
            } else {
                await this.props.record.save();
                return;
            }
        }
        const recordId = record.data.id;
        const { relation } = this.props.record.activeFields[fieldName].options;
        const sequence = this.props.sequences[recordId];
        // Open tooltip to edit sequence
        this.popover.add(
            currentTarget,
            Many2ManyTagsSequencePopover,
            {relation, rootId, recordId, sequence, parent: this},
            {closeOnClickAway: true}
        );

    }

    get tags() {
        const tags = this.props.value.records.map((record) => this.getTagProps(record));
        tags.sort((a, b) => a.sequence - b.sequence);
        return tags;
    }

}

// Đăng ký widget mới
Many2ManyTagsSequenceField.template = "welly_widget.Many2ManyTagsSequenceField";
Many2ManyTagsSequenceField.components = {
    ...Many2ManyTagsField.components,
    TagsSequenceList,
};
registry.category("fields").add("many2many_tags_sequence", Many2ManyTagsSequenceField);
