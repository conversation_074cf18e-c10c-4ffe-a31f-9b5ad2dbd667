from odoo import models, fields, api
import requests
import json
import logging

_logger = logging.getLogger(__name__)

class ZnsConfig(models.Model):
    _name = 'zns.config'
    _description = '<PERSON><PERSON>u hình thông tin Zalo Notification Service'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    end_point = fields.Char(string='Đường dẫn gửi tin nhắn', required=True, tracking=True)
    token_url = fields.Char(string='Đường dẫn lấy access token', required=True, tracking=True)
    app_id = fields.Char(string='Application ID', required=True, tracking=True)
    app_secret = fields.Char(string='Secret Key', required=True)
    template_id = fields.Char(string='Mẫu gửi tin nhắn', required=True, tracking=True)
    access_token = fields.Char(string='Access Token')
    refresh_token = fields.Char(string='Refresh Token', required=True)
    token_updated_date = fields.Datetime(string='<PERSON><PERSON>y cập nhật token')

    def refresh_access_token(self):
        """<PERSON>àm mới access token bằng refresh token"""
        self.ensure_one()

        if not self.refresh_token:
            _logger.error("Không có refresh token để làm mới access token")
            return False

        payload = {
            "app_id": self.app_id,
            "refresh_token": self.refresh_token,
            "grant_type": "refresh_token"
        }

        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "secret_key": self.app_secret
        }

        try:
            response = requests.post(self.token_url, data=payload, headers=headers)

            if response.status_code == 200:
                token_data = response.json()
                if token_data.get("access_token"):
                    self.write({
                        'access_token': token_data.get("access_token"),
                        'refresh_token': token_data.get("refresh_token", self.refresh_token),
                        'token_updated_date': fields.Datetime.now(),
                    })
                    self.env['mail.message'].create({
                        'model': 'zns.config',
                        'res_id': self.id,
                        'message_type': 'comment',
                        'body': "Làm mới ZNS access token thành công",
                    })
                    return True
                else:
                    self.env['mail.message'].create({
                        'model': 'zns.config',
                        'res_id': self.id,
                        'message_type': 'comment',
                        'body': f"Lỗi khi làm mới ZNS token: {response.status_code}, {response.text}",
                    })
                    return False
            else:
                self.env['mail.message'].create({
                    'model': 'zns.config',
                    'res_id': self.id,
                    'message_type': 'comment',
                    'body': f"Lỗi khi làm mới ZNS token: {response.status_code}, {response.text}",
                })
                return False

        except Exception as e:
            _logger.exception(f"Lỗi khi kết nối đến Zalo OAuth: {str(e)}")
            return False

    @api.model
    def get_zns_config(self):
        """Lấy cấu hình ZNS hiện tại (chỉ có 1 bản ghi active)"""
        return self.search([(1, '=', 1)], limit=1)

    @api.model
    def cron_refresh_access_token(self):
        """Hàm được gọi bởi cron job để làm mới access token"""
        zns_config = self.get_zns_config()
        if zns_config:
            return zns_config.refresh_access_token()
        else:
            self.env['mail.message'].create({
                'model': 'zns.config',
                'res_id': self.id,
                'message_type': 'comment',
                'body': f"Không tìm thấy cấu hình ZNS nào hoạt động",
            })
            return False
