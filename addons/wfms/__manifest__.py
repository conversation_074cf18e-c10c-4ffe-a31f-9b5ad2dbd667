# -*- coding: utf-8 -*-
{
    'name': "GYMO Welly SYSTEM MANAGEMENT",

    'summary': """Welly SYSTEM MANAGEMENT""",

    'description': """
        Tất cả các module c<PERSON><PERSON>o sẽ được quản lý tại đây
    """,
    'license': 'OEEL-1',
    'author': "Welly Tech",
    'website': "https://wellytech.vn",

    # Categories can be used to filter modules in modules listing
    'category': 'Welly',
    'version': '1.0.1',
    'sequence': 1,

    # any module necessary for this one to work correctly
    'depends': ['base', 'web', 'bus', 'mail'],

    # always loaded
    'data': [
        'data/system_parameter.xml',
        'security/ir.model.access.csv',
        'security/group.xml',
        'security/wfms_security.xml',
        'security/notification_security.xml',
        'views/views.xml',
        'views/login_layout.xml',
        'views/ir_ui_menu.xml',
        'views/menu_item.xml',
        'views/notification_views.xml',
    ],
    'assets': {
        'web.assets_backend': [
            ('remove', 'web/static/src/legacy/js/core/py_utils.js'),
            'wfms/static/src/xml/tracking_value.xml',
            'wfms/static/src/css/wfms.css',
            'wfms/static/src/js/web_client.js',
            'wfms/static/src/core/error_dialogs.xml',
            'wfms/static/src/core/error_dialogs.js',
            'wfms/static/src/js/py_utils.js',
            # Add new notification assets
            'wfms/static/src/xml/navbar_notification.xml',
            'wfms/static/src/js/navbar_notification.js',
        ]
    },
    'post_load': 'post_load',
    # only loaded in demonstration mode
    'installable': True,
    'application': True,
    'auto_install': False,
}
