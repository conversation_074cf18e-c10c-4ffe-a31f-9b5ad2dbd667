.o_form_view .o_field_widget {
  margin-bottom: 20px;
}

.o_input {
  border-bottom: 1px solid #ccc !important;
}


.tooltip-inner {
  max-width: 1024px !important;
}

.o_image_zoom {
  align-items: center;
  justify-content: center;
  object-fit: contain;

  img {
    min-width: 512px;
    max-width: 100%;
    max-height: 100%;
    height: auto;
  }
}


.o_field_widget.o_field_many2many_tags input.o_input,
.o_field_many2many_selection input.o_input {
  border-bottom: none !important;
}

.o_wrap_label>label {
  margin-top: 5px !important;
}

.o_form_view:not(.o_field_highlight) .o_field_many2one_selection .o_external_button,
.o_form_view:not(.o_field_highlight) .o_field_many2one_selection .o_dropdown_button,
.o_form_view:not(.o_field_highlight) .o_field_many2many_selection .o_dropdown_button,
.o_form_view .o_datepicker .o_datepicker_button {
  visibility: visible !important;
}

select,
body:not(.o_touch_device) .o_field_selection:not(:hover):not(:focus-within) select:not(:hover),
body:not(.o_touch_device) .o_field_selection:hover select:hover {
  background: transparent url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='7' height='4' viewBox='0 0 7 4'><polygon fill='%23495057' points='3.5 4 7 0 0 0'/></svg>") no-repeat center !important;
  background-position: bottom 10px right 5px !important;
}

.o_field_widget.o_field_many2many_tags .o_tags_input .o_tag {
  padding: 5px;
  margin-right: 4px;
}

.o_field_widget.o_field_many2many_tags_avatar .badge {
  padding: 3px 5px !important;
}

.o_field_widget.o_field_many2many .badge {
  padding: .5rem !important;
}

.o_field_CopyClipboardText>div,
.o_field_CopyClipboardURL>div,
.o_field_CopyClipboardChar>div {
  border: none !important;
}

.o_calendar_sidebar_container .o_calendar_filter .o_calendar_filter_items_checkall .o_cw_filter_input_bg {
  height: 20px;
  width: 20px;
}

.o_calendar_sidebar_container .o_calendar_filter .o_calendar_filter_items_checkall .o_cw_filter_input_bg>i {
  margin-top: 2px;
  margin-left: 2px;
}

.o_calendar_sidebar_container .o_calendar_filter .o_calendar_filter_item .o_cw_filter_input_bg.o_beside_avatar>i {
  margin-top: 2px;
  margin-left: 1px;
}

.o-checkbox.form-check {
  padding-top: 5px !important;
}

.note-editable {
  border-bottom: 1px solid #ddd !important;
}

.o_onboarding_container {
  width: 100% !important;
}

.bootstrap-datetimepicker-widget ul li.show {
  width: 100%;
}

.row {
  --gutter-x: 64px !important;
  width: 100% !important;
}

.row.gutter-15 {
  --gutter-x: 15px !important;
  --gutter-y: 15px !important;
}

.row.gutter-5 {
  --gutter-x: 5px !important;
  --gutter-y: 5px !important;
}

.o_web_sign_draw_button,
.o_web_sign_load_button {
  width: 100%;
  border: 1px solid #ddd;
}

.o_web_sign_draw_button:focus,
.o_web_sign_load_button:focus {
  box-shadow: none !important;
}

.o_web_sign_draw_button.active,
.o_web_sign_load_button.active,
.o_web_sign_draw_button:hover,
.o_web_sign_load_button:hover {
  background-color: #66598f;
  color: white;
}

.badge {
  padding: 3px 6px 6px 6px !important
}