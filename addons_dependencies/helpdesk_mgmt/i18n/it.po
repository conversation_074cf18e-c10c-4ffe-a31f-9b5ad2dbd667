# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_mgmt
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-02-15 11:06+0000\n"
"Last-Translator: mymage <<EMAIL>>\n"
"Language-Team: none\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.6.2\n"

#. module: helpdesk_mgmt
#. odoo-javascript
#: code:addons/helpdesk_mgmt/static/src/js/new_ticket.js:0
#, python-format
msgid "%s file exceed the maximum file size of %s."
msgstr "Il file %s ha una dimensione superiore al massimo di %s."

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_ticket_list
msgid "<em class=\"font-weight-normal text-muted\">Tickets in category:</em>"
msgstr ""
"<em class=\"font-weight-normal text-muted\">Ticket nella categoria:</em>"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_ticket_list
msgid "<em class=\"font-weight-normal text-muted\">Tickets in stage:</em>"
msgstr "<em class=\"font-weight-normal text-muted\">Ticket nella fase:</em>"

#. module: helpdesk_mgmt
#: model:mail.template,body_html:helpdesk_mgmt.assignment_email_template
msgid ""
"<p>Hello <t t-out=\"object.user_id.name\"></t>,</p>\n"
"                <p>The ticket <t t-out=\"object.number\"></t> has been "
"assigned to you.</p>\n"
"            "
msgstr ""
"<p>Hello <t t-out=\"object.user_id.name\"></t>,</p>\n"
"                <p>Il ticket <t t-out=\"object.number\"></t> ti è stato "
"assegnato.</p>\n"
"            "

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_helpdesk_ticket_page
msgid "<small class=\"text-right\">Stage:</small>"
msgstr "<small class=\"text-right\">Fase:</small>"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_kanban_view
msgid "<span>View</span>"
msgstr "<span>Vista</span>"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_helpdesk_ticket_page
msgid "<strong class=\"d-block mb-2\">Attachments</strong>"
msgstr "<strong class=\"d-block mb-2\">Allegati</strong>"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_helpdesk_ticket_page
msgid "<strong>Assignee</strong>"
msgstr "<strong>Assegnatario</strong>"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_helpdesk_ticket_page
msgid ""
"<strong>Attachments:</strong>\n"
"                            <br/>"
msgstr ""
"<strong>Allegati:</strong>\n"
"                            <br/>"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_helpdesk_ticket_page
msgid "<strong>Category:</strong>"
msgstr "<strong>Categoria:</strong>"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_helpdesk_ticket_page
msgid "<strong>Close Date:</strong>"
msgstr "<strong>Data chiusura:</strong>"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_helpdesk_ticket_page
msgid "<strong>Customer</strong>"
msgstr "<strong>Cliente</strong>"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_helpdesk_ticket_page
msgid "<strong>Date:</strong>"
msgstr "<strong>Data:</strong>"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_helpdesk_ticket_page
msgid "<strong>Description</strong>"
msgstr "<strong>Descrizione</strong>"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_helpdesk_ticket_page
msgid "<strong>Last Stage Update:</strong>"
msgstr "<strong>Ultimo aggiornamento della fase:</strong>"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_helpdesk_ticket_page
msgid "<strong>Message and communication history</strong>"
msgstr "<strong>Cronologia messaggio e comunicazioni</strong>"

#. module: helpdesk_mgmt
#: model:mail.template,body_html:helpdesk_mgmt.closed_ticket_template
msgid ""
"<table border=\"0\" width=\"100%\" cellpadding=\"0\" bgcolor=\"#ededed\" "
"style=\"padding: 20px; background-color: #ededed; border-collapse:separate;"
"\">\n"
"                    <tbody>\n"
"                      <!-- HEADER -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" "
"cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-"
"color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                            <tr>\n"
"                              <td valign=\"middle\" align=\"right\">\n"
"                                <img t-att-src=\"'/logo.png?company=%s' % "
"object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; "
"width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\">\n"
"                              </td>\n"
"                            </tr>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"                      <!-- CONTENT -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" "
"cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"min-width: 590px; background-"
"color: rgb(255, 255, 255); padding: 20px; border-collapse:separate;\">\n"
"                            <tbody>\n"
"                              <td valign=\"top\" style=\"font-family:Arial,"
"Helvetica,sans-serif; color: #555; font-size: 14px;\">\n"
"                                <p>Hello <t t-out=\"object.user_id.name\"></"
"t>,</p>\n"
"                                <p>The ticket \"<t t-out=\"object."
"display_name\"></t>\" has been closed.</p>\n"
"                              </td>\n"
"                            </tbody>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"                      <!-- FOOTER -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" "
"cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-"
"color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                            <tr>\n"
"                              <td valign=\"middle\" align=\"left\" "
"style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: "
"12px;\">\n"
"                                <t t-out=\"object.company_id.phone\"></t>\n"
"                              </td>\n"
"                              <td valign=\"middle\" align=\"left\" "
"style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: "
"12px;\">\n"
"                                <t t-out=\"object.company_id.email\"></t>\n"
"                              </td>\n"
"                              <td valign=\"middle\" align=\"left\" "
"style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: "
"12px;\">\n"
"                                <t t-out=\"object.company_id.website\"></t>\n"
"                              </td>\n"
"                            </tr>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"                    </tbody>\n"
"                </table>\n"
"            "
msgstr ""
"<table border=\"0\" width=\"100%\" cellpadding=\"0\" bgcolor=\"#ededed\" "
"style=\"padding: 20px; background-color: #ededed; border-collapse:separate;"
"\">\n"
"                    <tbody>\n"
"                      <!-- HEADER -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" "
"cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-"
"color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                            <tr>\n"
"                              <td valign=\"middle\" align=\"right\">\n"
"                                <img t-att-src=\"'/logo.png?company=%s' % "
"object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; "
"width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\">\n"
"                              </td>\n"
"                            </tr>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"                      <!-- CONTENT -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" "
"cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"min-width: 590px; background-"
"color: rgb(255, 255, 255); padding: 20px; border-collapse:separate;\">\n"
"                            <tbody>\n"
"                              <td valign=\"top\" style=\"font-family:Arial,"
"Helvetica,sans-serif; color: #555; font-size: 14px;\">\n"
"                                <p>Hello <t t-out=\"object.user_id.name\"></"
"t>,</p>\n"
"                                <p>The ticket \"<t t-out=\"object."
"display_name\"></t>\" has been closed.</p>\n"
"                              </td>\n"
"                            </tbody>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"                      <!-- FOOTER -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" "
"cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-"
"color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                            <tr>\n"
"                              <td valign=\"middle\" align=\"left\" "
"style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: "
"12px;\">\n"
"                                <t t-out=\"object.company_id.phone\"></t>\n"
"                              </td>\n"
"                              <td valign=\"middle\" align=\"left\" "
"style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: "
"12px;\">\n"
"                                <t t-out=\"object.company_id.email\"></t>\n"
"                              </td>\n"
"                              <td valign=\"middle\" align=\"left\" "
"style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: "
"12px;\">\n"
"                                <t t-out=\"object.company_id.website\"></t>\n"
"                              </td>\n"
"                            </tr>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"                    </tbody>\n"
"                </table>\n"
"            "

#. module: helpdesk_mgmt
#: model:mail.template,body_html:helpdesk_mgmt.changed_stage_template
msgid ""
"<table border=\"0\" width=\"100%\" cellpadding=\"0\" bgcolor=\"#ededed\" "
"style=\"padding: 20px; background-color: #ededed; border-collapse:separate;"
"\">\n"
"                    <tbody>\n"
"                      <!-- HEADER -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" "
"cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-"
"color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                            <tr>\n"
"                              <td valign=\"middle\" align=\"right\">\n"
"                                <img t-att-src=\"'/logo.png?company=%s' % "
"object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; "
"width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\">\n"
"                              </td>\n"
"                            </tr>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"                      <!-- CONTENT -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" "
"cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"min-width: 590px; background-"
"color: rgb(255, 255, 255); padding: 20px; border-collapse:separate;\">\n"
"                            <tbody>\n"
"                              <td valign=\"top\" style=\"font-family:Arial,"
"Helvetica,sans-serif; color: #555; font-size: 14px;\">\n"
"                                <p>Hello <t t-out=\"object.user_id.name\"></"
"t>,</p>\n"
"                                <p>The ticket \"<t t-out=\"object."
"display_name\"></t>\" stage has changed to <t t-out=\"object.stage_id."
"name\"></t>.</p>\n"
"                              </td>\n"
"                            </tbody>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"                      <!-- FOOTER -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" "
"cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-"
"color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                            <tr>\n"
"                              <td valign=\"middle\" align=\"left\" "
"style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: "
"12px;\">\n"
"                                <t t-out=\"object.company_id.phone\"></t>\n"
"                              </td>\n"
"                              <td valign=\"middle\" align=\"left\" "
"style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: "
"12px;\">\n"
"                                <t t-out=\"object.company_id.email\"></t>\n"
"                              </td>\n"
"                              <td valign=\"middle\" align=\"left\" "
"style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: "
"12px;\">\n"
"                                <t t-out=\"object.company_id.website\"></t>\n"
"                              </td>\n"
"                            </tr>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"                    </tbody>\n"
"                </table>\n"
"            "
msgstr ""
"<table border=\"0\" width=\"100%\" cellpadding=\"0\" bgcolor=\"#ededed\" "
"style=\"padding: 20px; background-color: #ededed; border-collapse:separate;"
"\">\n"
"                    <tbody>\n"
"                      <!-- HEADER -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" "
"cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-"
"color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                            <tr>\n"
"                              <td valign=\"middle\" align=\"right\">\n"
"                                <img t-att-src=\"'/logo.png?company=%s' % "
"object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; "
"width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\">\n"
"                              </td>\n"
"                            </tr>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"                      <!-- CONTENT -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" "
"cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"min-width: 590px; background-"
"color: rgb(255, 255, 255); padding: 20px; border-collapse:separate;\">\n"
"                            <tbody>\n"
"                              <td valign=\"top\" style=\"font-family:Arial,"
"Helvetica,sans-serif; color: #555; font-size: 14px;\">\n"
"                                <p>Salve<t t-out=\"object.user_id.name\"></"
"t>,</p>\n"
"                                <p>La fase del ticket \"<t t-out=\"object."
"display_name\"></t>\" è cambiata a <t t-out=\"object.stage_id.name\"></t>.</"
"p>\n"
"                              </td>\n"
"                            </tbody>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"                      <!-- FOOTER -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" "
"cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-"
"color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                            <tr>\n"
"                              <td valign=\"middle\" align=\"left\" "
"style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: "
"12px;\">\n"
"                                <t t-out=\"object.company_id.phone\"></t>\n"
"                              </td>\n"
"                              <td valign=\"middle\" align=\"left\" "
"style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: "
"12px;\">\n"
"                                <t t-out=\"object.company_id.email\"></t>\n"
"                              </td>\n"
"                              <td valign=\"middle\" align=\"left\" "
"style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: "
"12px;\">\n"
"                                <t t-out=\"object.company_id.website\"></t>\n"
"                              </td>\n"
"                            </tr>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"                    </tbody>\n"
"                </table>\n"
"            "

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_team__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Un dizionario Python che verrà valutato per fornire valori predefiniti "
"durante la creazione di nuovi record per questo alias."

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_team_form
msgid "Accept Emails From"
msgstr "Accetta email da"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__access_warning
msgid "Access warning"
msgstr "Avviso accesso"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__message_needaction
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__message_needaction
msgid "Action Needed"
msgstr "Azione richiesta"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__active
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_category__active
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_channel__active
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_stage__active
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_tag__active
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__active
msgid "Active"
msgstr "Attivo"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_team_tree
msgid "Active tickets"
msgstr "Ticket attivi"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__activity_ids
msgid "Activities"
msgstr "Attività"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decorazione eccezione attività"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__activity_state
msgid "Activity State"
msgstr "Stato attività"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona tipo attività"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_create_ticket
msgid "Add Attachments"
msgstr "Aggiungi Allegati"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__alias_contact
msgid "Alias Contact Security"
msgstr "Alias contatto sicurezza"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__alias_name
msgid "Alias Name"
msgstr "Nome alias"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__alias_domain
msgid "Alias domain"
msgstr "Dominio alias"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__alias_model_id
msgid "Aliased Model"
msgstr "Modello con alias"

#. module: helpdesk_mgmt
#. odoo-python
#: code:addons/helpdesk_mgmt/controllers/myaccount.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_kanban_view
#, python-format
msgid "All"
msgstr "Tutto"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_team__show_in_portal
msgid "Allow to select this team when creating a new ticket in the portal."
msgstr ""
"Permetti la selezione di questa squadra durante la creazione di nuovo ticket "
"dal portale."

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.ticket_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_category_form
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_channel_form
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_team_form
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_category_search
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_channel_search
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_stage_form
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_stage_search
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_tag_form
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_tag_search
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_team_search
msgid "Archived"
msgstr "In archivio"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_team_form
msgid "Assign to"
msgstr "Assegna a"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.ticket_view_form
msgid "Assign to me"
msgstr "Assegna a me"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__assigned_date
msgid "Assigned Date"
msgstr "Data Assegnazione"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__user_id
msgid "Assigned user"
msgstr "Utente assegnato"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__message_attachment_count
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__message_attachment_count
msgid "Attachment Count"
msgstr "Numero allegati"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_team_form
msgid "Avatar"
msgstr "Avatar"

#. module: helpdesk_mgmt
#: model:helpdesk.ticket.stage,name:helpdesk_mgmt.helpdesk_ticket_stage_awaiting
msgid "Awaiting"
msgstr "In attesa"

#. module: helpdesk_mgmt
#: model:ir.model.fields.selection,name:helpdesk_mgmt.selection__helpdesk_ticket__kanban_state__blocked
msgid "Blocked"
msgstr "Bloccato"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_ticket_list
msgid "By"
msgstr "Di"

#. module: helpdesk_mgmt
#: model:helpdesk.ticket.stage,name:helpdesk_mgmt.helpdesk_ticket_stage_cancelled
msgid "Cancelled"
msgstr "Annullato"

#. module: helpdesk_mgmt
#: model:ir.actions.act_window,name:helpdesk_mgmt.helpdesk_ticket_category_action
#: model:ir.ui.menu,name:helpdesk_mgmt.helpdesk_ticket_category_menu
msgid "Categories"
msgstr "Categorie"

#. module: helpdesk_mgmt
#. odoo-python
#: code:addons/helpdesk_mgmt/controllers/myaccount.py:0
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__category_id
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__category_ids
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_create_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_ticket_list
#, python-format
msgid "Category"
msgstr "Categoria"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__channel_id
msgid "Channel"
msgstr "Canale"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket__channel_id
msgid ""
"Channel indicates where the source of a ticketcomes from (it could be a "
"phone call, an email...)"
msgstr "Il Canale indica qual è l'origine di un ticket (telefonata, email...)"

#. module: helpdesk_mgmt
#: model:ir.actions.act_window,name:helpdesk_mgmt.helpdesk_ticket_channel_action
#: model:ir.ui.menu,name:helpdesk_mgmt.helpdesk_ticket_channel_menu
msgid "Channels"
msgstr "Canali"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_ticket_list
msgid "Close Date"
msgstr "Data Chiusura"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_stage__close_from_portal
msgid "Close From Portal"
msgstr "Scegli dal portale"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__closed
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_stage__closed
msgid "Closed"
msgstr "Chiuso"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__closed_date
msgid "Closed Date"
msgstr "Data Chiusura"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__color
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_tag__color
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__color
msgid "Color Index"
msgstr "Indice colore"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
msgid "Commercial Entity"
msgstr "Entità commerciale"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__commercial_partner_id
msgid "Commercial Partner"
msgstr "Partner commerciale"

#. module: helpdesk_mgmt
#: model:ir.model,name:helpdesk_mgmt.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__company_id
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_category__company_id
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_channel__company_id
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_stage__company_id
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_tag__company_id
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__company_id
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_category_search
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_channel_search
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_stage_search
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_team_search
msgid "Company"
msgstr "Azienda"

#. module: helpdesk_mgmt
#: model:ir.model,name:helpdesk_mgmt.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni configurazione"

#. module: helpdesk_mgmt
#: model:ir.ui.menu,name:helpdesk_mgmt.helpdesk_ticket_config_main_menu
msgid "Configuration"
msgstr "Configurazione"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_team_form
msgid "Configure domain name"
msgstr "Configura nome dominio"

#. module: helpdesk_mgmt
#: model:ir.model,name:helpdesk_mgmt.model_res_partner
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__partner_id
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_helpdesk_ticket_page
msgid "Contact"
msgstr "Contatto"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_ticket_list
msgid "Create Date"
msgstr "Data creazione"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__create_uid
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_category__create_uid
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_channel__create_uid
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_stage__create_uid
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_tag__create_uid
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__create_date
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_category__create_date
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_channel__create_date
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_stage__create_date
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_tag__create_date
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__create_date
msgid "Created on"
msgstr "Creato il"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_ticket_list
msgid "Current stage of the ticket"
msgstr "Fase attuale del ticket"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_helpdesk_ticket_page
msgid "Current stage of this ticket"
msgstr "Fase attuale di questo ticket"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Messaggio personalizzato per il rifiuto"

#. module: helpdesk_mgmt
#. odoo-python
#: code:addons/helpdesk_mgmt/models/helpdesk_ticket.py:0
#, python-format
msgid "Customer"
msgstr "Cliente"

#. module: helpdesk_mgmt
#. odoo-python
#: code:addons/helpdesk_mgmt/models/helpdesk_ticket.py:0
#, python-format
msgid "Customer Email"
msgstr "Email Cliente"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket__access_url
msgid "Customer Portal URL"
msgstr "URL portale cliente"

#. module: helpdesk_mgmt
#: model:ir.actions.act_window,name:helpdesk_mgmt.helpdesk_ticket_dashboard_action
#: model:ir.ui.menu,name:helpdesk_mgmt.helpdesk_ticket_dashboard_menu
msgid "Dashboard"
msgstr "Bacheca"

#. module: helpdesk_mgmt
#: model:ir.model.fields.selection,name:helpdesk_mgmt.selection__helpdesk_ticket__kanban_state__normal
msgid "Default"
msgstr "Predefinito"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__alias_defaults
msgid "Default Values"
msgstr "Valori predefiniti"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_kanban
msgid "Delete"
msgstr "Elimina"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__description
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_stage__description
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_create_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.ticket_view_form
msgid "Description"
msgstr "Descrizione"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__display_name
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_category__display_name
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_channel__display_name
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_stage__display_name
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_tag__display_name
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_stage__close_from_portal
msgid ""
"Display button in portal ticket form to allow closing ticket with this stage "
"as target."
msgstr ""
"Visualizza pulsante nel modulo portale per consentire la chiusura del ticket "
"con questa fase come obiettivo."

#. module: helpdesk_mgmt
#: model:helpdesk.ticket.stage,name:helpdesk_mgmt.helpdesk_ticket_stage_done
msgid "Done"
msgstr "Fatto"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_kanban
msgid "Dropdown menu"
msgstr "Menu a tendina"

#. module: helpdesk_mgmt
#: model:ir.actions.server,name:helpdesk_mgmt.action_duplicate_ticket
msgid "Duplicate"
msgstr "Duplica"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_kanban
msgid "Edit"
msgstr "Modifica"

#. module: helpdesk_mgmt
#: model:helpdesk.ticket.channel,name:helpdesk_mgmt.helpdesk_ticket_channel_email
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__partner_email
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__alias_id
msgid "Email"
msgstr "E-mail"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_team_form
msgid "Email Alias"
msgstr "Alias email"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_stage__mail_template_id
msgid "Email Template"
msgstr "Modello e-mail"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__email_cc
msgid "Email cc"
msgstr "cc Email"

#. module: helpdesk_mgmt
#. odoo-javascript
#: code:addons/helpdesk_mgmt/static/src/js/new_ticket.js:0
#, python-format
msgid "File upload"
msgstr "Carica file"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_stage__fold
msgid "Folded in Kanban"
msgstr "Minimizzato nel Kanban"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_team_form
msgid ""
"Follow this salesteam to automatically track the events associated to users "
"of this team."
msgstr ""
"Segui questo team di vendita per seguire automaticamente gli eventi "
"associati agli utenti in questo team."

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__message_follower_ids
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__message_follower_ids
msgid "Followers"
msgstr "Seguito da"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__message_partner_ids
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguito da (partner)"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome es. fa-tasks"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
msgid "Future Activities"
msgstr "Attività Future"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket__sequence
msgid "Gives the sequence order when displaying a list of tickets."
msgstr "Stabilisce l'ordine con cui visualizzare un elenco di ticket."

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
msgid "Group By"
msgstr "Raggruppa per"

#. module: helpdesk_mgmt
#: model:ir.model,name:helpdesk_mgmt.model_ir_http
msgid "HTTP Routing"
msgstr "Instradamento HTTP"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__has_message
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__has_message
msgid "Has Message"
msgstr "Ha un messaggio"

#. module: helpdesk_mgmt
#: model:ir.module.category,name:helpdesk_mgmt.module_helpdesk_category
#: model:ir.ui.menu,name:helpdesk_mgmt.helpdesk_ticket_main_menu
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.res_config_settings_view_form
msgid "Helpdesk"
msgstr "Assistenza clienti"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_category_search
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_channel_search
msgid "Helpdesk Category Search"
msgstr "Cerca categoria assistenza clienti"

#. module: helpdesk_mgmt
#: model:mail.template,name:helpdesk_mgmt.changed_stage_template
msgid "Helpdesk Changed Stage notification Email"
msgstr "E-mail notifica cambio stato assistenza clienti"

#. module: helpdesk_mgmt
#: model:mail.template,name:helpdesk_mgmt.closed_ticket_template
msgid "Helpdesk Closed Ticket Notification Email"
msgstr "E-mail notifica chiusura ticket assistenza clienti"

#. module: helpdesk_mgmt
#: model:res.groups,name:helpdesk_mgmt.group_helpdesk_manager
msgid "Helpdesk Manager"
msgstr "Responsabile assistenza clienti"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_res_users__helpdesk_team_ids
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_team_form
msgid "Helpdesk Team"
msgstr "Team assistenza clienti"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_team_search
msgid "Helpdesk Team Search"
msgstr "Cerca team assistenza clienti"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_team_form
msgid "Helpdesk Team name..."
msgstr "Nome team assistenza clienti..."

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_stage__team_ids
msgid "Helpdesk Teams"
msgstr "Team assistenza clienti"

#. module: helpdesk_mgmt
#: model:ir.actions.act_window,name:helpdesk_mgmt.action_helpdesk_ticket_kanban_from_dashboard
#: model:ir.model,name:helpdesk_mgmt.model_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.ticket_view_form
msgid "Helpdesk Ticket"
msgstr "Ticket assistenza clienti"

#. module: helpdesk_mgmt
#: model:ir.model,name:helpdesk_mgmt.model_helpdesk_ticket_category
msgid "Helpdesk Ticket Category"
msgstr "Categoria ticket assistenza clienti"

#. module: helpdesk_mgmt
#: model:ir.model,name:helpdesk_mgmt.model_helpdesk_ticket_channel
msgid "Helpdesk Ticket Channel"
msgstr "Canale ticket assistenza clienti"

#. module: helpdesk_mgmt
#: model:mail.message.subtype,name:helpdesk_mgmt.hlp_tck_team_created
msgid "Helpdesk Ticket Created"
msgstr "Ticket assistenza clienti creato"

#. module: helpdesk_mgmt
#: model:ir.model,name:helpdesk_mgmt.model_helpdesk_ticket_stage
msgid "Helpdesk Ticket Stage"
msgstr "Fase ticket assistenza clienti"

#. module: helpdesk_mgmt
#: model:ir.model,name:helpdesk_mgmt.model_helpdesk_ticket_tag
msgid "Helpdesk Ticket Tag"
msgstr "Etichetta ticket assistenza clienti"

#. module: helpdesk_mgmt
#: model:ir.model,name:helpdesk_mgmt.model_helpdesk_ticket_team
msgid "Helpdesk Ticket Team"
msgstr "Team ticket assistenza clienti"

#. module: helpdesk_mgmt
#: model:ir.module.category,description:helpdesk_mgmt.module_helpdesk_category
msgid "Helps you handle your helpdesk security."
msgstr "Aiuta a gestire la sicurezza della tua assistenza clienti."

#. module: helpdesk_mgmt
#: model:ir.model.fields.selection,name:helpdesk_mgmt.selection__helpdesk_ticket__priority__2
msgid "High"
msgstr "Alta"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_kanban_view
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
msgid "High Priority"
msgstr "Priorità Alta"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__id
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_category__id
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_channel__id
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_stage__id
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_tag__id
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__id
msgid "ID"
msgstr "ID"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_team__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task "
"creation alias)"
msgstr ""
"ID del record padre che contiene l'alias (esempio: progetto che contiene "
"l'alias di creazione dell'attività)"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__activity_exception_icon
msgid "Icon"
msgstr "Icona"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona per indicare un'attività eccezione."

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket__message_needaction
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_team__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se selezionata, nuovi messaggi richiedono attenzione."

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket__message_has_error
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_team__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Se selezionata, alcuni messaggi hanno un errore di consegna."

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_stage__mail_template_id
msgid ""
"If set an email will be sent to the customer when the ticketreaches this "
"step."
msgstr ""
"Se impostato verrà mandata una mail al cliente quando il ticket raggiunge "
"questa fase."

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_team__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Se impostato, questo contenuto verrà inviato automaticamente agli utenti non "
"autorizzati invece del messaggio predefinito."

#. module: helpdesk_mgmt
#: model:helpdesk.ticket.stage,name:helpdesk_mgmt.helpdesk_ticket_stage_in_progress
msgid "In Progress"
msgstr "In corso"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__message_is_follower
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__message_is_follower
msgid "Is Follower"
msgstr "Segue"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__kanban_state
msgid "Kanban State"
msgstr "Stato Kanban"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket____last_update
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_category____last_update
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_channel____last_update
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_stage____last_update
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_tag____last_update
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: helpdesk_mgmt
#. odoo-python
#: code:addons/helpdesk_mgmt/controllers/myaccount.py:0
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__last_stage_update
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_ticket_list
#, python-format
msgid "Last Stage Update"
msgstr "Ultimo aggiornamento fase"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__write_uid
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_category__write_uid
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_channel__write_uid
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_stage__write_uid
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_tag__write_uid
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__write_date
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_category__write_date
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_channel__write_date
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_stage__write_date
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_tag__write_date
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
msgid "Last Week"
msgstr "Settimana Scorsa"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
msgid "Late Activities"
msgstr "Attività in ritardo"

#. module: helpdesk_mgmt
#: model:ir.model.fields.selection,name:helpdesk_mgmt.selection__helpdesk_ticket__priority__0
msgid "Low"
msgstr "Bassa"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__message_main_attachment_id
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__message_main_attachment_id
msgid "Main Attachment"
msgstr "Allegato principale"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__attachment_ids
msgid "Media Attachments"
msgstr "Media Allegati"

#. module: helpdesk_mgmt
#: model:ir.model.fields.selection,name:helpdesk_mgmt.selection__helpdesk_ticket__priority__1
msgid "Medium"
msgstr "Media"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__user_ids
msgid "Members"
msgstr "Membri"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__message_has_error
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__message_has_error
msgid "Message Delivery error"
msgstr "Errore di consegna messaggio"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__message_ids
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__message_ids
msgid "Messages"
msgstr "Messaggi"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
msgid "My Activities"
msgstr "Le mie attività"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Scadenza mia attività"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
msgid "My Followed Tickets"
msgstr "I ticket che seguo"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
msgid "My Tickets"
msgstr "I miei tickets"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_category__name
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_channel__name
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_tag__name
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__name
msgid "Name"
msgstr "Nome"

#. module: helpdesk_mgmt
#: model:helpdesk.ticket.stage,name:helpdesk_mgmt.helpdesk_ticket_stage_new
msgid "New"
msgstr "Nuovo"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_my_home
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_my_tickets
msgid "New Ticket"
msgstr "Nuovo Ticket"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.res_config_settings_view_form
msgid "New ticket form (portal)"
msgstr "Scheda nuovo ticket (portale)"

#. module: helpdesk_mgmt
#. odoo-python
#: code:addons/helpdesk_mgmt/controllers/myaccount.py:0
#, python-format
msgid "Newest"
msgstr "Il più recente"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Scadenza prossima attività"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__activity_summary
msgid "Next Activity Summary"
msgstr "Riepilogo prossima attività"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo prossima attività"

#. module: helpdesk_mgmt
#. odoo-python
#: code:addons/helpdesk_mgmt/models/helpdesk_ticket.py:0
#, python-format
msgid "No Subject"
msgstr "Nessun oggetto"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_my_tickets
msgid "No tickets found."
msgstr "Nessun ticket trovato."

#. module: helpdesk_mgmt
#. odoo-python
#: code:addons/helpdesk_mgmt/controllers/myaccount.py:0
#, python-format
msgid "None"
msgstr "Nessuno"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_ticket_list
msgid "Number"
msgstr "Numero"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__message_needaction_counter
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__message_needaction_counter
msgid "Number of Actions"
msgstr "Numero di azioni"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__message_has_error_counter
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__message_has_error_counter
msgid "Number of errors"
msgstr "Numero di errori"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket__message_needaction_counter
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_team__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numero di messaggi che richiedono un'azione"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket__message_has_error_counter
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_team__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numero di messaggi con errore di consegna"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__todo_ticket_count
msgid "Number of tickets"
msgstr "Numero di ticket"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__todo_ticket_count_high_priority
msgid "Number of tickets in high priority"
msgstr "Numero di ticket con priorità alta"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__todo_ticket_count_unassigned
msgid "Number of tickets unassigned"
msgstr "Numero di ticket non assegnati"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__todo_ticket_count_unattended
msgid "Number of tickets unattended"
msgstr "Numero di ticket non gestiti"

#. module: helpdesk_mgmt
#: model:helpdesk.ticket.category,name:helpdesk_mgmt.helpdesk_category_3
msgid "Odoo"
msgstr "Odoo"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
msgid "Open"
msgstr "Aperto"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_team__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ID facoltativo di un thread (record) a cui verranno allegati tutti i "
"messaggi in arrivo, anche se non hanno risposto. Se impostato, disabiliterà "
"completamente la creazione di nuovi record."

#. module: helpdesk_mgmt
#: model:helpdesk.ticket.channel,name:helpdesk_mgmt.helpdesk_ticket_channel_other
msgid "Other"
msgstr "Altro"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.ticket_view_form
msgid "Other Information"
msgstr "Altre informazioni"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__alias_user_id
msgid "Owner"
msgstr "Proprietario"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__alias_parent_model_id
msgid "Parent Model"
msgstr "Modello padre"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ID record thread padre"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_team__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not "
"necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Modello padre che detiene l'alias. Il modello che contiene il riferimento "
"alias non è necessariamente il modello fornito da alias_model_id (esempio: "
"progetto (parent_model) e task (model))"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
msgid "Partner"
msgstr "Partner"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__partner_name
msgid "Partner Name"
msgstr "Nome partner"

#. module: helpdesk_mgmt
#: model:helpdesk.ticket.channel,name:helpdesk_mgmt.helpdesk_ticket_channel_phone
msgid "Phone"
msgstr "Telefono"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_views_pivot
msgid "Pivot Analysis"
msgstr "Analisi pivot"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_team__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following "
"channels\n"
msgstr ""
"Politica per inviare un messaggio sul documento utilizzando il mailgateway.\n"
"- chiunque: chiunque può inviare\n"
"- clienti: solo i clienti autenticati\n"
"- chi segue: solo chi segue il documento collegato o i membri dei seguenti "
"canali\n"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__access_url
msgid "Portal Access URL"
msgstr "URL accesso portale"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__priority
msgid "Priority"
msgstr "Priorità"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_team_tree
msgid "Priority tickets"
msgstr "Ticket prioritari"

#. module: helpdesk_mgmt
#: model:ir.model.fields.selection,name:helpdesk_mgmt.selection__helpdesk_ticket__kanban_state__done
msgid "Ready for next stage"
msgstr "Pronto per la prossima fase"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__alias_force_thread_id
msgid "Record Thread ID"
msgstr "ID thread record"

#. module: helpdesk_mgmt
#: model:helpdesk.ticket.stage,name:helpdesk_mgmt.helpdesk_ticket_stage_rejected
msgid "Rejected"
msgstr "Respinto"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_res_partner__helpdesk_ticket_ids
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_res_users__helpdesk_ticket_ids
msgid "Related tickets"
msgstr "Ticket correlati"

#. module: helpdesk_mgmt
#: model:ir.actions.act_window,name:helpdesk_mgmt.helpdesk_ticket_reporting_action
#: model:ir.ui.menu,name:helpdesk_mgmt.helpdesk_ticket_reporting_menu
msgid "Reporting"
msgstr "Rendicontazione"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.res_config_settings_view_form
msgid "Required Category"
msgstr "Categoria richiesta"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_res_company__helpdesk_mgmt_portal_category_id_required
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_res_config_settings__helpdesk_mgmt_portal_category_id_required
msgid "Required Category field in Helpdesk portal"
msgstr "Campo categoria richiesta nel portale assistenza clienti"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.res_config_settings_view_form
msgid "Required Team"
msgstr "Team richiesto"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_res_company__helpdesk_mgmt_portal_team_id_required
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_res_config_settings__helpdesk_mgmt_portal_team_id_required
msgid "Required Team field in Helpdesk portal"
msgstr "Campo team richiesto nel portale assistenza clienti"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__activity_user_id
msgid "Responsible User"
msgstr "Utente responsabile"

#. module: helpdesk_mgmt
#. odoo-python
#: code:addons/helpdesk_mgmt/controllers/myaccount.py:0
#, python-format
msgid "Search in All"
msgstr "Cerca fra tutti"

#. module: helpdesk_mgmt
#. odoo-python
#: code:addons/helpdesk_mgmt/controllers/myaccount.py:0
#, python-format
msgid "Search in Number"
msgstr "Cerca fra i numeri"

#. module: helpdesk_mgmt
#. odoo-python
#: code:addons/helpdesk_mgmt/controllers/myaccount.py:0
#, python-format
msgid "Search in Title"
msgstr "Cerca nel titolo"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__access_token
msgid "Security Token"
msgstr "Token di sicurezza"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_res_company__helpdesk_mgmt_portal_select_team
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_res_config_settings__helpdesk_mgmt_portal_select_team
msgid "Select team in Helpdesk portal"
msgstr "Seleziona team nel portale assistenza clienti"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_create_ticket
msgid "Send a new ticket"
msgstr "Invia nuovo ticket"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__sequence
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_category__sequence
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_channel__sequence
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_stage__sequence
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__sequence
msgid "Sequence"
msgstr "Sequenza"

#. module: helpdesk_mgmt
#: model:ir.actions.act_window,name:helpdesk_mgmt.action_helpdesk_mgmt_config_settings
#: model:ir.ui.menu,name:helpdesk_mgmt.helpdesk_config_settings_menu
msgid "Settings"
msgstr "Impostazioni"

#. module: helpdesk_mgmt
#: model:ir.actions.server,name:helpdesk_mgmt.model_helpdesk_ticket_action_share
msgid "Share"
msgstr "Condividi"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
msgid "Show all records which has next action date is before today"
msgstr "Visualizza tutte le righe con l'azione successiva antecedente ad oggi"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__show_in_portal
msgid "Show in portal form"
msgstr "Mostra nel modulo del portale"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.res_config_settings_view_form
msgid "Show teams form"
msgstr "Mostra modulo squadre"

#. module: helpdesk_mgmt
#: model:helpdesk.ticket.category,name:helpdesk_mgmt.helpdesk_category_1
msgid "Software"
msgstr "Software"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_stage__team_ids
msgid "Specific team that uses this stage. If it is empty all teams could uses"
msgstr ""
"Team specifico che usa questa fase. Se vuoto tutti i team possono usarla"

#. module: helpdesk_mgmt
#. odoo-python
#: code:addons/helpdesk_mgmt/controllers/myaccount.py:0
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__stage_id
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_ticket_list
#, python-format
msgid "Stage"
msgstr "Fase"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_stage__name
msgid "Stage Name"
msgstr "Nome fase"

#. module: helpdesk_mgmt
#: model:ir.actions.act_window,name:helpdesk_mgmt.helpdesk_ticket_stage_action
#: model:ir.ui.menu,name:helpdesk_mgmt.helpdesk_ticket_stage_menu
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_category_form
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_channel_form
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_stage_form
msgid "Stages"
msgstr "Fasi"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stato in base alle attività\n"
"Scaduto: la data richiesta è trascorsa\n"
"Oggi: la data attività è oggi\n"
"Pianificato: attività future."

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_create_ticket
msgid "Subject"
msgstr "Oggetto"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_create_ticket
msgid "Submit Ticket"
msgstr "Invia Ticket"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
msgid "Tag"
msgstr "Etichetta"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__tag_ids
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_tag_form
msgid "Tags"
msgstr "Etichette"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__team_id
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_create_ticket
msgid "Team"
msgstr "Team"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__user_id
msgid "Team Leader"
msgstr "Team leader"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_team_form
msgid "Team Members"
msgstr "Membri del team"

#. module: helpdesk_mgmt
#: model:ir.actions.act_window,name:helpdesk_mgmt.helpdesk_ticket_team_action
#: model:ir.ui.menu,name:helpdesk_mgmt.helpdesk_ticket_team_menu
msgid "Teams"
msgstr "Teams"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_team__alias_id
msgid ""
"The email address associated with                                this "
"channel. New emails received will                                "
"automatically create new tickets assigned                                to "
"the channel."
msgstr ""
"L'indirizzo email associato a                                questo canale. "
"Le nuove email ricevute creeranno automaticamente nuovi ticket da "
"assegnare                                al canale."

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_team__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming "
"email that does not reply to an existing record will cause the creation of a "
"new record of this model (e.g. a Project Task)"
msgstr ""
"Il modello (tipo documento Odoo) a cui corrisponde questo alias. Qualsiasi e-"
"mail in arrivo che non risponde a un record esistente causerà la creazione "
"di un nuovo record di questo modello (es. un task progetto)"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_team__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Il nome dell'alias email, ad es. 'lavori' se vuoi ricevere email per "
"<<EMAIL>>"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_team__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"Il proprietario dei record creati dopo aver ricevuto email con questo alias. "
"Se questo campo non è impostato il sistema tenterà di trovare il "
"proprietario corretto in base all'indirizzo del mittente (Da) oppure "
"utilizzerà l'account amministratore se non viene trovato alcun utente di "
"sistema per quell'indirizzo."

#. module: helpdesk_mgmt
#: model:mail.template,subject:helpdesk_mgmt.closed_ticket_template
msgid "The ticket {{object.number}} has been closed."
msgstr "Il ticket {{object.number}} è stato chiuso."

#. module: helpdesk_mgmt
#: model:mail.template,subject:helpdesk_mgmt.changed_stage_template
msgid "The ticket {{object.number}} stage has changed."
msgstr "La fase del ticket {{object.number}} è cambiata."

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""
"Questa fase è minimizzata nella vista Kanban quando non ci sono record da "
"mostrare nella fase stessa."

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
msgid "Ticket"
msgstr "Ticket"

#. module: helpdesk_mgmt
#: model:mail.template,name:helpdesk_mgmt.assignment_email_template
msgid "Ticket Assignment"
msgstr "Assegnazione ticket"

#. module: helpdesk_mgmt
#: model:mail.message.subtype,name:helpdesk_mgmt.hlp_tck_created
msgid "Ticket Created"
msgstr "Ticket creato"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_stage_search
msgid "Ticket Stage Search"
msgstr "Ricerca fase Ticket"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_tag_search
msgid "Ticket Tag Search"
msgstr "Ricerca Tag Ticket"

#. module: helpdesk_mgmt
#: model:ir.actions.act_window,name:helpdesk_mgmt.helpdesk_ticket_tag_action
#: model:ir.ui.menu,name:helpdesk_mgmt.helpdesk_ticket_tag_menu
msgid "Ticket Tags"
msgstr "Tag Ticket"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_res_partner__helpdesk_ticket_active_count
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_res_users__helpdesk_ticket_active_count
msgid "Ticket active count"
msgstr "Conteggio ticket attivi"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_res_partner__helpdesk_ticket_count
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_res_users__helpdesk_ticket_count
msgid "Ticket count"
msgstr "Conteggio ticket"

#. module: helpdesk_mgmt
#: model:mail.message.subtype,description:helpdesk_mgmt.hlp_tck_created
msgid "Ticket created"
msgstr "Ticket creato"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__number
msgid "Ticket number"
msgstr "Numero Ticket"

#. module: helpdesk_mgmt
#: model:ir.actions.act_window,name:helpdesk_mgmt.helpdesk_ticket_action
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__ticket_ids
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_res_partner__helpdesk_ticket_count_string
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_res_users__helpdesk_ticket_count_string
#: model:ir.ui.menu,name:helpdesk_mgmt.helpdesk_ticket_menu
#: model:ir.ui.menu,name:helpdesk_mgmt.helpdesk_ticket_reporting_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_layout
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_my_home
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_my_tickets
msgid "Tickets"
msgstr "Ticket"

#. module: helpdesk_mgmt
#. odoo-python
#: code:addons/helpdesk_mgmt/controllers/myaccount.py:0
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__name
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.portal_ticket_list
#, python-format
msgid "Title"
msgstr "Titolo"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_kanban_view
msgid "To Do"
msgstr "Da fare"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
msgid "Today Activities"
msgstr "Attività odierne"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_kanban_view
msgid "Toggle dropdown"
msgstr "Attiva menu a tendina"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo di attività eccezione sul record."

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_kanban_view
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
msgid "Unassigned"
msgstr "Non assegnato"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_team_tree
msgid "Unassigned tickets"
msgstr "Ticket non assegnati"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__unattended
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_stage__unattended
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_kanban_view
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
msgid "Unattended"
msgstr "Non gestito"

#. module: helpdesk_mgmt
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_team_tree
msgid "Unattended tickets"
msgstr "Ticket non gestiti"

#. module: helpdesk_mgmt
#: model:ir.model,name:helpdesk_mgmt.model_res_users
#: model:res.groups,name:helpdesk_mgmt.group_helpdesk_user
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.helpdesk_ticket_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk_mgmt.view_helpdesk_ticket_team_search
msgid "User"
msgstr "Utente"

#. module: helpdesk_mgmt
#: model:res.groups,name:helpdesk_mgmt.group_helpdesk_user_own
msgid "User: Personal tickets"
msgstr "Utente: Ticket Personali"

#. module: helpdesk_mgmt
#: model:res.groups,name:helpdesk_mgmt.group_helpdesk_user_team
msgid "User: Team tickets"
msgstr "Utente: ticket squadra"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__user_ids
msgid "Users"
msgstr "Utenti"

#. module: helpdesk_mgmt
#: model:ir.model.fields.selection,name:helpdesk_mgmt.selection__helpdesk_ticket__priority__3
msgid "Very High"
msgstr "Molto alta"

#. module: helpdesk_mgmt
#: model:helpdesk.ticket.channel,name:helpdesk_mgmt.helpdesk_ticket_channel_web
msgid "Web"
msgstr "Web"

#. module: helpdesk_mgmt
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket__website_message_ids
#: model:ir.model.fields,field_description:helpdesk_mgmt.field_helpdesk_ticket_team__website_message_ids
msgid "Website Messages"
msgstr "Messaggi sito web"

#. module: helpdesk_mgmt
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket__website_message_ids
#: model:ir.model.fields,help:helpdesk_mgmt.field_helpdesk_ticket_team__website_message_ids
msgid "Website communication history"
msgstr "Storico comunicazioni sito web"

#. module: helpdesk_mgmt
#: model:helpdesk.ticket.category,name:helpdesk_mgmt.helpdesk_category_2
msgid "Wifi"
msgstr "Wifi"

#. module: helpdesk_mgmt
#: model:mail.template,subject:helpdesk_mgmt.assignment_email_template
msgid ""
"{{object.company_id.name}} Ticket Assignment (Ref {{object.number or 'n/"
"a' }})"
msgstr ""
"{{object.company_id.name}} Assegnazione ticket (Rif. {{object.number or 'n/"
"a' }})"

#~ msgid "SMS Delivery error"
#~ msgstr "Errore consegna SMS"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Numero di messaggi che richiedono un'azione"

#~ msgid "List of cc from incoming emails."
#~ msgstr "Lista dei cc dalle email in entrata."

#~ msgid "Number of unread messages"
#~ msgstr "Numero di messaggi non letti"

#~ msgid "Unread Messages"
#~ msgstr "Messaggi non letti"

#~ msgid "Unread Messages Counter"
#~ msgstr "Contatore messaggi non letti"

#~ msgid ""
#~ "<br/>\n"
#~ "                                    <b>Category:</b>"
#~ msgstr ""
#~ "<br/>\n"
#~ "                                    <b>Categoria:</b>"

#~ msgid ""
#~ "<br/>\n"
#~ "                                    <b>Stage:</b>"
#~ msgstr ""
#~ "<br/>\n"
#~ "                                    <b>Fase:</b>"

#~ msgid "History"
#~ msgstr "Storico"

#~ msgid "There are no tickets in your account."
#~ msgstr "Non ci sono ticket nel tuo account."

#~ msgid ""
#~ "${object.company_id.name} Ticket Assignment (Ref ${object.number or 'n/"
#~ "a' })"
#~ msgstr ""
#~ "${object.company_id.name} Assegnazione Ticket (Ref ${object.number or 'n/"
#~ "a' })"

#~ msgid ""
#~ "<?xml version=\"1.0\"?>\n"
#~ "<data><p>Hello ${object.user_id.name},</p>\n"
#~ "                <p>The ticket ${object.number} has been assigned to you.</"
#~ "p>\n"
#~ "            </data>"
#~ msgstr ""
#~ "<?xml version=\"1.0\"?>\n"
#~ "<data><p>Ciao ${object.user_id.name},</p>\n"
#~ "                <p>Ti è stato assegnato il ticket ${object.number}.</p>\n"
#~ "            </data>"

#~ msgid "Followers (Channels)"
#~ msgstr "Followers (Canali)"

#~ msgid "The ticket ${object.number} has been closed."
#~ msgstr "Il ticket ${object.number} è stato chiuso."

#~ msgid "The ticket ${object.number} stage has changed."
#~ msgstr "La fase del ticket ${object.number} è cambiata."

#~ msgid "Ticket Stages"
#~ msgstr "Fasi Ticket"
