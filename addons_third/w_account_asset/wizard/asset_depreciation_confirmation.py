# -*- coding: utf-8 -*-
from datetime import datetime, date
from dateutil.relativedelta import relativedelta

from odoo import api, fields, models, _
from odoo.exceptions import UserError

_YEARS = [('%s' % x, '%s' % x) for x in range(2000, 2050)]


class AssetDepreciationConfirmationWizard(models.TransientModel):
    _name = 'asset.depreciation.confirmation.wizard'

    month = fields.Selection([
        ('1', '1'),
        ('2', '2'),
        ('3', '3'),
        ('4', '4'),
        ('5', '5'),
        ('6', '6'),
        ('7', '7'),
        ('8', '8'),
        ('9', '9'),
        ('10', '10'),
        ('11', '11'),
        ('12', '12'),
    ], 'Tháng', required=1)
    year = fields.Selection(_YEARS, 'Năm', required=1)
    asset_type = fields.Selection(
        [('sale', '<PERSON><PERSON> hàng: <PERSON><PERSON> nhận doanh thu'), ('purchase', 'Mua: <PERSON><PERSON><PERSON> sản'), ('expense', 'Deferred Expense')])

    @api.model
    def default_get(self, fields_list):
        res = super(AssetDepreciationConfirmationWizard, self).default_get(fields_list)
        today = date.today()
        res['month'] = str(today.month)
        res['year'] = str(today.year)
        return res

    def asset_compute(self):
        sql = f'''SELECT am.ID
                    FROM
                        account_asset AS aas
                        LEFT JOIN account_move am ON aas.ID = am.asset_id 
                    WHERE
                        aas.STATE = 'open' 
                        AND aas.asset_type = '{self.asset_type}' 
                        AND am.STATE = 'posted' 
                        AND DATE_PART( 'month', am.DATE :: DATE ) = '{self.month}' 
                        AND DATE_PART( 'year', am.DATE :: DATE ) = '{self.year}' 
                        AND am.auto_post = 'no' '''
        self._cr.execute(sql)
        recs = self._cr.fetchall()
        list = []
        for r in recs :
            list.append(r[0])
        return {
            'name': _('Journal Entries'),
            'view_mode': 'tree,form',
            'res_model': 'account.move',
            'views': [(self.env.ref('account.view_move_tree').id, 'tree'), (self.env.ref('account.view_move_form').id, 'form')],
            'type': 'ir.actions.act_window',
            'domain': [('id', 'in', list)],
            'context': dict(self._context, create=False),
        }
