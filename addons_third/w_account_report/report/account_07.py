# -*- coding: utf-8 -*-
from datetime import date
from odoo import models, fields, api, _
from odoo.modules.module import get_resource_path
from odoo.exceptions import ValidationError
from odoo.addons.report_xlsx.models.xlsx_utils import XlsxUtils
from odoo.osv import expression

_current_column = 0


def next_column(column=None, jump_next=0):
    global _current_column
    if column is not None:
        _current_column = 0 + column
        return _current_column
    _current_column += 1
    if jump_next:
        _current_column += jump_next
    return 0 + _current_column


class ReportAccount07(models.TransientModel):
    _name = 'report.account.07'
    _inherit = 'wizard.preview.xlsx'
    _description = 'Sổ chi tiết tài khoản'

    name = fields.Char(readonly=1)
    report_name = fields.Char()
    report_07_type = fields.Selection([('inbound', 'Inbound'), ('outbound', 'outbound')])
    preview_xml = fields.Char(readonly=1)
    date_from = fields.Date('Từ ngày', default=date.today().replace(day=1))
    date_to = fields.Date('Đến ngày', default=date.today())
    account_id = fields.Many2one('account.account', string='Tài khoản', required=True)
    partners = fields.Many2many('res.partner', 'rp_07_partner_rel', 'report_id', 'parnter_id', string='Đối tượng')
    account_ids = fields.Many2many('account.account', compute='_get_account_ids')

    @api.depends('report_07_type')
    def _get_account_ids(self):
        '''
        Lấy list các tài khoản có thể chọn tuỳ theo loại báo cáo
        '''
        debit_account_ids = self.env['account.account'].browse()
        # phải thu
        if self.report_07_type == 'inbound':
            debit_account_ids = self.env['account.account'].with_context(show_parent_account=True).search([
                ('company_id', '=', self.env.company.id),
                ('debit_account', '=', True),
                ('account_type', '=', 'asset_receivable')
            ])
        # phải trả
        elif self.report_07_type == 'outbound':
            debit_account_ids = self.env['account.account'].with_context(show_parent_account=True).search([
                ('company_id', '=', self.env.company.id),
                ('debit_account', '=', True),
                ('account_type', '=', 'liability_payable')
            ])
        account_ids = debit_account_ids.mapped('parent_id')
        debit_account_ids = debit_account_ids + account_ids

        if not debit_account_ids:
            raise ValidationError('Chưa cấu hình tài khoản công nợ!')
        self.account_ids = debit_account_ids.ids

    @api.model
    def default_get(self, fields):
        result = super().default_get(fields)
        if self._context.get('default_report_07_type') == 'inbound':
            result['name'] = 'Sổ chi tiết công nợ phải thu'
        elif self._context.get('default_report_07_type') == 'outbound':
            result['name'] = 'Sổ chi tiết công nợ phải trả'
        else:
            result['name'] = 'Sổ chi tiết tài khoản'
        return result

    @api.constrains('date_from', 'date_to')
    def check_from_date_to_date(self):
        if self.date_from and self.date_to and self.date_from > self.date_to:
            raise ValidationError('Khoảng thời gian lên báo cáo không đúng. Xin hãy nhập lại')

    def get_open_balance(self):
        '''
        lấy số đầu kỳ cho báo cáo
        '''
        sql = """
        select d.id, d.ref, d.name, sum(d.opening) opening
        from (
            select rp.id, rp.ref, rp.name, sum(aml.debit) - sum(aml.credit) opening
            from account_move am
                inner join account_move_line aml on am.id=aml.move_id
                inner join account_account aa on aml.account_id=aa.id
                inner join res_partner rp on aml.partner_id=rp.id
            where true
                and aa.parent_path like '{parent_path}%'
                and am.state = 'posted'
                and am.date < '{date}'
                {where_partner}
            group by rp.id, am.id
            having sum(aml.debit) - sum(aml.credit) <> 0
            order by rp.name asc, am.date asc
        ) d
        group by d.id, d.ref, d.name
        having sum(d.opening) <>0
        """
        where_partner = ''
        if self.partners:
            _ids = [0, 0] + self.partners.ids
            where_partner = 'and rp.id in (%s)' % ','.join(str(x) for x in _ids)

        self._cr.execute(sql.format(
            where_partner=where_partner,
            parent_path=self.account_id.parent_path,
            date=self.date_from.strftime('%Y-%m-%d'),
        ))
        return {x['id']: {**x, **{'items': []}} for x in self._cr.dictfetchall()}

    def get_data(self):
        '''
        Lấy dữ liệu phát sinh trong kỳ cho báo cáo
        '''
        sql = """
        select rp.id, rp.ref, rp.name, am.date as date, am.date_document as date_document, am.name move_name,
            case when aml2.name is not null then aml2.name else am.ref end move_ref,
            aa.code acc1, aa2.code acc2, least(abs(aml.balance), abs(aml2.balance)) amount,
            case when aml.balance > 0 then 'debit' else 'credit' end as side,
            case when (aml2.debit2 < 0 or aml2.credit2 < 0) then -1 else 1 end as sign
        from account_move am
            inner join account_move_line aml on am.id=aml.move_id
            inner join account_account aa on aml.account_id=aa.id
            inner join res_partner rp on aml.partner_id=rp.id
            inner join account_move_line aml2 on am.id=aml2.move_id and aml2.group=aml.group and aml2.id<>aml.id
            inner join account_account aa2 on aml2.account_id=aa2.id
        where true
            and aa.parent_path like '{parent_path}%'
            and aa2.parent_path not like '{parent_path}%'
            and am.state = 'posted'
            and am.date between '{df}' and '{dt}'
            and (coalesce(aml2.debit2, 0) >= 0 and coalesce(aml2.credit2, 0) >= 0)
            {where_partner}
            and am.move_type <> 'entry'
        group by rp.id, am.id, aa.id, aa2.id, aml.id, aml2.id
        having sum(aml.debit) - sum(aml.credit) <> 0
    
		union all
		
        select rp.id, rp.ref, rp.name, am.date as date, am.date_document as date_document, am.name move_name,
            case when aml2.name is not null then aml2.name else am.ref end move_ref,
            aa.code acc1, aa2.code acc2, least(abs(aml.balance), abs(aml2.balance)) amount,
            case when aml.balance > 0 then 'debit' else 'credit' end as side,
            case when (aml2.debit < 0 or aml2.credit < 0) then -1 else 1 end as sign
        from account_move am
            inner join account_move_line aml on am.id=aml.move_id
            inner join account_account aa on aml.account_id=aa.id
            inner join res_partner rp on aml.partner_id=rp.id
            inner join account_move_line aml2 on am.id=aml2.move_id and aml2.group=aml.group and aml2.id<>aml.id
            inner join account_account aa2 on aml2.account_id=aa2.id
        where true
            and aa.parent_path like '{parent_path}%'
            and aa2.parent_path not like '{parent_path}%'
            and am.state = 'posted'
            and am.date between '{df}' and '{dt}'
            and (
				(coalesce(aml.debit, 0) > 0 and coalesce(aml2.credit, 0) > 0)
				or
				(coalesce(aml2.debit, 0) > 0 and coalesce(aml.credit, 0) > 0)
			)
            {where_partner}
			and am.move_type = 'entry'
        group by rp.id, am.id, aa.id, aa2.id, aml.id, aml2.id
        having sum(aml.debit) - sum(aml.credit) <> 0
        """
        where_partner = ''
        if self.partners:
            _ids = [0, 0] + self.partners.ids
            where_partner = 'and rp.id in (%s)' % ','.join(str(x) for x in _ids)

        self._cr.execute(sql.format(
            where_partner=where_partner,
            parent_path=self.account_id.parent_path,
            df=self.date_from.strftime('%Y-%m-%d'),
            dt=self.date_to.strftime('%Y-%m-%d'),
        ))
        data = self._cr.dictfetchall()
        res = self.get_open_balance()
        for i in data:
            _id = i['id']
            if _id not in res:
                res[_id] = {'opening': 0, 'id': _id, 'ref': i['ref'], 'name': i['name'], 'items': []}
            if 'items' not in res[_id]:
                res[_id]['items'] = []

            res[_id]['items'].append(i)

        return res

    def generate_xlsx(self, wb, data):
        '''
        Đọc file mẫu excel
        đổ dữ liệu vào
        '''
        self.ensure_one()
        company = self.env.user.company_id
        ws = wb.add_worksheet(u'Báo cáo')
        utils = XlsxUtils(get_resource_path('w_account_report', 'templates', 'report_account_07.xls'), wb)
        utils.load_range_format(0, (0, 0, 7, 12))
        utils.copy_range(0, ws, (0, 0, 4, 12), 0)

        if self._context.get('default_report_07_type') == 'inbound' or self.report_07_type == 'inbound':
            utils.write(ws, (0, 0), 'SỔ CHI TIẾT CÔNG NỢ PHẢI THU')
        elif self._context.get('default_report_07_type') == 'outbound' or self.report_07_type == 'outbound':
            utils.write(ws, (0, 0), 'SỔ CHI TIẾT CÔNG NỢ PHẢI TRẢ')
            utils.write(ws, (3, 1), 'Mã NCC')
            utils.write(ws, (3, 2), 'Tên NCC')
        utils.write(ws, (1, 0), 'Từ ngày %s đến ngày %s' % (
            self.date_from.strftime('%d/%m/%Y'),
            self.date_to.strftime('%d/%m/%Y'),
        ))

        def get_date(_d):
            if not _d or not isinstance(_d, date):
                return ''
            return _d.strftime('%d/%m/%Y')

        data = self.get_data()
        _row = 5
        stt = 0
        for ind in data:
            i = data[ind]
            stt += 1
            row_pin = 0 + _row    # dòng điền tổng theo từng partner
            utils.write(ws, (_row, next_column(0)), stt, _origin_cell_axis=(5, next_column(jump_next=-1)))
            utils.write_merge(ws, (_row, 1, _row, 6), i['name'], _origin_cell_axis=(5, 1))
            utils.write(ws, (_row, next_column(7)), '', _origin_cell_axis=(5, next_column(jump_next=-1)))
            utils.write(ws, (_row, next_column(8)), '', _origin_cell_axis=(5, next_column(jump_next=-1)))

            _row += 1

            open_debit = i['opening'] if i['opening'] > 0 else 0
            open_credit = -i['opening'] if i['opening'] < 0 else 0
            balance = i['opening']
            utils.write(ws, (_row, next_column(0)), '', _origin_cell_axis=(6, next_column(jump_next=-1)))
            for col in range(1, 13):
                utils.write(ws, (_row, next_column()), '', _origin_cell_axis=(6, next_column(jump_next=-1)))
            utils.write(ws, (_row, 4), 'Số dư đầu kỳ', _origin_cell_axis=(6, 4))
            utils.write(ws, (_row, 11), open_debit, _origin_cell_axis=(6, 11))
            utils.write(ws, (_row, 12), open_credit, _origin_cell_axis=(6, 12))

            sum_debit, sum_credit = 0, 0
            for item in i['items']:
                _row += 1
                debit = item['amount'] if item['side'] == 'debit' else 0
                credit = item['amount'] if item['side'] == 'credit' else 0
                debit *= item['sign']
                credit *= item['sign']
                if debit < 0:
                    credit = -debit
                    debit = 0
                if credit < 0:
                    debit = -credit
                    credit = 0
                sum_debit += debit
                sum_credit += credit
                balance += debit - credit
                balance_debit = balance if balance > 0 else 0
                balance_credit = -balance if balance < 0 else 0
                utils.write(ws, (_row, next_column(0)), '', _origin_cell_axis=(7, next_column(jump_next=-1)))
                utils.write(ws, (_row, next_column()), i['ref'] or '', _origin_cell_axis=(7, next_column(jump_next=-1)))
                utils.write(ws, (_row, next_column()), i['name'] or '', _origin_cell_axis=(7, next_column(jump_next=-1)))
                utils.write(ws, (_row, next_column()), get_date(item['date']), _origin_cell_axis=(7, next_column(jump_next=-1)))
                utils.write(ws, (_row, next_column()), get_date(item['date_document']), _origin_cell_axis=(7, next_column(jump_next=-1)))
                utils.write(ws, (_row, next_column()), item['move_name'] or '', _origin_cell_axis=(7, next_column(jump_next=-1)))
                utils.write(ws, (_row, next_column()), item['move_ref'] or '', _origin_cell_axis=(7, next_column(jump_next=-1)))
                utils.write(ws, (_row, next_column()), item['acc1'] or '', _origin_cell_axis=(7, next_column(jump_next=-1)))
                utils.write(ws, (_row, next_column()), item['acc2'] or '', _origin_cell_axis=(7, next_column(jump_next=-1)))
                utils.write(ws, (_row, next_column()), debit, _origin_cell_axis=(7, next_column(jump_next=-1)))
                utils.write(ws, (_row, next_column()), credit, _origin_cell_axis=(7, next_column(jump_next=-1)))

                utils.write(ws, (_row, next_column()), balance_debit, _origin_cell_axis=(7, next_column(jump_next=-1)))
                utils.write(ws, (_row, next_column()), balance_credit, _origin_cell_axis=(7, next_column(jump_next=-1)))

            utils.write(ws, (row_pin, 9), sum_debit, _origin_cell_axis=(5, 9))
            utils.write(ws, (row_pin, 10), sum_credit, _origin_cell_axis=(5, 10))
            utils.write(ws, (row_pin, 11), 0, _origin_cell_axis=(5, 11))
            utils.write(ws, (row_pin, 12), 0, _origin_cell_axis=(5, 12))

            _row += 1

    def button_preview(self):
        path = self.preview_xlsx_report('w_account_report.report_account_07', self.id)
        result_html = self.convert_xlsx_html(path)
        self.preview_xml = self.convert_xlsx_html_sticky_header(result_html, 4, space_first_row=16) if path else None

    def button_download(self):
        # self.button_preview()
        self.report_name = self.name
        return self.env.ref('w_account_report.report_report_account_07_xlsx').report_action(self)


class ReportAccount07Xlsx(models.AbstractModel):
    _name = 'report.w_account_report.report_account_07'
    _inherit = 'report.report_xlsx.abstract'

    def generate_xlsx_report(self, workbook, data, objects):
        if self._context.get('preview_data'):
            if objects.report_07_type == 'outbound':
                objects.with_context(default_report_07_type='outbound').generate_xlsx(workbook, data)
            elif objects.report_07_type == 'inbound':
                objects.with_context(default_report_07_type='inbound').generate_xlsx(workbook, data)
            else:
                objects.generate_xlsx(workbook, data)
        else:
            if objects.report_07_type == 'outbound':
                objects.with_context(default_report_07_type='outbound').with_context({'export_excel': True}).generate_xlsx(workbook, data)
            elif objects.report_07_type == 'inbound':
                objects.with_context(default_report_07_type='inbound').with_context({'export_excel': True}).generate_xlsx(workbook, data)
            else:
                objects.with_context({'export_excel': True}).generate_xlsx(workbook, data)
